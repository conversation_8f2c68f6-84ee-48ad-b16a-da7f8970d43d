<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreProduitVarianteRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'produit_id' => 'required|exists:produits,id',
            'sku' => 'required|string|max:100|unique:produit_variantes,sku',
            'prix_supplement' => 'required|numeric|min:0',
            'stock' => 'required|integer|min:0',
            'actif' => 'nullable|boolean',
            'valeurs' => 'required|array',
            'valeurs.*.attribut_id' => 'required|exists:attributs,id',
            'valeurs.*.valeur' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'produit_id.required' => 'Le champ produit_id est obligatoire.',
            'produit_id.exists' => 'Le produit sélectionné est invalide.',
            'sku.required' => 'Le champ sku est obligatoire.',
            'sku.unique' => 'Le sku doit être unique.',
            'prix_supplement.required' => 'Le prix supplémentaire est obligatoire.',
            'stock.required' => 'Le stock est obligatoire.',
            'valeurs.required' => 'Les valeurs d\'attributs sont obligatoires.',
            'valeurs.*.attribut_id.required' => 'L\'attribut est obligatoire.',
            'valeurs.*.attribut_id.exists' => 'L\'attribut sélectionné est invalide.',
            'valeurs.*.valeur.required' => 'La valeur de l\'attribut est obligatoire.',
        ];
    }
} 