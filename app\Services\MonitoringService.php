<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

class MonitoringService
{
    /**
     * Collect system health metrics
     */
    public function getSystemHealth(): array
    {
        return [
            'database' => $this->checkDatabaseHealth(),
            'cache' => $this->checkCacheHealth(),
            'queue' => $this->checkQueueHealth(),
            'storage' => $this->checkStorageHealth(),
            'redis' => $this->checkRedisHealth(),
            'memory' => $this->getMemoryUsage(),
            'performance' => $this->getPerformanceMetrics(),
            'security' => $this->getSecurityMetrics(),
        ];
    }

    /**
     * Check database health
     */
    private function checkDatabaseHealth(): array
    {
        try {
            $start = microtime(true);
            DB::select('SELECT 1');
            $responseTime = (microtime(true) - $start) * 1000;

            // Check slow queries
            $slowQueries = $this->getSlowQueriesCount();

            // Check connection count
            $connections = $this->getDatabaseConnections();

            return [
                'status' => 'healthy',
                'response_time_ms' => round($responseTime, 2),
                'slow_queries_last_hour' => $slowQueries,
                'active_connections' => $connections,
                'connection_limit' => 100, // Default MySQL limit
                'connection_usage_percent' => round(($connections / 100) * 100, 2)
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
                'response_time_ms' => null
            ];
        }
    }

    /**
     * Check cache health
     */
    private function checkCacheHealth(): array
    {
        try {
            $testKey = 'health_check_' . time();
            $testValue = 'test_value';

            $start = microtime(true);
            Cache::put($testKey, $testValue, 60);
            $retrieved = Cache::get($testKey);
            Cache::forget($testKey);
            $responseTime = (microtime(true) - $start) * 1000;

            $status = ($retrieved === $testValue) ? 'healthy' : 'unhealthy';

            return [
                'status' => $status,
                'response_time_ms' => round($responseTime, 2),
                'driver' => config('cache.default'),
                'hit_rate' => $this->getCacheHitRate()
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Check queue health
     */
    private function checkQueueHealth(): array
    {
        try {
            $metrics = [];
            $queues = ['default', 'emails', 'images', 'search', 'payments'];

            foreach ($queues as $queue) {
                $size = $this->getQueueSize($queue);
                $failed = $this->getFailedJobsCount($queue);

                $metrics[$queue] = [
                    'pending_jobs' => $size,
                    'failed_jobs' => $failed,
                    'status' => $size < 1000 ? 'healthy' : 'warning'
                ];
            }

            return [
                'status' => 'healthy',
                'queues' => $metrics,
                'workers_active' => $this->getActiveWorkersCount()
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Check storage health
     */
    private function checkStorageHealth(): array
    {
        try {
            $disks = ['local', 'public'];
            $metrics = [];

            foreach ($disks as $disk) {
                $path = storage_path();
                if ($disk === 'public') {
                    $path = storage_path('app/public');
                }

                $freeSpace = disk_free_space($path);
                $totalSpace = disk_total_space($path);
                $usedSpace = $totalSpace - $freeSpace;
                $usagePercent = round(($usedSpace / $totalSpace) * 100, 2);

                $metrics[$disk] = [
                    'free_space_gb' => round($freeSpace / 1024 / 1024 / 1024, 2),
                    'total_space_gb' => round($totalSpace / 1024 / 1024 / 1024, 2),
                    'usage_percent' => $usagePercent,
                    'status' => $usagePercent < 85 ? 'healthy' : 'warning'
                ];
            }

            return [
                'status' => 'healthy',
                'disks' => $metrics
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Check Redis health
     */
    private function checkRedisHealth(): array
    {
        try {
            $start = microtime(true);
            Redis::ping();
            $responseTime = (microtime(true) - $start) * 1000;

            $info = Redis::info();
            $memoryUsage = $info['used_memory_human'] ?? 'unknown';
            $connectedClients = $info['connected_clients'] ?? 0;

            return [
                'status' => 'healthy',
                'response_time_ms' => round($responseTime, 2),
                'memory_usage' => $memoryUsage,
                'connected_clients' => $connectedClients
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get memory usage
     */
    private function getMemoryUsage(): array
    {
        return [
            'memory_usage_mb' => round(memory_get_usage(true) / 1024 / 1024, 2),
            'memory_peak_mb' => round(memory_get_peak_usage(true) / 1024 / 1024, 2),
            'memory_limit' => ini_get('memory_limit')
        ];
    }

    /**
     * Get performance metrics
     */
    private function getPerformanceMetrics(): array
    {
        return [
            'average_response_time' => $this->getAverageResponseTime(),
            'slow_requests_last_hour' => $this->getSlowRequestsCount(),
            'error_rate_last_hour' => $this->getErrorRate(),
            'requests_per_minute' => $this->getRequestsPerMinute()
        ];
    }

    /**
     * Get security metrics
     */
    private function getSecurityMetrics(): array
    {
        return [
            'blocked_ips_last_hour' => $this->getBlockedIpsCount(),
            'security_events_last_hour' => $this->getSecurityEventsCount(),
            'failed_auth_attempts' => $this->getFailedAuthAttempts(),
            'suspicious_activities' => $this->getSuspiciousActivities()
        ];
    }

    /**
     * Get slow queries count
     */
    private function getSlowQueriesCount(): int
    {
        try {
            // This would need to be implemented based on your database logs
            // For now, return a placeholder
            return 0;
        } catch (\Exception $e) {
            return -1;
        }
    }

    /**
     * Get database connections
     */
    private function getDatabaseConnections(): int
    {
        try {
            $result = DB::select("SHOW STATUS WHERE variable_name = 'Threads_connected'");
            return $result[0]->Value ?? 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get cache hit rate
     */
    private function getCacheHitRate(): float
    {
        try {
            // This would need implementation based on cache driver
            // For Redis, you could use INFO stats
            return 95.5; // Placeholder
        } catch (\Exception $e) {
            return 0.0;
        }
    }

    /**
     * Get queue size
     */
    private function getQueueSize(string $queue): int
    {
        try {
            return Redis::llen("queues:{$queue}");
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get failed jobs count
     */
    private function getFailedJobsCount(string $queue): int
    {
        try {
            return DB::table('failed_jobs')
                ->where('queue', $queue)
                ->where('failed_at', '>', now()->subHour())
                ->count();
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get active workers count
     */
    private function getActiveWorkersCount(): int
    {
        try {
            // This would need implementation based on your queue monitoring
            return 3; // Placeholder
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get average response time
     */
    private function getAverageResponseTime(): float
    {
        $cacheKey = 'metrics:avg_response_time';
        return Cache::get($cacheKey, 0.0);
    }

    /**
     * Get slow requests count
     */
    private function getSlowRequestsCount(): int
    {
        $cacheKey = 'metrics:slow_requests_count';
        return Cache::get($cacheKey, 0);
    }

    /**
     * Get error rate
     */
    private function getErrorRate(): float
    {
        $cacheKey = 'metrics:error_rate';
        return Cache::get($cacheKey, 0.0);
    }

    /**
     * Get requests per minute
     */
    private function getRequestsPerMinute(): int
    {
        $cacheKey = 'metrics:requests_per_minute';
        return Cache::get($cacheKey, 0);
    }

    /**
     * Get blocked IPs count
     */
    private function getBlockedIpsCount(): int
    {
        $cacheKey = 'security:blocked_ips_count';
        return Cache::get($cacheKey, 0);
    }

    /**
     * Get security events count
     */
    private function getSecurityEventsCount(): int
    {
        $cacheKey = 'security:events_count';
        return Cache::get($cacheKey, 0);
    }

    /**
     * Get failed auth attempts
     */
    private function getFailedAuthAttempts(): int
    {
        $cacheKey = 'security:failed_auth_count';
        return Cache::get($cacheKey, 0);
    }

    /**
     * Get suspicious activities
     */
    private function getSuspiciousActivities(): int
    {
        $cacheKey = 'security:suspicious_activities';
        return Cache::get($cacheKey, 0);
    }

    /**
     * Update performance metrics
     */
    public function updatePerformanceMetrics(float $responseTime, bool $isError = false): void
    {
        // Update average response time
        $this->updateAverageResponseTime($responseTime);

        // Update slow requests count
        if ($responseTime > 2000) { // 2 seconds
            $this->incrementSlowRequestsCount();
        }

        // Update error rate
        if ($isError) {
            $this->incrementErrorCount();
        }

        // Update requests per minute
        $this->incrementRequestsCount();
    }

    /**
     * Update average response time
     */
    private function updateAverageResponseTime(float $responseTime): void
    {
        $cacheKey = 'metrics:avg_response_time';
        $currentAvg = Cache::get($cacheKey, 0.0);
        $requestsKey = 'metrics:total_requests';
        $totalRequests = Cache::get($requestsKey, 0);

        $newAvg = (($currentAvg * $totalRequests) + $responseTime) / ($totalRequests + 1);

        Cache::put($cacheKey, $newAvg, 3600);
        Cache::put($requestsKey, $totalRequests + 1, 3600);
    }

    /**
     * Increment slow requests count
     */
    private function incrementSlowRequestsCount(): void
    {
        $cacheKey = 'metrics:slow_requests_count';
        $current = Cache::get($cacheKey, 0);
        Cache::put($cacheKey, $current + 1, 3600);
    }

    /**
     * Increment error count
     */
    private function incrementErrorCount(): void
    {
        $errorKey = 'metrics:error_count';
        $totalKey = 'metrics:total_requests_for_rate';

        $errorCount = Cache::get($errorKey, 0);
        $totalCount = Cache::get($totalKey, 0);

        Cache::put($errorKey, $errorCount + 1, 3600);
        Cache::put($totalKey, $totalCount + 1, 3600);

        // Calculate error rate
        $errorRate = $totalCount > 0 ? ($errorCount / $totalCount) * 100 : 0;
        Cache::put('metrics:error_rate', $errorRate, 3600);
    }

    /**
     * Increment requests count
     */
    private function incrementRequestsCount(): void
    {
        $minute = now()->format('Y-m-d H:i');
        $cacheKey = "metrics:requests_minute:{$minute}";

        $current = Cache::get($cacheKey, 0);
        Cache::put($cacheKey, $current + 1, 120); // Keep for 2 minutes

        // Update current minute metric
        Cache::put('metrics:requests_per_minute', $current + 1, 60);
    }

    /**
     * Generate health report
     */
    public function generateHealthReport(): array
    {
        $health = $this->getSystemHealth();

        $overallStatus = 'healthy';
        $issues = [];

        foreach ($health as $component => $status) {
            if (is_array($status) && isset($status['status'])) {
                if ($status['status'] === 'unhealthy') {
                    $overallStatus = 'unhealthy';
                    $issues[] = $component;
                } elseif ($status['status'] === 'warning' && $overallStatus === 'healthy') {
                    $overallStatus = 'warning';
                }
            }
        }

        return [
            'overall_status' => $overallStatus,
            'timestamp' => now()->toISOString(),
            'issues' => $issues,
            'components' => $health
        ];
    }
}
