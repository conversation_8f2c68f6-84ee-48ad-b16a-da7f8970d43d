<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AjouterStockRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'quantite' => 'required|integer|min:1',
            'reference' => 'nullable|string|max:255',
            'commentaire' => 'nullable|string|max:1000',
        ];
    }

    public function messages()
    {
        return [
            'quantite.required' => 'La quantité est obligatoire.',
            'quantite.integer' => 'La quantité doit être un nombre entier.',
            'quantite.min' => 'La quantité doit être au moins 1.',
            'reference.max' => 'La référence ne peut pas dépasser 255 caractères.',
            'commentaire.max' => 'Le commentaire ne peut pas dépasser 1000 caractères.',
        ];
    }
} 