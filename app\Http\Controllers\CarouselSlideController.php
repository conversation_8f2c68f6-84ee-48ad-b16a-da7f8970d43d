<?php

namespace App\Http\Controllers;

use App\Models\Carousel;
use App\Models\CarouselSlide;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class CarouselSlideController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'carousel_id' => 'required|exists:carousels,id',
            ]);

            if ($validator->fails()) {
                return response()->json(['errors' => $validator->errors()], 422);
            }

            $query = CarouselSlide::where('carousel_id', $request->input('carousel_id'));

            // Filtrer par statut actif
            if ($request->has('actif')) {
                $query->where('actif', $request->boolean('actif'));
            }

            // Ordonner par ordre
            $query->orderBy('ordre');

            $slides = $query->get();

            return response()->json($slides);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erreur lors de la récupération des slides',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'carousel_id' => 'required|exists:carousels,id',
                'titre' => 'required|string|max:255',
                'description' => 'nullable|string',
                'bouton_texte' => 'nullable|string|max:255',
                'bouton_lien' => 'nullable|string|max:255',
                'ordre' => 'nullable|integer|min:0',
                'actif' => 'nullable|boolean',
            ]);

            if ($validator->fails()) {
                return response()->json(['errors' => $validator->errors()], 422);
            }

            DB::beginTransaction();

            // Vérifier si le carousel existe
            $carousel = Carousel::findOrFail($request->input('carousel_id'));

            $slide = CarouselSlide::create([
                'carousel_id' => $request->input('carousel_id'),
                'titre' => $request->input('titre'),
                'description' => $request->input('description'),
                'bouton_texte' => $request->input('bouton_texte'),
                'bouton_lien' => $request->input('bouton_lien'),
                'ordre' => $request->input('ordre', 0),
                'actif' => $request->input('actif', true),
            ]);

            DB::commit();

            return response()->json($slide, 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'error' => 'Erreur lors de la création du slide',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(string $id)
    {
        try {
            $slide = CarouselSlide::with('images')->findOrFail($id);

            return response()->json($slide);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Slide non trouvé',
                'message' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, string $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'titre' => 'nullable|string|max:255',
                'description' => 'nullable|string',
                'bouton_texte' => 'nullable|string|max:255',
                'bouton_lien' => 'nullable|string|max:255',
                'ordre' => 'nullable|integer|min:0',
                'actif' => 'nullable|boolean',
            ]);

            if ($validator->fails()) {
                return response()->json(['errors' => $validator->errors()], 422);
            }

            DB::beginTransaction();

            $slide = CarouselSlide::findOrFail($id);
            $slide->update($request->all());

            DB::commit();

            return response()->json($slide);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'error' => 'Erreur lors de la mise à jour du slide',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(string $id)
    {
        try {
            DB::beginTransaction();

            $slide = CarouselSlide::findOrFail($id);
            $slide->delete();

            DB::commit();

            return response()->json(null, 204);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'error' => 'Erreur lors de la suppression du slide',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reorder slides.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function reorder(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'carousel_id' => 'required|exists:carousels,id',
                'slides' => 'required|array',
                'slides.*.id' => 'required|exists:carousel_slides,id',
                'slides.*.ordre' => 'required|integer|min:0',
            ]);

            if ($validator->fails()) {
                return response()->json(['errors' => $validator->errors()], 422);
            }

            DB::beginTransaction();

            // Vérifier si le carousel existe
            $carousel = Carousel::findOrFail($request->input('carousel_id'));

            // Mettre à jour l'ordre des slides
            foreach ($request->input('slides') as $slideData) {
                $slide = CarouselSlide::findOrFail($slideData['id']);

                // Vérifier que le slide appartient bien au carousel spécifié
                if ($slide->carousel_id != $request->input('carousel_id')) {
                    return response()->json([
                        'error' => 'Le slide n\'appartient pas au carousel spécifié',
                    ], 422);
                }

                $slide->ordre = $slideData['ordre'];
                $slide->save();
            }

            DB::commit();

            // Récupérer les slides mis à jour
            $slides = CarouselSlide::where('carousel_id', $request->input('carousel_id'))
                ->orderBy('ordre')
                ->get();

            return response()->json($slides);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'error' => 'Erreur lors de la réorganisation des slides',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
