<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Marque extends Model
{
    protected $fillable = [
        'nom_marque',
        'description_marque',
        'logo_marque' // Deprecated - will be removed in favor of images relation
    ];

    public function produits()
    {
        return $this->hasMany(Produit::class);
    }

    /**
     * Get all images for this brand
     */
    public function images(): MorphMany
    {
        return $this->morphMany(Image::class, 'imageable')->orderBy('order');
    }

    /**
     * Get the primary image for this brand
     */
    public function getPrimaryImageAttribute()
    {
        return $this->images()->where('is_primary', true)->first() ?? $this->images()->first();
    }

    /**
     * Get the primary image URL for this brand
     */
    public function getPrimaryImageUrlAttribute()
    {
        if ($this->primaryImage) {
            return $this->primaryImage->url;
        }

        // Fallback to the old logo_marque field if no images are associated
        if ($this->logo_marque) {
            return $this->logo_marque;
        }

        return null;
    }
}
