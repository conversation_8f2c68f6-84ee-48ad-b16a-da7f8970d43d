<?php

namespace App\Http\Controllers;

use App\Models\Carousel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use App\Http\Requests\StoreCarouselRequest;
use App\Http\Requests\UpdateCarouselRequest;

class CarouselController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $query = Carousel::with('slides');

            // Filtrer par statut actif
            if ($request->has('actif')) {
                $query->where('actif', $request->boolean('actif'));
            }

            // Ordonner par ordre
            $query->orderBy('ordre');

            $carousels = $query->get();

            return response()->json($carousels);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erreur lors de la récupération des carousels',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(StoreCarouselRequest $request)
    {
        try {
            $validated = $request->validated();
            DB::beginTransaction();
            $carousel = Carousel::create([
                'nom' => $validated['nom'],
                'description' => $validated['description'] ?? null,
                'actif' => $validated['actif'] ?? true,
                'ordre' => $validated['ordre'] ?? 0,
            ]);
            DB::commit();
            return response()->json($carousel, 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'error' => 'Erreur lors de la création du carousel',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(string $id)
    {
        try {
            $carousel = Carousel::with([
                'slides' => function ($query) {
                    $query->orderBy('ordre');
                }
            ])->findOrFail($id);

            return response()->json($carousel);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Carousel non trouvé',
                'message' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(UpdateCarouselRequest $request, string $id)
    {
        try {
            $validated = $request->validated();
            DB::beginTransaction();
            $carousel = Carousel::findOrFail($id);
            $carousel->update($validated);
            DB::commit();
            return response()->json($carousel);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'error' => 'Erreur lors de la mise à jour du carousel',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(string $id)
    {
        try {
            DB::beginTransaction();

            $carousel = Carousel::findOrFail($id);
            $carousel->delete();

            DB::commit();

            return response()->json(null, 204);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'error' => 'Erreur lors de la suppression du carousel',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get active carousels for frontoffice.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getActiveCarousels()
    {
        try {
            $carousels = Carousel::actif()
                ->with([
                    'slides' => function ($query) {
                        $query->actif()->orderBy('ordre');
                    }
                ])
                ->orderBy('ordre')
                ->get();

            return response()->json($carousels);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erreur lors de la récupération des carousels actifs',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get slides for a specific carousel.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSlides(string $id)
    {
        try {
            $carousel = Carousel::findOrFail($id);

            $slides = $carousel->slides()
                ->with('images')
                ->orderBy('ordre')
                ->get();

            // Add primary image URL to each slide
            $slides->each(function ($slide) {
                $slide->primary_image_url = $slide->primaryImageUrl;
            });

            return response()->json($slides);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erreur lors de la récupération des slides du carousel',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
