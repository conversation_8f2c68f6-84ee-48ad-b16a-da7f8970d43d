<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * Modèle pour les résumés de session de prédiction
 *
 * @property int $id
 * @property float $session_duration
 * @property int $total_predictions
 * @property int $satisfied_count
 * @property int $neutral_count
 * @property int $unsatisfied_count
 * @property float $average_confidence
 * @property string $most_common_prediction
 * @property string|null $session_id
 * @property string|null $user_agent
 * @property string|null $ip_address
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class SessionSummary extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'session_summaries';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'session_duration',
        'total_predictions',
        'satisfied_count',
        'neutral_count',
        'unsatisfied_count',
        'average_confidence',
        'most_common_prediction',
        'session_id',
        'user_agent',
        'ip_address',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'session_duration' => 'float',
        'total_predictions' => 'integer',
        'satisfied_count' => 'integer',
        'neutral_count' => 'integer',
        'unsatisfied_count' => 'integer',
        'average_confidence' => 'float',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Calculer le pourcentage de satisfaction
     */
    public function getSatisfactionPercentageAttribute(): float
    {
        if ($this->total_predictions === 0) {
            return 0;
        }

        return round(($this->satisfied_count / $this->total_predictions) * 100, 2);
    }

    /**
     * Calculer le pourcentage de neutralité
     */
    public function getNeutralPercentageAttribute(): float
    {
        if ($this->total_predictions === 0) {
            return 0;
        }

        return round(($this->neutral_count / $this->total_predictions) * 100, 2);
    }

    /**
     * Calculer le pourcentage d'insatisfaction
     */
    public function getUnsatisfiedPercentageAttribute(): float
    {
        if ($this->total_predictions === 0) {
            return 0;
        }

        return round(($this->unsatisfied_count / $this->total_predictions) * 100, 2);
    }

    /**
     * Scope pour filtrer par période
     */
    public function scopeInPeriod($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope pour filtrer par session
     */
    public function scopeBySession($query, $sessionId)
    {
        return $query->where('session_id', $sessionId);
    }

    /**
     * Scope pour filtrer par prédiction commune
     */
    public function scopeByPrediction($query, $prediction)
    {
        return $query->where('most_common_prediction', $prediction);
    }
}
