<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('paiements', function (Blueprint $table) {
            // Check if columns don't exist before adding them
            if (!Schema::hasColumn('paiements', 'refunded_at')) {
                $table->timestamp('refunded_at')->nullable()->after('processed_at');
            }
            
            // Ensure montant is decimal type if it isn't already
            $table->decimal('montant', 10, 2)->change();
            
            // Ensure status column can handle the enum values
            $table->string('status')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('paiements', function (Blueprint $table) {
            if (Schema::hasColumn('paiements', 'refunded_at')) {
                $table->dropColumn('refunded_at');
            }
        });
    }
};
