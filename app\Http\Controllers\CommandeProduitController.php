<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\CommandeProduit;

class CommandeProduitController extends Controller
{
    public function store(Request $request)
    {try{
        $commandeProduit = new CommandeProduit([
            'commande_id' => $request->input('commande_id'),
            'produit_id' => $request->input('produit_id'),
            'quantite' => $request->input('quantite'),
            'prix_unitaire' => $request->input('prix_unitaire'),
        ]);
        $commandeProduit->save();
        return response()->json($commandeProduit, 201); 
        } catch (\Exception $e) {
        return response()->json(["error"=>"probleme d'insertion {$e->getMessage()}"]);
        }
    }

    public function update(Request $request, $id)
    {
        try{
            $commandeProduit = CommandeProduit::findOrFail($id);
            $commandeProduit->update($request->all());
            return response()->json($commandeProduit, 200);
        } catch (\Exception $e) {
            return response()->json(["error"=>"modification impossible {$e->getMessage()}"]);
        }
    }

    public function destroy($id)
    {   try{
        $commandeProduit = CommandeProduit::findOrFail($id);
        $commandeProduit->delete();
        return response()->json(["message" => "CommandeProduit supprimée avec succès"], 200);
        } catch (\Exception $e) {
            return response()->json(["error"=>"suppression impossible {$e->getMessage()}"]);
        }
    }

    public function show($id)
    {
        try{
            $commandeProduit = CommandeProduit::findOrFail($id);
            return response()->json($commandeProduit);
        } catch (\Exception $e) {
            return response()->json(["error"=>"probleme de récupération des données {$e->getMessage()}"]);
        }
    }
    
}
