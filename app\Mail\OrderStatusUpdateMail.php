<?php

namespace App\Mail;

use App\Models\Commande;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class OrderStatusUpdateMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public Commande $commande;
    public string $newStatus;
    public string $previousStatus;

    /**
     * Create a new message instance.
     */
    public function __construct(Commande $commande, string $newStatus, string $previousStatus)
    {
        $this->commande = $commande;
        $this->newStatus = $newStatus;
        $this->previousStatus = $previousStatus;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Mise à jour du statut de votre commande #' . $this->commande->numero_commande,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            markdown: 'emails.orders.status_update', // Assurez-vous que cette vue existe
            with: [
                'commande' => $this->commande,
                'newStatus' => $this->newStatus,
                'previousStatus' => $this->previousStatus,
                'orderUrl' => route('orders.show', $this->commande->id), // Exemple de lien
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
