<?php

namespace App\Services;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class ValidationService
{
    /**
     * Common validation rules for different data types
     */
    const RULES = [
        'email' => 'required|email:rfc,dns|max:255',
        'password' => 'required|string|min:8|max:255|regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/',
        'phone' => 'nullable|string|max:20|regex:/^[\+]?[0-9\s\-\(\)]+$/',
        'price' => 'required|numeric|min:0|max:999999.99',
        'quantity' => 'required|integer|min:0|max:99999',
        'text_short' => 'required|string|min:1|max:255',
        'text_medium' => 'nullable|string|max:1000',
        'text_long' => 'nullable|string|max:5000',
        'url' => 'nullable|url|max:2000',
        'slug' => 'required|string|max:255|regex:/^[a-z0-9]+(?:-[a-z0-9]+)*$/',
        'date' => 'nullable|date|after_or_equal:today',
        'date_past' => 'nullable|date|before_or_equal:today',
        'image_upload' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120', // 5MB max
        'boolean' => 'boolean',
        'id' => 'required|integer|min:1',
        'optional_id' => 'nullable|integer|min:1',
    ];

    /**
     * Sanitize input data to prevent XSS and other attacks
     */
    public function sanitizeInput(array $data): array
    {
        $sanitized = [];

        foreach ($data as $key => $value) {
            if (is_string($value)) {
                // Remove potentially dangerous characters and scripts
                $value = strip_tags($value);
                $value = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
                $value = trim($value);

                // Additional sanitization for specific patterns
                $value = preg_replace('/[<>"\']/', '', $value);
                $value = preg_replace('/javascript:/i', '', $value);
                $value = preg_replace('/on\w+=/i', '', $value);
            } elseif (is_array($value)) {
                $value = $this->sanitizeInput($value);
            }

            $sanitized[$key] = $value;
        }

        return $sanitized;
    }

    /**
     * Validate product data
     */
    public function validateProduct(Request $request): array
    {
        $rules = [
            'nom_produit' => self::RULES['text_short'],
            'description_produit' => self::RULES['text_long'],
            'prix_produit' => self::RULES['price'],
            'image_produit' => self::RULES['url'],
            'quantite_produit' => self::RULES['quantity'],
            'marque_id' => self::RULES['id'] . '|exists:marques,id',
            'sous_sous_categorie_id' => self::RULES['id'] . '|exists:sous_sous_categories,id',
            'reference' => 'nullable|string|max:100|unique:produits,reference',
        ];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $this->sanitizeInput($validator->validated());
    }

    /**
     * Validate promotion data
     */
    public function validatePromotion(Request $request): array
    {
        $rules = [
            'nom' => self::RULES['text_short'],
            'code' => 'required|string|max:50|unique:promotions,code',
            'description' => self::RULES['text_medium'],
            'type' => 'required|in:pourcentage,montant_fixe,gratuit',
            'valeur' => 'required|numeric|min:0',
            'statut' => 'required|in:active,inactive,brouillon',
            'date_debut' => self::RULES['date'],
            'date_fin' => 'nullable|date|after:date_debut',
            'priorité' => 'nullable|integer|min:0|max:100',
            'cumulable' => self::RULES['boolean'],
            'conditions' => 'nullable|array',
            'event_id' => self::RULES['optional_id'] . '|exists:promotion_events,id',
            'image' => self::RULES['url'],
            'featured' => self::RULES['boolean'],
        ];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $this->sanitizeInput($validator->validated());
    }

    /**
     * Validate user data
     */
    public function validateUser(Request $request, bool $isUpdate = false): array
    {
        $rules = [
            'nom' => self::RULES['text_short'],
            'prenom' => self::RULES['text_short'],
            'email' => self::RULES['email'],
            'telephone' => self::RULES['phone'],
            'adresse' => self::RULES['text_medium'],
            'ville' => self::RULES['text_short'],
            'code_postal' => 'nullable|string|max:10',
            'date_naissance' => self::RULES['date_past'],
        ];

        if (!$isUpdate) {
            $rules['password'] = self::RULES['password'];
            $rules['email'] .= '|unique:users,email';
        } else {
            $rules['password'] = 'nullable|' . self::RULES['password'];
            // For updates, allow same email for current user
            $rules['email'] .= '|unique:users,email,' . ($request->user()->id ?? '');
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $this->sanitizeInput($validator->validated());
    }

    /**
     * Validate cart item data
     */
    public function validateCartItem(Request $request): array
    {
        $rules = [
            'produit_id' => self::RULES['id'] . '|exists:produits,id',
            'variante_id' => self::RULES['optional_id'] . '|exists:produit_variantes,id',
            'quantite' => 'required|integer|min:1|max:999',
        ];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $validator->validated();
    }

    /**
     * Validate order data
     */
    public function validateOrder(Request $request): array
    {
        $rules = [
            'adresse_commande' => self::RULES['text_medium'],
            'ville_commande' => self::RULES['text_short'],
            'code_postal_commande' => 'required|string|max:10',
            'telephone_commande' => self::RULES['phone'],
            'email_commande' => self::RULES['email'],
            'commentaire' => self::RULES['text_medium'],
            'mode_paiement' => 'required|in:carte,virement,especes,paypal',
            'mode_livraison' => 'required|in:domicile,point_relais,magasin',
        ];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $this->sanitizeInput($validator->validated());
    }

    /**
     * Validate file upload
     */
    public function validateFileUpload(Request $request, string $field = 'file'): array
    {
        $rules = [
            $field => self::RULES['image_upload'],
        ];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $validator->validated();
    }

    /**
     * Validate search parameters
     */
    public function validateSearch(Request $request): array
    {
        $rules = [
            'q' => 'required|string|min:2|max:255',
            'page' => 'nullable|integer|min:1|max:1000',
            'per_page' => 'nullable|integer|min:1|max:100',
            'sort_by' => 'nullable|string|in:nom,prix,date,relevance',
            'sort_direction' => 'nullable|in:asc,desc',
            'filters' => 'nullable|array',
        ];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $this->sanitizeInput($validator->validated());
    }

    /**
     * Custom validation rule for strong passwords
     */
    public function isStrongPassword(string $password): bool
    {
        return preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/', $password);
    }

    /**
     * Validate SQL injection patterns
     */
    public function containsSqlInjection(string $input): bool
    {
        $patterns = [
            '/(\bselect\b|\binsert\b|\bupdate\b|\bdelete\b|\bdrop\b|\btruncate\b)/i',
            '/(\bunion\b|\bjoin\b)/i',
            '/(--|;|\/\*|\*\/)/i',
            '/(\bor\b.*=|and.*=)/i',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $input)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Validate XSS patterns
     */
    public function containsXss(string $input): bool
    {
        $patterns = [
            '/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/i',
            '/javascript:/i',
            '/on\w+\s*=/i',
            '/<iframe/i',
            '/<object/i',
            '/<embed/i',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $input)) {
                return true;
            }
        }

        return false;
    }    /**
         * Sanitize search input to prevent XSS and injection attacks
         */
    public function sanitizeSearchInput(string $input): string
    {
        // Remove HTML tags
        $sanitized = strip_tags($input);

        // Remove dangerous characters
        $sanitized = preg_replace('/[<>"\']/', '', $sanitized);

        // Limit length
        $sanitized = substr($sanitized, 0, 100);

        // Trim whitespace
        $sanitized = trim($sanitized);

        return $sanitized;
    }

    /**
     * Validate product filter parameters
     */
    public function validateProductFilter(array $data): array
    {
        $rules = [
            'search' => 'nullable|string|max:255|regex:/^[a-zA-Z0-9\s\-_\u00C0-\u017F]+$/u',
            'page' => 'nullable|integer|min:1|max:1000',
            'per_page' => 'nullable|integer|min:5|max:100',
            'prix_min' => 'nullable|numeric|min:0|max:999999',
            'prix_max' => 'nullable|numeric|min:0|max:999999|gte:prix_min',
            'marque_id' => 'nullable|integer|exists:marques,id',
            'sous_sous_categorie_id' => 'nullable|integer|exists:sous_sous_categories,id',
            'collection_ids' => 'nullable|array',
            'collection_ids.*' => 'integer|exists:collections,id',
            'sort_by' => 'nullable|string|in:nom_produit,prix_produit,created_at,stock',
            'sort_direction' => 'nullable|string|in:asc,desc',
            'with_promotions' => 'nullable|boolean',
            'en_stock' => 'nullable|boolean',
            'actif' => 'nullable|boolean',
            'attributs' => 'nullable|array',
            'attributs.*.attribut_id' => 'required_with:attributs|integer|exists:attributs,id',
            'attributs.*.valeur' => 'required_with:attributs|string|max:255',
        ];

        $messages = [
            'search.regex' => 'Le terme de recherche contient des caractères non autorisés.',
            'prix_max.gte' => 'Le prix maximum doit être supérieur ou égal au prix minimum.',
            'page.max' => 'Le numéro de page ne peut pas dépasser 1000.',
            'per_page.max' => 'Le nombre d\'éléments par page ne peut pas dépasser 100.',
            'per_page.min' => 'Le nombre d\'éléments par page doit être d\'au moins 5.',
            'marque_id.exists' => 'La marque sélectionnée n\'existe pas.',
            'sous_sous_categorie_id.exists' => 'La sous-sous-catégorie sélectionnée n\'existe pas.',
            'collection_ids.*.exists' => 'Une des collections sélectionnées n\'existe pas.',
            'sort_by.in' => 'Le critère de tri doit être l\'un des suivants: nom_produit, prix_produit, created_at, stock.',
            'sort_direction.in' => 'La direction de tri doit être "asc" ou "desc".',
            'attributs.*.attribut_id.exists' => 'Un des attributs sélectionnés n\'existe pas.',
        ];

        $validator = Validator::make($data, $rules, $messages);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        // Sanitize and set defaults
        $validated = $validator->validated();

        // Sanitize search input if present
        if (isset($validated['search'])) {
            $validated['search'] = $this->sanitizeSearchInput($validated['search']);
        }

        // Set defaults
        $validated['page'] = $validated['page'] ?? 1;
        $validated['per_page'] = $validated['per_page'] ?? 12;
        $validated['sort_by'] = $validated['sort_by'] ?? 'created_at';
        $validated['sort_direction'] = $validated['sort_direction'] ?? 'desc';        // Remove null values
        return array_filter($validated, function ($value) {
            return $value !== null && $value !== '';
        });
    }

    /**
     * Validate data with custom rules
     */
    public function validateWithRules(array $data, array $rules, array $messages = []): array
    {
        $validator = Validator::make($data, $rules, $messages);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $validator->validated();
    }
}
