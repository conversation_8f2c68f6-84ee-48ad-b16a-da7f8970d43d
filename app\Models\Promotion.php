<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Promotion extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'nom',
        'code',
        'description',
        'type',
        'valeur',
        'statut',
        'date_debut',
        'date_fin',
        'priorité',
        'cumulable',
        'conditions',
        'event_id',
        'image',
        'featured',
    ];

    protected $casts = [
        'date_debut' => 'datetime',
        'date_fin' => 'datetime',
        'conditions' => 'array',
        'cumulable' => 'boolean',
        'featured' => 'boolean',
    ];

    public function produits()
    {
        return $this->belongsToMany(Produit::class, 'produit_promotion')
            ->withPivot('date_debut', 'date_fin')
            ->withTimestamps();
    }

    public function collections()
    {
        return $this->belongsToMany(Collection::class, 'collection_promotion')
            ->withPivot('date_debut', 'date_fin')
            ->withTimestamps();
    }

    public function profilsRemise()
    {
        return $this->belongsToMany(
            ProfilRemise::class,
            'promotion_profil_remise',
            'promotion_id',
            'profil_remise'
        )->withTimestamps();
    }

    /**
     * Get the event associated with this promotion
     */
    public function event()
    {
        return $this->belongsTo(PromotionEvent::class, 'event_id');
    }

    /**
     * Get the image URL for this promotion
     */
    public function getImageUrlAttribute()
    {
        return $this->image ? $this->image : null;
    }

    /**
     * Get the categories associated with this promotion through products
     */
    public function categories()
    {
        return $this->hasManyThrough(
            Categorie::class,
            Produit::class,
            'id',
            'id',
            'id',
            'sous_sous_categorie_id'
        );
    }

    /**
     * Get the brands associated with this promotion through products
     */
    public function marques()
    {
        return $this->belongsToMany(
            Marque::class,
            'produit_promotion',
            'promotion_id',
            'marque_id'
        )->distinct();
    }

    /**
     * Scope a query to only include active promotions
     */
    public function scopeActive($query)
    {
        $now = now();
        return $query->where('promotions.statut', 'active')
            ->where(function ($q) use ($now) {
                $q->where('promotions.date_debut', '<=', $now)
                    ->orWhereNull('promotions.date_debut');
            })
            ->where(function ($q) use ($now) {
                $q->where('promotions.date_fin', '>=', $now)
                    ->orWhereNull('promotions.date_fin');
            });
    }

    /**
     * Scope a query to only include promotions valid for a specific date
     */
    public function scopeValidFor($query, $date = null)
    {
        $date = $date ?? now();
        return $query->where(function ($q) use ($date) {
            $q->where('promotions.date_debut', '<=', $date)
                ->orWhereNull('promotions.date_debut');
        })
            ->where(function ($q) use ($date) {
                $q->where('promotions.date_fin', '>=', $date)
                    ->orWhereNull('promotions.date_fin');
            });
    }

    /**
     * Scope a query to only include featured promotions
     */
    public function scopeFeatured($query)
    {
        return $query->where('featured', true);
    }

    /**
     * Scope a query to order by priority
     */
    public function scopeByPriority($query, $direction = 'desc')
    {
        return $query->orderBy('priorité', $direction);
    }

    /**
     * Scope a query to filter by promotion type
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope a query to filter by status
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('statut', $status);
    }

    /**
     * Scope a query to find promotions that can be cumulated
     */
    public function scopeCumulable($query)
    {
        return $query->where('cumulable', true);
    }

    /**
     * Scope a query to find promotions by event
     */
    public function scopeForEvent($query, $eventId)
    {
        return $query->where('event_id', $eventId);
    }

    public function estActive()
    {
        if ($this->statut === 'inactive') {
            return false;
        }

        $maintenant = now();

        if ($this->date_debut && $maintenant->lt($this->date_debut)) {
            return false;
        }

        if ($this->date_fin && $maintenant->gt($this->date_fin)) {
            return false;
        }

        return true;
    }

    public function calculerRemise($montant)
    {
        if (!$this->estActive()) {
            return 0;
        }

        switch ($this->type) {
            case 'pourcentage':
                return $montant * ($this->valeur / 100);
            case 'montant_fixe':
                return min($montant, $this->valeur);
            case 'gratuit':
                return $montant;
            default:
                return 0;
        }
    }
}
