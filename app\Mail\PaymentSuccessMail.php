<?php

namespace App\Mail;

use App\Models\Commande;
use App\Models\Paiement;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class PaymentSuccessMail extends Mailable
{
    use Queueable, SerializesModels;

    public Commande $commande;
    public Paiement $paiement;

    /**
     * Create a new message instance.
     */
    public function __construct(Commande $commande, Paiement $paiement)
    {
        $this->commande = $commande;
        $this->paiement = $paiement;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            from: config('mail.from.address'),
            subject: 'Paiement confirmé pour votre commande #' . $this->commande->numero_commande,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.orders.payment-success',
            with: [
                'commande' => $this->commande,
                'paiement' => $this->paiement,
                'orderNumber' => $this->commande->numero_commande,
                'orderTotal' => $this->commande->total_commande,
                'paymentAmount' => $this->paiement->montant,
                'paymentMethod' => $this->paiement->methode_paiement,
                'transactionId' => $this->paiement->transaction_id,
                'paidAt' => $this->paiement->created_at ? $this->paiement->created_at->format('d/m/Y à H:i') : now()->format('d/m/Y à H:i'),
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
