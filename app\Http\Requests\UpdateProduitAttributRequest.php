<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateProduitAttributRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'valeur' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'valeur.required' => 'La valeur de l\'attribut est obligatoire.',
        ];
    }
} 