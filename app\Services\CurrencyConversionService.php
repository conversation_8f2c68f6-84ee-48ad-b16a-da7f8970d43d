<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class CurrencyConversionService
{
    private const TND_TO_EUR_FALLBACK_RATE = 0.305; // Fallback rate as of 2024 (1 TND = 0.305 EUR)
    private const CACHE_TTL = 3600; // 1 hour cache

    /**
     * Convert amount from TND to EUR
     */
    public function convertTndToEur(float $amountTnd): array
    {
        $exchangeRate = $this->getExchangeRate();
        $amountEur = round($amountTnd * $exchangeRate, 2);

        return [
            'original_amount' => $amountTnd,
            'original_currency' => 'TND',
            'converted_amount' => $amountEur,
            'converted_currency' => 'EUR',
            'exchange_rate' => $exchangeRate,
            'conversion_timestamp' => now()->toISOString()
        ];
    }

    /**
     * Get current TND to EUR exchange rate
     */
    public function getExchangeRate(): float
    {
        $cacheKey = 'exchange_rate_tnd_eur';

        // Try to get from cache first
        $cachedRate = Cache::get($cacheKey);
        if ($cachedRate) {
            return $cachedRate;
        }

        // Try multiple exchange rate APIs
        $rate = $this->fetchFromExchangeRateAPI()
            ?? $this->fetchFromFreeCurrencyAPI()
            ?? $this->fetchFromCurrencyLayerAPI()
            ?? self::TND_TO_EUR_FALLBACK_RATE;

        // Cache the rate
        Cache::put($cacheKey, $rate, self::CACHE_TTL);

        return $rate;
    }

    /**
     * Fetch rate from ExchangeRate-API (Free tier available)
     */
    private function fetchFromExchangeRateAPI(): ?float
    {
        try {
            $response = Http::timeout(10)->get('https://api.exchangerate-api.com/v4/latest/TND');

            if ($response->successful()) {
                $data = $response->json();
                return $data['rates']['EUR'] ?? null;
            }
        } catch (\Exception $e) {
            Log::warning('ExchangeRate-API failed: ' . $e->getMessage());
        }

        return null;
    }

    /**
     * Fetch rate from FreeCurrencyAPI
     */
    private function fetchFromFreeCurrencyAPI(): ?float
    {
        try {
            // You can get a free API key from https://freecurrencyapi.com/
            $apiKey = config('services.currency.freecurrency_api_key');

            if (!$apiKey) {
                return null;
            }

            $response = Http::timeout(10)->get('https://api.freecurrencyapi.com/v1/latest', [
                'apikey' => $apiKey,
                'base_currency' => 'TND',
                'currencies' => 'EUR'
            ]);

            if ($response->successful()) {
                $data = $response->json();
                return $data['data']['EUR'] ?? null;
            }
        } catch (\Exception $e) {
            Log::warning('FreeCurrencyAPI failed: ' . $e->getMessage());
        }

        return null;
    }

    /**
     * Fetch rate from CurrencyLayer API
     */
    private function fetchFromCurrencyLayerAPI(): ?float
    {
        try {
            // You can get a free API key from https://currencylayer.com/
            $apiKey = config('services.currency.currencylayer_api_key');

            if (!$apiKey) {
                return null;
            }

            $response = Http::timeout(10)->get('http://api.currencylayer.com/live', [
                'access_key' => $apiKey,
                'source' => 'TND',
                'currencies' => 'EUR',
                'format' => 1
            ]);

            if ($response->successful()) {
                $data = $response->json();
                return $data['quotes']['TNDEUR'] ?? null;
            }
        } catch (\Exception $e) {
            Log::warning('CurrencyLayer API failed: ' . $e->getMessage());
        }

        return null;
    }

    /**
     * Check if a currency is supported by Stripe
     */
    public function isSupportedByStripe(string $currency): bool
    {
        $stripeSupportedCurrencies = [
            'aed',
            'afn',
            'all',
            'amd',
            'ang',
            'aoa',
            'ars',
            'aud',
            'awg',
            'azn',
            'bam',
            'bbd',
            'bdt',
            'bgn',
            'bhd',
            'bif',
            'bmd',
            'bnd',
            'bob',
            'brl',
            'bsd',
            'bwp',
            'bzd',
            'cad',
            'cdf',
            'chf',
            'clp',
            'cny',
            'cop',
            'crc',
            'cve',
            'czk',
            'djf',
            'dkk',
            'dop',
            'dzd',
            'egp',
            'etb',
            'eur',
            'fjd',
            'fkp',
            'gbp',
            'gel',
            'ghs',
            'gip',
            'gmd',
            'gnf',
            'gtq',
            'gyd',
            'hkd',
            'hnl',
            'hrk',
            'htg',
            'huf',
            'idr',
            'ils',
            'inr',
            'iqd',
            'isk',
            'jmd',
            'jod',
            'jpy',
            'kes',
            'kgs',
            'khr',
            'kmf',
            'krw',
            'kwd',
            'kyd',
            'kzt',
            'lak',
            'lbp',
            'lkr',
            'lrd',
            'lsl',
            'mad',
            'mdl',
            'mga',
            'mkd',
            'mmk',
            'mnt',
            'mop',
            'mro',
            'mur',
            'mvr',
            'mwk',
            'mxn',
            'myr',
            'mzn',
            'nad',
            'ngn',
            'nio',
            'nok',
            'npr',
            'nzd',
            'omr',
            'pab',
            'pen',
            'pgk',
            'php',
            'pkr',
            'pln',
            'pyg',
            'qar',
            'ron',
            'rsd',
            'rub',
            'rwf',
            'sar',
            'sbd',
            'scr',
            'sek',
            'sgd',
            'shp',
            'sle',
            'sos',
            'srd',
            'std',
            'szl',
            'thb',
            'tjs',
            'top',
            'try',
            'ttd',
            'tvd',
            'tzs',
            'uah',
            'ugx',
            'usd',
            'uyu',
            'uzs',
            'vnd',
            'vuv',
            'wst',
            'xaf',
            'xcd',
            'xof',
            'xpf',
            'yer',
            'zar',
            'zmw'
        ];

        return in_array(strtolower($currency), $stripeSupportedCurrencies);
    }

    /**
     * Get manual exchange rate (for testing or admin override)
     */
    public function getManualExchangeRate(): ?float
    {
        return Cache::get('manual_tnd_eur_rate');
    }

    /**
     * Set manual exchange rate (for testing or admin override)
     */
    public function setManualExchangeRate(float $rate): void
    {
        Cache::put('manual_tnd_eur_rate', $rate, 86400); // 24 hours
        Log::info("Manual TND to EUR exchange rate set: $rate");
    }

    /**
     * Clear cached exchange rates
     */
    public function clearCache(): void
    {
        Cache::forget('exchange_rate_tnd_eur');
        Cache::forget('manual_tnd_eur_rate');
    }
}
