# 📧 Contact API Documentation

## Overview

The Contact API provides a secure and rate-limited way for users to submit contact forms that will be sent via email to `<EMAIL>`. The API includes validation, rate limiting, and comprehensive logging.

## 🚀 Features

- ✅ **Email Delivery**: Sends formatted emails to `<EMAIL>`
- ✅ **Rate Limiting**: 5 submissions per hour per IP address
- ✅ **Input Validation**: Comprehensive validation with French error messages
- ✅ **Security**: IP tracking, user agent logging, and spam protection
- ✅ **Professional Email Template**: Beautiful HTML email template
- ✅ **Queue Support**: Emails are queued for better performance
- ✅ **Logging**: Complete audit trail of all submissions

## 📍 API Endpoints

### Base URL
```
/api/contact
```

### 1. Submit Contact Form
**POST** `/api/contact/submit`

Submit a contact form that will be sent via email.

#### Request Body
```json
{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "message": "Your message here (minimum 10 characters)"
}
```

#### Validation Rules
- **name**: Required, string, 2-255 characters
- **email**: Required, valid email address, max 255 characters
- **message**: Required, string, 10-5000 characters

#### Success Response (200)
```json
{
  "success": true,
  "message": "Message envoyé avec succès",
  "data": {
    "message": "Votre message a été envoyé avec succès. Nous vous répondrons dans les plus brefs délais.",
    "submitted_at": "29/05/2025 à 12:30"
  }
}
```

#### Validation Error Response (422)
```json
{
  "success": false,
  "message": "Erreur de validation",
  "errors": {
    "name": ["Le nom doit contenir au moins 2 caractères."],
    "email": ["L'adresse email doit être valide."],
    "message": ["Le message doit contenir au moins 10 caractères."]
  }
}
```

#### Rate Limit Error Response (429)
```json
{
  "success": false,
  "message": "Trop de tentatives. Veuillez réessayer dans 45 minutes.",
  "data": {
    "retry_after": 2700
  }
}
```

### 2. Get Contact Form Information
**GET** `/api/contact/info`

Get information about the contact form fields and validation rules.

#### Response (200)
```json
{
  "success": true,
  "message": "Informations du formulaire de contact",
  "data": {
    "fields": {
      "name": {
        "required": true,
        "min_length": 2,
        "max_length": 255,
        "type": "string"
      },
      "email": {
        "required": true,
        "max_length": 255,
        "type": "email"
      },
      "message": {
        "required": true,
        "min_length": 10,
        "max_length": 5000,
        "type": "text"
      }
    },
    "rate_limit": {
      "max_attempts": 5,
      "window": "1 hour"
    },
    "contact_email": "<EMAIL>"
  }
}
```

### 3. Check Rate Limit Status
**GET** `/api/contact/rate-limit-status`

Check the current rate limit status for the requesting IP address.

#### Response (200)
```json
{
  "success": true,
  "message": "Statut de la limitation de débit",
  "data": {
    "attempts_made": 2,
    "attempts_remaining": 3,
    "max_attempts": 5,
    "window": "1 hour"
  }
}
```

#### When Rate Limited
```json
{
  "success": true,
  "message": "Statut de la limitation de débit",
  "data": {
    "attempts_made": 5,
    "attempts_remaining": 0,
    "max_attempts": 5,
    "window": "1 hour",
    "retry_after_seconds": 2700,
    "retry_after_minutes": 45
  }
}
```

## 📧 Email Template

The contact form submissions are sent using a professional HTML email template that includes:

- **Header**: JiheneLine branding with contact form title
- **Timestamp**: When the message was received
- **Contact Information**: Name and email of the sender
- **Message Content**: Formatted message with proper line breaks
- **Reply Instructions**: Automatic reply-to configuration
- **Footer**: Professional branding and system information

### Email Features
- **Reply-To**: Automatically set to the sender's email
- **Subject**: "Nouveau message de contact - JiheneLine"
- **Responsive Design**: Works on all email clients
- **Professional Styling**: Clean, modern design
- **Security Information**: IP address and timestamp tracking

## 🔒 Security Features

### Rate Limiting
- **Limit**: 5 submissions per hour per IP address
- **Window**: Rolling 1-hour window
- **Storage**: Uses Laravel's rate limiter with Redis/database backend

### Input Validation
- **XSS Protection**: All inputs are validated and sanitized
- **Length Limits**: Prevents abuse with reasonable character limits
- **Email Validation**: Ensures valid email addresses
- **Required Fields**: All fields are mandatory

### Logging & Monitoring
- **Submission Logging**: All submissions are logged with IP and user agent
- **Error Logging**: Failed submissions and errors are logged
- **Rate Limit Logging**: Rate limit violations are tracked
- **Email Delivery**: Success/failure of email delivery is logged

## 🧪 Testing

### Test Script
Run the included test script to verify the API:

```bash
php test_contact_api.php
```

### Manual Testing with cURL

#### Submit a contact form:
```bash
curl -X POST http://localhost:8000/api/contact/submit \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "name": "John Doe",
    "email": "<EMAIL>",
    "message": "Test message from API documentation"
  }'
```

#### Get form information:
```bash
curl -X GET http://localhost:8000/api/contact/info \
  -H "Accept: application/json"
```

#### Check rate limit status:
```bash
curl -X GET http://localhost:8000/api/contact/rate-limit-status \
  -H "Accept: application/json"
```

## 🔧 Configuration

### Resend Configuration (Recommended)
The Contact API is configured to use **Resend** for email delivery. Make sure these are configured in your `.env` file:

```env
# Resend Configuration (Current Setup)
MAIL_MAILER=resend
RESEND_API_KEY=your_resend_api_key_here
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="JiheneLine"

# Queue Configuration (recommended for production)
QUEUE_CONNECTION=redis
```

### Alternative SMTP Configuration
If you prefer to use traditional SMTP instead of Resend:

```env
# SMTP Configuration (Alternative)
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="JiheneLine"
```

### Resend Setup Instructions
1. **Create Resend Account**: Go to [resend.com](https://resend.com) and sign up
2. **Get API Key**: Navigate to API Keys section and create a new key
3. **Add Domain**: Add and verify your domain (jiheneline.tech)
4. **Update .env**: Add your API key to `RESEND_API_KEY`
5. **Test**: Run `php test_contact_resend.php` to verify setup

### Rate Limiting Configuration
The rate limiting is configured in the `ContactController` and can be adjusted:

```php
// In ContactController.php
$key = 'contact-form:' . $request->ip();
if (RateLimiter::tooManyAttempts($key, 5)) { // 5 attempts
    // Rate limited
}
RateLimiter::hit($key, 3600); // 1 hour window
```

## 🚀 Frontend Integration

### JavaScript Example
```javascript
async function submitContactForm(formData) {
  try {
    const response = await fetch('/api/contact/submit', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify(formData)
    });

    const result = await response.json();

    if (response.ok) {
      // Success
      alert(result.data.message);
    } else {
      // Handle validation errors or rate limiting
      if (response.status === 422) {
        // Show validation errors
        console.log(result.errors);
      } else if (response.status === 429) {
        // Rate limited
        alert(result.message);
      }
    }
  } catch (error) {
    console.error('Error submitting form:', error);
  }
}

// Usage
const formData = {
  name: 'John Doe',
  email: '<EMAIL>',
  message: 'Hello, I would like more information about your products.'
};

submitContactForm(formData);
```

### React Example
```jsx
import { useState } from 'react';

function ContactForm() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setErrors({});

    try {
      const response = await fetch('/api/contact/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(formData)
      });

      const result = await response.json();

      if (response.ok) {
        alert(result.data.message);
        setFormData({ name: '', email: '', message: '' });
      } else if (response.status === 422) {
        setErrors(result.errors);
      } else {
        alert(result.message);
      }
    } catch (error) {
      alert('Une erreur est survenue. Veuillez réessayer.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <div>
        <label>Nom:</label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => setFormData({...formData, name: e.target.value})}
          required
        />
        {errors.name && <span className="error">{errors.name[0]}</span>}
      </div>

      <div>
        <label>Email:</label>
        <input
          type="email"
          value={formData.email}
          onChange={(e) => setFormData({...formData, email: e.target.value})}
          required
        />
        {errors.email && <span className="error">{errors.email[0]}</span>}
      </div>

      <div>
        <label>Message:</label>
        <textarea
          value={formData.message}
          onChange={(e) => setFormData({...formData, message: e.target.value})}
          required
          minLength={10}
          maxLength={5000}
        />
        {errors.message && <span className="error">{errors.message[0]}</span>}
      </div>

      <button type="submit" disabled={loading}>
        {loading ? 'Envoi...' : 'Envoyer'}
      </button>
    </form>
  );
}
```

## 📝 Error Codes

| Code | Description |
|------|-------------|
| 200  | Success - Form submitted successfully |
| 422  | Validation Error - Invalid input data |
| 429  | Rate Limited - Too many submissions |
| 500  | Server Error - Internal server error |

## 🎯 Best Practices

1. **Always validate on frontend** before submitting to reduce server load
2. **Handle rate limiting gracefully** with user-friendly messages
3. **Show loading states** during form submission
4. **Clear form data** after successful submission
5. **Display validation errors** clearly to users
6. **Implement retry logic** for network errors
7. **Use HTTPS** in production for security

## 🔍 Monitoring

The API logs all activities for monitoring:

- **Successful submissions**: Logged with user details
- **Failed submissions**: Logged with error details
- **Rate limit violations**: Logged with IP and timestamp
- **Email delivery status**: Success/failure tracking

Check Laravel logs for detailed information:
```bash
tail -f storage/logs/laravel.log
```

## 🎉 Conclusion

The Contact API is production-ready with comprehensive security, validation, and monitoring features. It provides a reliable way for users to contact your business while protecting against spam and abuse.

For any questions or issues, please contact the development team.
