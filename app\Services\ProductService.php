<?php

namespace App\Services;

use App\Models\Produit;
use App\Models\Promotion;
use App\Models\Marque;
use App\Models\sous_sousCategorie;
use App\Services\SimpleCacheService;
use App\Services\ValidationService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class ProductService
{
    private SimpleCacheService $cacheService;
    private ValidationService $validationService;

    public function __construct(ValidationService $validationService, SimpleCacheService $cacheService)
    {
        $this->cacheService = $cacheService;
        $this->validationService = $validationService;
    }

    /**
     * Get products with advanced filtering, caching, and optimization
     */
    public function getProducts(array $filters = [], bool $useCache = true): LengthAwarePaginator
    {
        $cacheKey = $this->generateProductCacheKey($filters);

        if ($useCache && !($filters['no_cache'] ?? false)) {
            $cached = $this->cacheService->get($cacheKey);
            if ($cached) {
                Log::debug('Product listing served from cache', ['cache_key' => $cacheKey]);
                return $cached;
            }
        }

        $query = $this->buildOptimizedProductQuery($filters);

        $perPage = min($filters['per_page'] ?? 15, 100);
        $page = max($filters['page'] ?? 1, 1);

        // Get paginated results
        $products = $query->paginate($perPage, ['*'], 'page', $page);

        // Cache the results if enabled
        if ($useCache && !($filters['no_cache'] ?? false)) {
            $this->cacheService->put($cacheKey, $products, 3600); // 1 hour cache
            Log::debug('Product listing cached', ['cache_key' => $cacheKey, 'count' => $products->count()]);
        }

        return $products;
    }

    /**
     * Build optimized product query with eager loading and efficient filtering
     */
    private function buildOptimizedProductQuery(array $filters): Builder
    {
        $query = Produit::query();

        // Handle custom 'with' parameter for eager loading
        if (!empty($filters['with'])) {
            $withRelations = $this->parseWithParameter($filters['with']);
            $query->with($withRelations);
        } else {
            // Default eager loading
            $query->with([
                'marque:id,nom_marque,logo_marque',
                'marque.images' => function ($query) {
                    $query->select('id', 'imageable_id', 'imageable_type', 'path', 'order')
                        ->orderBy('order');
                },
                'sousSousCategorie:id,nom_sous_sous_categorie,sous_categorie_id',
                'sousSousCategorie.sousCategorie:id,nom_sous_categorie,categorie_id',
                'sousSousCategorie.sousCategorie.categorie:id,nom_categorie',
                'collections:id,nom',
                'images' => function ($query) {
                    $query->select('id', 'imageable_id', 'imageable_type', 'path', 'order')
                        ->orderBy('order');
                },
                'variantes:id,produit_parent_id,sku,prix_supplement,stock,actif',
                'promotions' => function ($query) {
                    $query->active()->select('promotions.id', 'promotions.nom', 'promotions.type', 'promotions.valeur', 'promotions.statut');
                }
            ]);
        }

        // Apply search filter with full-text search optimization
        if (!empty($filters['search'])) {
            $searchTerm = $this->validationService->sanitizeSearchInput($filters['search']);
            $query->where(function ($q) use ($searchTerm) {
                $q->where('nom_produit', 'LIKE', "%{$searchTerm}%")
                    ->orWhere('description_produit', 'LIKE', "%{$searchTerm}%")
                    ->orWhere('reference', 'LIKE', "%{$searchTerm}%")
                    ->orWhereHas('marque', function ($mq) use ($searchTerm) {
                        $mq->where('nom_marque', 'LIKE', "%{$searchTerm}%");
                    });
            });
        }

        // Apply price filters
        if (isset($filters['prix_min'])) {
            $query->where('prix_produit', '>=', $filters['prix_min']);
        }
        if (isset($filters['prix_max'])) {
            $query->where('prix_produit', '<=', $filters['prix_max']);
        }

        // Apply brand filter
        if (!empty($filters['marque_id'])) {
            $query->where('marque_id', $filters['marque_id']);
        }

        // Apply category filter
        if (!empty($filters['sous_sous_categorie_id'])) {
            $query->where('sous_sous_categorie_id', $filters['sous_sous_categorie_id']);
        }

        // Apply stock filter
        if (isset($filters['in_stock']) && $filters['in_stock']) {
            $query->where('quantite_produit', '>', 0);
        }

        // Apply promotion filter
        if (isset($filters['on_sale']) && $filters['on_sale']) {
            $query->whereHas('promotions', function ($q) {
                $q->active();
            });
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';

        switch ($sortBy) {
            case 'id':
                $query->orderBy('id', $sortDirection);
                break;
            case 'nom_produit':
                $query->orderBy('nom_produit', $sortDirection);
                break;
            case 'prix_produit':
                $query->orderBy('prix_produit', $sortDirection);
                break;
            case 'stock':
                $query->orderBy('quantite_produit', $sortDirection);
                break;
            case 'created_at':
                $query->orderBy('created_at', $sortDirection);
                break;
            case 'price_asc':
                $query->orderBy('prix_produit', 'asc');
                break;
            case 'price_desc':
                $query->orderBy('prix_produit', 'desc');
                break;
            case 'popularity':
                // Order by products with most orders
                $query->withCount('commandes')
                    ->orderBy('commandes_count', 'desc');
                break;
            case 'rating':
                // Order by average rating if rating system exists
                $query->orderBy('created_at', 'desc'); // Fallback
                break;
            default:
                $query->orderBy('created_at', 'desc');
        }

        return $query;
    }

    /**
     * Get a single product with complete details and caching
     */
    public function getProductById(int $productId, bool $useCache = true): ?Produit
    {
        if ($useCache) {
            $cached = $this->cacheService->get($productId);
            if ($cached) {
                return $cached;
            }
        }

        $product = Produit::with([
            'marque',
            'sousSousCategorie.sousCategorie.categorie',
            'collections',
            'images' => function ($query) {
                $query->orderBy('order');
            },
            'variantes' => function ($query) {
                $query->where('actif', true)->orderBy('prix_supplement');
            },
            'promotions' => function ($query) {
                $query->active();
            },
            'valeurs.attribut'
        ])->find($productId);

        if ($product && $useCache) {
            $this->cacheService->put($productId, $product, 3600);
        }

        return $product;
    }

    /**
     * Get related products using sophisticated algorithms
     */
    public function getRelatedProducts(Produit $product, int $limit = 8): Collection
    {
        $cacheKey = "related_products:{$product->id}:{$limit}";

        $cached = $this->cacheService->getProductListing($cacheKey);
        if ($cached) {
            return $cached;
        }

        // Algorithm: Same category > Same brand > Similar price range
        $related = collect();

        // 1. Same sub-sub-category (excluding current product)
        if ($product->sous_sous_categorie_id) {
            $sameCategory = Produit::where('sous_sous_categorie_id', $product->sous_sous_categorie_id)
                ->where('id', '!=', $product->id)
                ->where('quantite_produit', '>', 0)
                ->with(['marque', 'images'])
                ->inRandomOrder()
                ->limit($limit / 2)
                ->get();

            $related = $related->merge($sameCategory);
        }

        // 2. Same brand (if we need more products)
        if ($related->count() < $limit && $product->marque_id) {
            $needed = $limit - $related->count();
            $sameBrand = Produit::where('marque_id', $product->marque_id)
                ->where('id', '!=', $product->id)
                ->whereNotIn('id', $related->pluck('id'))
                ->where('quantite_produit', '>', 0)
                ->with(['marque', 'images'])
                ->inRandomOrder()
                ->limit($needed)
                ->get();

            $related = $related->merge($sameBrand);
        }

        // 3. Similar price range (if we still need more)
        if ($related->count() < $limit) {
            $needed = $limit - $related->count();
            $priceRange = $product->prix_produit * 0.3; // 30% price range

            $similarPrice = Produit::whereBetween('prix_produit', [
                $product->prix_produit - $priceRange,
                $product->prix_produit + $priceRange
            ])
                ->where('id', '!=', $product->id)
                ->whereNotIn('id', $related->pluck('id'))
                ->where('quantite_produit', '>', 0)
                ->with(['marque', 'images'])
                ->inRandomOrder()
                ->limit($needed)
                ->get();

            $related = $related->merge($similarPrice);
        }

        $result = $related->take($limit);

        // Cache for 2 hours
        $this->cacheService->setProductListing($cacheKey, $result, 7200);

        return $result;
    }

    /**
     * Get featured products with caching
     */
    public function getFeaturedProducts(int $limit = 12): Collection
    {
        $cacheKey = "featured_products:{$limit}";

        $cached = $this->cacheService->getProductListing($cacheKey);
        if ($cached) {
            return $cached;
        }

        // Get products with active promotions or high stock/new arrivals
        $featured = Produit::query()
            ->with(['marque', 'images', 'promotions'])
            ->where(function ($query) {
                $query->whereHas('promotions', function ($q) {
                    $q->active();
                })
                    ->orWhere('created_at', '>=', now()->subDays(30)) // New arrivals
                    ->orWhere('quantite_produit', '>', 50); // High stock items
            })
            ->where('quantite_produit', '>', 0)
            ->orderByDesc('created_at')
            ->limit($limit)
            ->get();

        // Cache for 4 hours
        $this->cacheService->setProductListing($cacheKey, $featured, 14400);

        return $featured;
    }

    /**
     * Search products with advanced full-text search
     */
    public function searchProducts(string $query, array $filters = [], int $limit = 50): Collection
    {
        $sanitizedQuery = $this->validationService->sanitizeSearchInput($query);
        $cacheKey = "search:" . md5($sanitizedQuery . serialize($filters)) . ":{$limit}";

        $cached = $this->cacheService->getProductListing($cacheKey);
        if ($cached) {
            return $cached;
        }

        $searchQuery = Produit::query()
            ->with(['marque', 'images', 'sousSousCategorie'])
            ->where(function ($q) use ($sanitizedQuery) {
                $q->where('nom_produit', 'LIKE', "%{$sanitizedQuery}%")
                    ->orWhere('description_produit', 'LIKE', "%{$sanitizedQuery}%")
                    ->orWhere('reference', 'LIKE', "%{$sanitizedQuery}%")
                    ->orWhereHas('marque', function ($mq) use ($sanitizedQuery) {
                        $mq->where('nom_marque', 'LIKE', "%{$sanitizedQuery}%");
                    });
            });

        // Apply additional filters
        if (!empty($filters['marque_id'])) {
            $searchQuery->where('marque_id', $filters['marque_id']);
        }

        if (!empty($filters['prix_min'])) {
            $searchQuery->where('prix_produit', '>=', $filters['prix_min']);
        }

        if (!empty($filters['prix_max'])) {
            $searchQuery->where('prix_produit', '<=', $filters['prix_max']);
        }

        $results = $searchQuery
            ->orderByRaw("CASE
                WHEN nom_produit LIKE ? THEN 1
                WHEN reference LIKE ? THEN 2
                ELSE 3 END", ["%{$sanitizedQuery}%", "%{$sanitizedQuery}%"])
            ->limit($limit)
            ->get();

        // Cache for 30 minutes (shorter for search results)
        $this->cacheService->setProductListing($cacheKey, $results, 1800);

        return $results;
    }

    /**
     * Parse the 'with' parameter for eager loading
     */
    private function parseWithParameter(string $withParam): array
    {
        // URL decode the parameter (handles %2C for commas)
        $decoded = urldecode($withParam);

        // Split by comma and clean up
        $relations = array_map('trim', explode(',', $decoded));

        // Map frontend relation names to actual model relations
        $relationMap = [
            'marque' => 'marque:id,nom_marque,logo_marque',
            'sousSousCategorie' => 'sousSousCategorie:id,nom_sous_sous_categorie,sous_categorie_id',
            'sousCategorie' => 'sousSousCategorie.sousCategorie:id,nom_sous_categorie,categorie_id',
            'categorie' => 'sousSousCategorie.sousCategorie.categorie:id,nom_categorie',
            'collections' => 'collections:id,nom',
            'images' => 'images:id,imageable_id,imageable_type,path,order',
            'variantes' => 'variantes:id,produit_parent_id,sku,prix_supplement,stock,actif',
            'promotions' => 'promotions:id,nom,type,valeur,statut',
            'valeurs' => 'valeurs.attribut:id,nom,type_valeur'
        ];

        $mappedRelations = [];
        foreach ($relations as $relation) {
            if (isset($relationMap[$relation])) {
                $mappedRelations[] = $relationMap[$relation];
            } else {
                // Allow direct relation names as fallback
                $mappedRelations[] = $relation;
            }
        }

        return $mappedRelations;
    }

    /**
     * Generate cache key for product listings
     */
    private function generateProductCacheKey(array $filters): string
    {
        // Remove sensitive or non-cacheable parameters
        $cacheableFilters = array_filter($filters, function ($key) {
            return !in_array($key, ['no_cache', 'csrf_token']);
        }, ARRAY_FILTER_USE_KEY);

        ksort($cacheableFilters);
        return 'products:list:' . md5(serialize($cacheableFilters));
    }

    /**
     * Get product statistics for admin dashboard
     */
    public function getProductStatistics(): array
    {
        $cacheKey = 'product_statistics';

        $cached = $this->cacheService->getProductListing($cacheKey);
        if ($cached) {
            return $cached;
        }

        $stats = [
            'total_products' => Produit::count(),
            'in_stock_products' => Produit::where('quantite_produit', '>', 0)->count(),
            'out_of_stock_products' => Produit::where('quantite_produit', '<=', 0)->count(),
            'products_on_sale' => Produit::whereHas('promotions', function ($q) {
                $q->active();
            })->count(),
            'total_brands' => Marque::count(),
            'total_categories' => sous_sousCategorie::count(),
            'avg_price' => round(Produit::avg('prix_produit'), 2),
            'total_value' => round(DB::table('produits')->sum(DB::raw('prix_produit * quantite_produit')), 2),
        ];

        // Cache for 6 hours
        $this->cacheService->setProductListing($cacheKey, $stats, 21600);

        return $stats;
    }

    /**
     * Invalidate all product-related cache
     */
    public function invalidateProductCache(?int $productId = null): void
    {
        // Always flush all product cache to ensure consistency
        // This is more aggressive but ensures no stale data
        $this->cacheService->flushProductCache();

        // Also clear any specific cache patterns
        $patterns = [
            'products:list:*',
            'featured_products:*',
            'search:*',
            'related_products:*',
            'product_statistics'
        ];

        foreach ($patterns as $pattern) {
            $this->cacheService->forgetPattern($pattern);
        }

        Log::info('Product cache invalidated comprehensively', [
            'product_id' => $productId,
            'patterns_cleared' => $patterns
        ]);
    }

    /**
     * Get filtered products (alias for getProducts)
     */
    public function getFilteredProducts(array $filters = [], bool $useCache = true): LengthAwarePaginator
    {
        return $this->getProducts($filters, $useCache);
    }

    /**
     * Create a new product
     */
    public function createProduct(array $data): Produit
    {
        try {
            DB::beginTransaction();

            $product = Produit::create([
                'nom_produit' => $data['nom_produit'],
                'description_produit' => $data['description_produit'] ?? null,
                'prix_produit' => $data['prix_produit'],
                'image_produit' => $data['image_produit'] ?? null,
                'quantite_produit' => $data['quantite_produit'],
                'marque_id' => $data['marque_id'],
                'sous_sous_categorie_id' => $data['sous_sous_categorie_id'],
            ]);

            // Load relationships
            $product->load(['marque', 'sousSousCategorie']);

            // Clear cache
            $this->invalidateProductCache();

            DB::commit();

            Log::info('Product created successfully', ['product_id' => $product->id]);

            return $product;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create product: ' . $e->getMessage(), ['data' => $data]);
            throw $e;
        }
    }
}
