<?php

namespace App\Http\Controllers;

use App\Models\Attribut;
use App\Models\GroupeAttribut;
use App\Models\ProduitValeur;
use App\Models\SousCategorie;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use App\Http\Requests\StoreAttributRequest;
use App\Http\Requests\UpdateAttributRequest;

class AttributController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        try {
            $query = Attribut::with('groupe');

            // Filtrage par groupe
            if ($request->has('groupe_id')) {
                $query->where('groupe_id', $request->input('groupe_id'));
            }

            // Filtrage par type de valeur
            if ($request->has('type_valeur')) {
                $query->where('type_valeur', $request->input('type_valeur'));
            }

            // Filtrage par filtrabilité
            if ($request->has('filtrable')) {
                $query->where('filtrable', $request->boolean('filtrable'));
            }

            // Filtrage par comparabilité
            if ($request->has('comparable')) {
                $query->where('comparable', $request->boolean('comparable'));
            }

            // Filtrage par sous-catégorie
            if ($request->has('sous_categorie_id')) {
                $query->whereHas('sousCategories', function ($q) use ($request) {
                    $q->where('sous_categories.id', $request->input('sous_categorie_id'));
                });
            }

            $attributs = $query->get();

            return response()->json($attributs);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erreur lors de la récupération des attributs',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreAttributRequest $request)
    {
        try {
            DB::beginTransaction();
            $validated = $request->validated();
            // Créer l'attribut
            $attribut = Attribut::create([
                'nom' => $validated['nom'],
                'description' => $validated['description'] ?? null,
                'type_valeur' => $validated['type_valeur'],
                'groupe_id' => $validated['groupe_id'] ?? null,
                'obligatoire' => $validated['obligatoire'] ?? false,
                'filtrable' => $validated['filtrable'] ?? false,
                'comparable' => $validated['comparable'] ?? false
            ]);
            // Associer aux sous-catégories si spécifié
            if (!empty($validated['sous_categories'])) {
                foreach ($validated['sous_categories'] as $sousCategorie) {
                    $attribut->sousCategories()->attach($sousCategorie['id'], [
                        'obligatoire' => $sousCategorie['obligatoire'] ?? false
                    ]);
                }
            }
            DB::commit();
            return response()->json($attribut->load('groupe', 'sousCategories'), 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'error' => 'Erreur lors de la création de l\'attribut',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
            $attribut = Attribut::with(['groupe', 'sousCategories', 'produitValeurs'])->findOrFail($id);
            return response()->json($attribut);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Attribut non trouvé',
                'message' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateAttributRequest $request, string $id)
    {
        try {
            DB::beginTransaction();
            $validated = $request->validated();
            $attribut = Attribut::findOrFail($id);
            // Mettre à jour les propriétés de base
            $attribut->fill([
                'nom' => $validated['nom'] ?? $attribut->nom,
                'description' => $validated['description'] ?? $attribut->description,
                'type_valeur' => $validated['type_valeur'] ?? $attribut->type_valeur,
                'groupe_id' => $validated['groupe_id'] ?? $attribut->groupe_id,
                'obligatoire' => $validated['obligatoire'] ?? $attribut->obligatoire,
                'filtrable' => $validated['filtrable'] ?? $attribut->filtrable,
                'comparable' => $validated['comparable'] ?? $attribut->comparable
            ]);
            $attribut->save();
            // Mettre à jour les associations avec les sous-catégories si spécifié
            if (!empty($validated['sous_categories'])) {
                $pivotData = [];
                foreach ($validated['sous_categories'] as $sousCategorie) {
                    $pivotData[$sousCategorie['id']] = [
                        'obligatoire' => $sousCategorie['obligatoire'] ?? false
                    ];
                }
                $attribut->sousCategories()->sync($pivotData);
            }
            DB::commit();
            return response()->json($attribut->load('groupe', 'sousCategories'));
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'error' => 'Erreur lors de la mise à jour de l\'attribut',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            DB::beginTransaction();

            $attribut = Attribut::findOrFail($id);

            // Vérifier si l'attribut est utilisé par des produits
            if ($attribut->produitValeurs()->count() > 0) {
                return response()->json([
                    'error' => 'Impossible de supprimer cet attribut',
                    'message' => 'Cet attribut est utilisé par des produits. Veuillez d\'abord supprimer ces valeurs.'
                ], 422);
            }

            // Vérifier si l'attribut est utilisé par des variantes
            if ($attribut->varianteValeurs()->count() > 0) {
                return response()->json([
                    'error' => 'Impossible de supprimer cet attribut',
                    'message' => 'Cet attribut est utilisé par des variantes de produits. Veuillez d\'abord supprimer ces valeurs.'
                ], 422);
            }

            // Détacher des sous-catégories
            $attribut->sousCategories()->detach();

            // Supprimer l'attribut
            $attribut->delete();

            DB::commit();

            return response()->json([
                'message' => 'Attribut supprimé avec succès'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'error' => 'Erreur lors de la suppression de l\'attribut',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Associer un attribut à une sous-catégorie
     */
    public function attachToSousCategorie(Request $request, string $id, string $sousCategorieId)
    {
        try {
            $attribut = Attribut::findOrFail($id);
            $sousCategorie = SousCategorie::findOrFail($sousCategorieId);

            $validator = Validator::make($request->all(), [
                'obligatoire' => 'nullable|boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'error' => 'Données invalides',
                    'message' => $validator->errors()
                ], 422);
            }

            $attribut->sousCategories()->syncWithoutDetaching([
                $sousCategorieId => ['obligatoire' => $request->input('obligatoire', false)]
            ]);

            return response()->json([
                'message' => 'Attribut associé à la sous-catégorie avec succès',
                'attribut' => $attribut->load('sousCategories')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erreur lors de l\'association de l\'attribut à la sous-catégorie',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Détacher un attribut d'une sous-catégorie
     */
    public function detachFromSousCategorie(string $id, string $sousCategorieId)
    {
        try {
            $attribut = Attribut::findOrFail($id);
            $sousCategorie = SousCategorie::findOrFail($sousCategorieId);

            $attribut->sousCategories()->detach($sousCategorieId);

            return response()->json([
                'message' => 'Attribut détaché de la sous-catégorie avec succès',
                'attribut' => $attribut->load('sousCategories')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erreur lors du détachement de l\'attribut de la sous-catégorie',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Récupérer les attributs filtrables pour le frontoffice
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFiltrableAttributes()
    {
        try {
            $attributs = Attribut::with(['groupe'])
                ->where('filtrable', true)
                ->orderBy('groupe_id')
                ->orderBy('nom')
                ->get()
                ->map(function ($attribut) {
                    // Récupérer les valeurs uniques utilisées pour cet attribut
                    $valeurs = [];

                    if ($attribut->type_valeur === 'texte') {
                        $valeurs = ProduitValeur::where('attribut_id', $attribut->id)
                            ->whereNotNull('valeur_texte')
                            ->distinct()
                            ->pluck('valeur_texte')
                            ->filter()
                            ->values()
                            ->toArray();
                    } elseif ($attribut->type_valeur === 'nombre') {
                        $stats = ProduitValeur::where('attribut_id', $attribut->id)
                            ->whereNotNull('valeur_nombre')
                            ->selectRaw('MIN(valeur_nombre) as min, MAX(valeur_nombre) as max')
                            ->first();

                        if ($stats) {
                            $valeurs = [
                                'min' => (float) $stats->min,
                                'max' => (float) $stats->max
                            ];
                        }
                    } elseif ($attribut->type_valeur === 'booleen') {
                        $valeurs = [true, false];
                    }

                    return [
                        'id' => $attribut->id,
                        'nom' => $attribut->nom,
                        'description' => $attribut->description,
                        'type_valeur' => $attribut->type_valeur,
                        'groupe' => $attribut->groupe ? [
                            'id' => $attribut->groupe->id,
                            'nom' => $attribut->groupe->nom
                        ] : null,
                        'valeurs_disponibles' => $valeurs
                    ];
                });

            // Regrouper par groupe
            $groupes = [];
            foreach ($attributs as $attribut) {
                $groupeId = $attribut['groupe'] ? $attribut['groupe']['id'] : 0;
                $groupeNom = $attribut['groupe'] ? $attribut['groupe']['nom'] : 'Sans groupe';

                if (!isset($groupes[$groupeId])) {
                    $groupes[$groupeId] = [
                        'id' => $groupeId,
                        'nom' => $groupeNom,
                        'attributs' => []
                    ];
                }

                $groupes[$groupeId]['attributs'][] = $attribut;
            }

            return response()->json(array_values($groupes));
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erreur lors de la récupération des attributs filtrables',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
