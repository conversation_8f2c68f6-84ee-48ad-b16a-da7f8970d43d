<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('attributs', function (Blueprint $table) {
            $table->id();
            $table->string('nom');
            $table->text('description')->nullable();
            $table->enum('type_valeur', ['texte', 'nombre', 'date', 'booleen', 'liste']);
            $table->foreignId('groupe_id')->nullable()->constrained('groupes_attributs')->onDelete('set null');
            $table->boolean('obligatoire')->default(false);
            $table->boolean('filtrable')->default(false);
            $table->boolean('comparable')->default(false);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attributs');
    }
};
