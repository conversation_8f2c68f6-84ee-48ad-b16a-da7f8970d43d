<?php

use App\Http\Middleware\CheckKeycloakRole;
use App\Http\Middleware\CookieCartMiddleware;
use App\Http\Middleware\VerifyKeycloakToken;
use App\Http\Middleware\PerformanceMonitoring;
use App\Http\Middleware\SecurityHeadersMiddleware;
use App\Http\Middleware\SecurityMonitoringMiddleware;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        api: __DIR__ . '/../routes/api.php',
        commands: __DIR__ . '/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->validateCsrfTokens(except: [
            'api/*',
            'docs/*',
            'api/documentation',
            'oauth2-callback'
        ]);

        // Register middleware aliases
        $middleware->alias([
            'role' => CheckKeycloakRole::class,
            'performance' => PerformanceMonitoring::class,
            'security.headers' => SecurityHeadersMiddleware::class,
            'security.monitor' => SecurityMonitoringMiddleware::class,
        ]);

        // Add performance monitoring and security to API group
        $middleware->appendToGroup('api', [
            SecurityHeadersMiddleware::class,
            SecurityMonitoringMiddleware::class,
            PerformanceMonitoring::class,
        ]);

        $middleware->appendToGroup('protected', [
            VerifyKeycloakToken::class
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
