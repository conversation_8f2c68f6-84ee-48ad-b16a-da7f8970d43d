<?php

namespace App\Services;

use App\Services\Keycloak\KeycloakTokenService;
use App\Services\Keycloak\KeycloakUserService;
use App\Services\Keycloak\KeycloakAdminService;
use App\Services\Keycloak\KeycloakRoleService;
use Illuminate\Support\Facades\Http;
use Exception;

class KeycloakService
{
    private KeycloakTokenService $tokenService;
    private KeycloakUserService $userService;
    private KeycloakAdminService $adminService;
    private KeycloakRoleService $roleService;

    public function __construct(
        KeycloakTokenService $tokenService,
        KeycloakUserService $userService,
        KeycloakAdminService $adminService,
        KeycloakRoleService $roleService
    ) {
        $this->tokenService = $tokenService;
        $this->userService = $userService;
        $this->adminService = $adminService;
        $this->roleService = $roleService;
    }

    public function validateToken(string $token): object
    {
        return $this->tokenService->validateToken($token);
    }

    public function createKeycloakUser(string $name, string $email, string $password, array $roles = ['client']): array
    {
        return $this->userService->createUser($name, $email, $password, $roles);
    }

    public function refreshToken(string $refreshToken): ?array
    {
        try {
            $response = Http::asForm()->post(
                sprintf(
                    '%s/realms/%s/protocol/openid-connect/token',
                    config('services.keycloak.base_url'),
                    config('services.keycloak.realm')
                ),
                [
                    'grant_type' => 'refresh_token',
                    'client_id' => config('services.keycloak.client_id'),
                    'client_secret' => config('services.keycloak.client_secret'),
                    'refresh_token' => $refreshToken
                ]
            );

            if ($response->successful()) {
                return $response->json();
            }

            return null;
        } catch (Exception $e) {
            report($e);
            return null;
        }
    }

    /**
     * Add a role to a user in Keycloak
     *
     * @param string $keycloakId
     * @param string $roleName
     * @return bool
     */
    public function addRoleToUser(string $keycloakId, string $roleName): bool
    {
        return $this->roleService->addRoleToUser($keycloakId, $roleName);
    }

    /**
     * Remove a role from a user in Keycloak
     *
     * @param string $keycloakId
     * @param string $roleName
     * @return bool
     */
    public function removeRoleFromUser(string $keycloakId, string $roleName): bool
    {
        return $this->roleService->removeRoleFromUser($keycloakId, $roleName);
    }

    /**
     * Get user's roles from Keycloak
     *
     * @param string $keycloakId
     * @return array
     */
    public function getUserRoles(string $keycloakId): array
    {
        return $this->roleService->getUserRoles($keycloakId);
    }
}
