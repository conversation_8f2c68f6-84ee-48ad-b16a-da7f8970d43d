<?php

namespace App\Http\Controllers;

use App\Models\GroupeClient;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use App\Http\Requests\StoreGroupeClientRequest;
use App\Http\Requests\UpdateGroupeClientRequest;
use App\Http\Requests\AddUserToGroupeClientRequest;

class GroupeClientController extends Controller
{
    /**
     * Display a listing of all client groups.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        try {
            $groupesClients = GroupeClient::withCount('users')->get();
            return response()->json($groupesClients);
        } catch (\Exception $e) {
            return response()->json([
                "error" => "Problème de récupération des groupes de clients",
                "message" => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created client group in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(StoreGroupeClientRequest $request)
    {
        try {
            $validatedData = $request->validated();
            $groupeClient = GroupeClient::create($validatedData);
            return response()->json($groupeClient, 201);
        } catch (\Exception $e) {
            return response()->json([
                "error" => "Problème lors de la création du groupe de clients",
                "message" => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified client group.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $groupeClient = GroupeClient::with('users')->findOrFail($id);
            return response()->json($groupeClient);
        } catch (\Exception $e) {
            return response()->json([
                "error" => "Problème de récupération du groupe de clients",
                "message" => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified client group in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(UpdateGroupeClientRequest $request, $id)
    {
        try {
            $validatedData = $request->validated();
            $groupeClient = GroupeClient::findOrFail($id);
            $groupeClient->update($validatedData);
            return response()->json($groupeClient);
        } catch (\Exception $e) {
            return response()->json([
                "error" => "Problème lors de la mise à jour du groupe de clients",
                "message" => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified client group from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            // Start a transaction
            DB::beginTransaction();
            
            $groupeClient = GroupeClient::findOrFail($id);
            
            // Update all users associated with this client group
            User::where('groupe_client_id', $id)
                ->update([
                    'groupe_client_id' => null,
                    'type_client' => 'normal'
                ]);
            
            // Delete the client group
            $groupeClient->delete();
            
            // Commit the transaction
            DB::commit();
            
            return response()->json([
                "message" => "Groupe de clients supprimé avec succès"
            ]);
        } catch (\Exception $e) {
            // Rollback the transaction
            DB::rollBack();
            
            return response()->json([
                "error" => "Problème lors de la suppression du groupe de clients",
                "message" => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Add a user to a client group.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function addUser(AddUserToGroupeClientRequest $request, $id)
    {
        try {
            $validatedData = $request->validated();
            // Start a transaction
            DB::beginTransaction();
            // Find the client group
            $groupeClient = GroupeClient::findOrFail($id);
            // Find the user
            $user = User::findOrFail($validatedData['user_id']);
            // Update user
            $user->groupe_client_id = $id;
            $user->type_client = 'groupe';
            // Add client role if not already present
            $roles = $user->roles ?? [];
            if (!in_array('client', $roles)) {
                $roles[] = 'client';
                $user->roles = $roles;
            }
            $user->save();
            // Commit the transaction
            DB::commit();
            return response()->json([
                "message" => "Utilisateur ajouté au groupe de clients avec succès",
                "user" => $user
            ]);
        } catch (\Exception $e) {
            // Rollback the transaction
            DB::rollBack();
            return response()->json([
                "error" => "Problème lors de l'ajout de l'utilisateur au groupe de clients",
                "message" => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Remove a user from a client group.
     *
     * @param  int  $id
     * @param  int  $userId
     * @return \Illuminate\Http\JsonResponse
     */
    public function removeUser($id, $userId)
    {
        try {
            // Start a transaction
            DB::beginTransaction();
            
            // Find the client group
            $groupeClient = GroupeClient::findOrFail($id);
            
            // Find the user
            $user = User::where('id', $userId)
                ->where('groupe_client_id', $id)
                ->firstOrFail();
            
            // Update user
            $user->groupe_client_id = null;
            $user->type_client = 'normal';
            $user->save();
            
            // Commit the transaction
            DB::commit();
            
            return response()->json([
                "message" => "Utilisateur retiré du groupe de clients avec succès"
            ]);
        } catch (\Exception $e) {
            // Rollback the transaction
            DB::rollBack();
            
            return response()->json([
                "error" => "Problème lors du retrait de l'utilisateur du groupe de clients",
                "message" => $e->getMessage()
            ], 500);
        }
    }
}
