<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreDistributorRequestRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $user = $this->user();
        $rules = [
            'company_name' => 'required|string|max:255',
            'business_type' => 'nullable|string|max:255',
            'motivation' => 'nullable|string',
            'website' => 'nullable|url|max:255',
            'phone' => 'required|string|max:20',
            'address' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'required|string|max:255',
            'tax_id' => 'nullable|string|max:50',
            'registration_number' => 'nullable|string|max:50',
            'has_physical_store' => 'nullable|boolean',
            'years_in_business' => 'nullable|integer|min:0',
            'product_categories_interested' => 'nullable|string',
        ];
        if (!$user) {
            $rules['email'] = 'required|email|max:255';
            $rules['name'] = 'required|string|max:255';
        }
        return $rules;
    }

    public function messages(): array
    {
        return [
            'company_name.required' => 'Le nom de la société est obligatoire.',
            'phone.required' => 'Le numéro de téléphone est obligatoire.',
            'address.required' => "L'adresse est obligatoire.",
            'city.required' => 'La ville est obligatoire.',
            'country.required' => 'Le pays est obligatoire.',
            'email.required' => "L'email est obligatoire.",
            'email.email' => 'Le format de l\'email est invalide.',
            'name.required' => 'Le nom est obligatoire.',
        ];
    }
}
