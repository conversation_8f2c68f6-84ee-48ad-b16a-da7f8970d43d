<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Categorie;
use Illuminate\Support\Facades\DB;

class CategorieController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $categorie = Categorie::all();
        return response()->json($categorie);
    }

    /**
     * Show the form for creating a new resource.
     */

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            // ✅ Validation des données reçues
            $validatedData = $request->validate([
                "nom_categorie" => "required|string|max:255",
                "image_categorie" => "nullable|string",
                "description_categorie" => "nullable|string",
                "featured" => "nullable|boolean",
                "featured_order" => "nullable|integer|min:0"
            ]);

            // ✅ Création et sauvegarde de la catégorie
            $categorie = Categorie::create($validatedData);

            // ✅ Retour avec une réponse JSON
            return response()->json([
                "categorie" => $categorie
            ], 201); // Code HTTP 201 pour une création réussie

        } catch (\Exception $e) {
            // ✅ Gestion d'erreurs avec un message d'erreur détaillé
            return response()->json([
                "error" => "Insertion impossible",
                "message" => $e->getMessage()
            ], 500); // Code HTTP 500 en cas d'erreur interne
        }
    }


    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
            $categorie = Categorie::findOrFail($id);
            return response()->json($categorie);
        } catch (\Exception $e) {
            return response()->json(["error" => "probleme de récupération des données {$e->getMessage()}"]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */


    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $categorie = Categorie::findOrFail($id);
            $categorie->nom_categorie = $request->input("nom_categorie");
            $categorie->image_categorie = $request->input("image_categorie");
            $categorie->description_categorie = $request->input("description_categorie");

            // Mettre à jour les champs featured si fournis
            if ($request->has('featured')) {
                $categorie->featured = $request->boolean('featured');
            }

            if ($request->has('featured_order')) {
                $categorie->featured_order = $request->input('featured_order');
            }

            $categorie->save();
            return response()->json($categorie);
        } catch (\Exception $e) {
            return response()->json(["error" => "probleme de modification {$e->getMessage()}"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $categorie = Categorie::findOrFail($id);
            $categorie->delete();
            return response()->json(["message" => "Categorie supprimée avec succès"], 200);
        } catch (\Exception $e) {
            return response()->json(["error" => "probleme de suppression de Categorie {$e->getMessage()}"]);
        }
    }

    /**
     * Get all featured categories.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFeaturedCategories()
    {
        try {
            $categories = Categorie::featured()
                ->with('images')
                ->get();

            return response()->json($categories);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erreur lors de la récupération des catégories mises en avant',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Set a category as featured or not.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function setFeatured(Request $request, string $id)
    {
        try {
            $request->validate([
                'featured' => 'required|boolean',
                'featured_order' => 'nullable|integer|min:0',
            ]);

            DB::beginTransaction();

            $categorie = Categorie::findOrFail($id);
            $categorie->featured = $request->boolean('featured');

            if ($request->has('featured_order')) {
                $categorie->featured_order = $request->input('featured_order');
            } else if ($request->boolean('featured') && !$categorie->featured_order) {
                // Si on met en avant la catégorie et qu'aucun ordre n'est spécifié,
                // on lui attribue le dernier ordre + 1
                $maxOrder = Categorie::where('featured', true)->max('featured_order') ?? 0;
                $categorie->featured_order = $maxOrder + 1;
            }

            $categorie->save();

            DB::commit();

            return response()->json($categorie);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'error' => 'Erreur lors de la mise à jour du statut featured',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reorder featured categories.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function reorderFeatured(Request $request)
    {
        try {
            $request->validate([
                'categories' => 'required|array',
                'categories.*.id' => 'required|exists:categories,id',
                'categories.*.featured_order' => 'required|integer|min:0',
            ]);

            DB::beginTransaction();

            foreach ($request->input('categories') as $categoryData) {
                $category = Categorie::findOrFail($categoryData['id']);
                $category->featured_order = $categoryData['featured_order'];
                $category->featured = true; // Assurer que la catégorie est marquée comme featured
                $category->save();
            }

            DB::commit();

            // Récupérer les catégories mises à jour
            $categories = Categorie::featured()->get();

            return response()->json($categories);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'error' => 'Erreur lors de la réorganisation des catégories mises en avant',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
