<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('paiements', function (Blueprint $table) {
            // Add currency conversion fields
            $table->string('original_currency', 3)->default('TND')->after('montant');
            $table->decimal('converted_amount', 10, 2)->nullable()->after('original_currency');
            $table->string('stripe_currency', 3)->nullable()->after('converted_amount');
            $table->decimal('exchange_rate', 10, 6)->nullable()->after('stripe_currency');
            $table->timestamp('conversion_timestamp')->nullable()->after('exchange_rate');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('paiements', function (Blueprint $table) {
            $table->dropColumn([
                'original_currency',
                'converted_amount', 
                'stripe_currency',
                'exchange_rate',
                'conversion_timestamp'
            ]);
        });
    }
};
