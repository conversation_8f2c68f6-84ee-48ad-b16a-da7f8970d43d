name: Endpoints
description: ''
endpoints:
  -
    httpMethods:
      - POST
    uri: api/auth/verify
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Verify tokens from frontend and set cookies'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      access_token:
        name: access_token
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      refresh_token:
        name: refresh_token
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      id_token:
        name: id_token
        description: ''
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      access_token: architecto
      refresh_token: architecto
      id_token: architecto
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/auth/logout
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Log the user out'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/auth/refresh
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Refresh token endpoint'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/auth/user
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get the current authenticated user'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"error":"Unauthenticated"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/marques
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '[{"id":5,"nom_marque":"J-Line","logo_marque":"https:\/\/encrypted-tbn0.gstatic.com\/images?q=tbn:ANd9GcQgl6_NVCEHJvlzCBvv7ZAXnvszQc6S0PuQmA&s","description_marque":"J-line by Jolipa - the trendsetter par excellence in interiors and decoration","created_at":"2025-03-25T22:42:10.000000Z","updated_at":"2025-03-25T22:42:10.000000Z"},{"id":6,"nom_marque":"Andrea House","logo_marque":"https:\/\/andrea-house.com\/img\/andreahouseweb-logo-1528187408.jpg","description_marque":"Elegant and minimalist home decor designed to elevate everyday living","created_at":"2025-03-25T22:54:57.000000Z","updated_at":"2025-03-25T22:54:57.000000Z"},{"id":7,"nom_marque":"KARE Design","logo_marque":"https:\/\/assets.wfcdn.com\/im\/49601032\/resize-h110-w290%5Ecompr-r85\/1764\/176469461\/default_name.jpg","description_marque":"A daring fusion of art and furniture, bringing originality and character to every space","created_at":"2025-03-25T22:58:17.000000Z","updated_at":"2025-03-25T22:58:17.000000Z"},{"id":8,"nom_marque":"Carr\u00e9 Blanc","logo_marque":"https:\/\/seeklogo.com\/images\/C\/carre-blanc-logo-9A253FBFA2-seeklogo.com.png","description_marque":"Refined and creative home linens that blend comfort with French elegance","created_at":"2025-03-25T23:02:14.000000Z","updated_at":"2025-03-25T23:02:14.000000Z"}]'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/marques
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/marques/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the marque.'
        required: true
        example: 5
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 5
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"id":5,"nom_marque":"J-Line","logo_marque":"https:\/\/encrypted-tbn0.gstatic.com\/images?q=tbn:ANd9GcQgl6_NVCEHJvlzCBvv7ZAXnvszQc6S0PuQmA&s","description_marque":"J-line by Jolipa - the trendsetter par excellence in interiors and decoration","created_at":"2025-03-25T22:42:10.000000Z","updated_at":"2025-03-25T22:42:10.000000Z"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/marques/{id}/produits'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the marque.'
        required: true
        example: 5
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 5
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '[{"id":18,"nom_produit":"Parure de lit classy","description_produit":"Parure de lit classy test by chaima","image_produit":"https:\/\/via.placeholder.com\/800x600.png?text=Produit+Image","prix_produit":120,"quantite_produit":1,"marque_id":5,"sous_sous_categorie_id":4,"created_at":"2025-04-15T17:34:26.000000Z","updated_at":"2025-04-18T23:03:21.000000Z","reference":"5-6"},{"id":24,"nom_produit":"Housse de couette","description_produit":"Housse ce couette coton","image_produit":null,"prix_produit":36,"quantite_produit":20,"marque_id":5,"sous_sous_categorie_id":4,"created_at":"2025-04-27T13:10:27.000000Z","updated_at":"2025-04-27T13:10:27.000000Z","reference":"5-23"}]'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/marques/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the marque.'
        required: true
        example: 5
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 5
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/marques/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the marque.'
        required: true
        example: 5
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 5
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/produits
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Afficher la liste des produits avec filtres et pagination'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"current_page":1,"data":[{"id":3,"nom_produit":"Parure de lit \u00c9l\u00e9gance","description_produit":"Parure de lit en coton \u00e9gyptien, douce et \u00e9l\u00e9gante pour un sommeil r\u00e9parateur","image_produit":"https:\/\/www.carreblanc.com\/media\/catalog\/product\/cache\/c687aa7517cf01e65c009f6943c2b1e9\/2\/0\/2023-parure-housse-de-couette-percale-coton-blanc-brode-elegance_1.jpg","prix_produit":129.99,"quantite_produit":50,"marque_id":8,"sous_sous_categorie_id":4,"created_at":"2025-04-09T20:51:37.000000Z","updated_at":"2025-04-09T20:51:37.000000Z","reference":"8-1"},{"id":4,"nom_produit":"Parure de lit Satin Luxe","description_produit":"Parure de lit en satin de coton, luxueuse et brillante pour un confort optimal","image_produit":"https:\/\/www.carreblanc.com\/media\/catalog\/product\/cache\/c687aa7517cf01e65c009f6943c2b1e9\/2\/0\/2023-parure-housse-de-couette-satin-coton-blanc-satin-luxe_1.jpg","prix_produit":159.99,"quantite_produit":30,"marque_id":8,"sous_sous_categorie_id":4,"created_at":"2025-04-09T20:51:40.000000Z","updated_at":"2025-04-09T20:51:40.000000Z","reference":"8-4"},{"id":5,"nom_produit":"Parure de lit Cosy","description_produit":"Parure de lit en flanelle, chaude et douce pour les nuits d''hiver","image_produit":"https:\/\/www.carreblanc.com\/media\/catalog\/product\/cache\/c687aa7517cf01e65c009f6943c2b1e9\/2\/0\/2023-parure-housse-de-couette-flanelle-gris-cosy_1.jpg","prix_produit":99.99,"quantite_produit":40,"marque_id":8,"sous_sous_categorie_id":4,"created_at":"2025-04-09T20:51:43.000000Z","updated_at":"2025-04-09T20:51:43.000000Z","reference":"8-5"},{"id":18,"nom_produit":"Parure de lit classy","description_produit":"Parure de lit classy test by chaima","image_produit":"https:\/\/via.placeholder.com\/800x600.png?text=Produit+Image","prix_produit":120,"quantite_produit":1,"marque_id":5,"sous_sous_categorie_id":4,"created_at":"2025-04-15T17:34:26.000000Z","updated_at":"2025-04-18T23:03:21.000000Z","reference":"5-6"},{"id":22,"nom_produit":"Fauteuil Scandinave Gris","description_produit":"\u00c9l\u00e9gant fauteuil de style scandinave avec accoudoirs, parfait pour votre salon ou bureau. Structure en bois de ch\u00eane massif et assise en tissu polyester de haute qualit\u00e9. Design \u00e9pur\u00e9 et confortable qui s''int\u00e8gre parfaitement dans tous les int\u00e9rieurs modernes.","image_produit":"[]","prix_produit":249.99,"quantite_produit":15,"marque_id":6,"sous_sous_categorie_id":4,"created_at":"2025-04-22T10:52:13.000000Z","updated_at":"2025-04-27T12:26:51.000000Z","reference":"6-19"},{"id":24,"nom_produit":"Housse de couette","description_produit":"Housse ce couette coton","image_produit":null,"prix_produit":36,"quantite_produit":20,"marque_id":5,"sous_sous_categorie_id":4,"created_at":"2025-04-27T13:10:27.000000Z","updated_at":"2025-04-27T13:10:27.000000Z","reference":"5-23"}],"first_page_url":"http:\/\/0.0.0.0:8000\/api\/produits?page=1","from":1,"last_page":1,"last_page_url":"http:\/\/0.0.0.0:8000\/api\/produits?page=1","links":[{"url":null,"label":"&laquo; Previous","active":false},{"url":"http:\/\/0.0.0.0:8000\/api\/produits?page=1","label":"1","active":true},{"url":null,"label":"Next &raquo;","active":false}],"next_page_url":null,"path":"http:\/\/0.0.0.0:8000\/api\/produits","per_page":15,"prev_page_url":null,"to":6,"total":6}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/produits
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Store a newly created resource in storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/produits/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified resource.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the produit.'
        required: true
        example: 3
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 3
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"id":3,"nom_produit":"Parure de lit \u00c9l\u00e9gance","description_produit":"Parure de lit en coton \u00e9gyptien, douce et \u00e9l\u00e9gante pour un sommeil r\u00e9parateur","image_produit":"https:\/\/www.carreblanc.com\/media\/catalog\/product\/cache\/c687aa7517cf01e65c009f6943c2b1e9\/2\/0\/2023-parure-housse-de-couette-percale-coton-blanc-brode-elegance_1.jpg","prix_produit":129.99,"quantite_produit":50,"marque_id":8,"sous_sous_categorie_id":4,"created_at":"2025-04-09T20:51:37.000000Z","updated_at":"2025-04-09T20:51:37.000000Z","reference":"8-1"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/produits/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update the specified resource in storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the produit.'
        required: true
        example: 3
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 3
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/produits/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove the specified resource from storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the produit.'
        required: true
        example: 3
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 3
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/produits/{id}/attributs'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Récupérer les attributs d'un produit (nouveau système)"
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the produit.'
        required: true
        example: 3
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 3
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '[{"id":3,"attribut_id":3,"attribut":{"id":3,"nom":"Couleur","description":"Couleur du produit","type_valeur":"texte","groupe":{"id":2,"nom":"Caract\u00e9ristiques g\u00e9n\u00e9rales"}},"valeur":"Blanc"},{"id":4,"attribut_id":4,"attribut":{"id":4,"nom":"Taille","description":"Taille du produit","type_valeur":"texte","groupe":{"id":2,"nom":"Caract\u00e9ristiques g\u00e9n\u00e9rales"}},"valeur":"240x220 cm"},{"id":5,"attribut_id":5,"attribut":{"id":5,"nom":"Materiau","description":"Materiau principal du produit","type_valeur":"texte","groupe":{"id":2,"nom":"Caract\u00e9ristiques g\u00e9n\u00e9rales"}},"valeur":"Coton \u00e9gyptien"},{"id":6,"attribut_id":6,"attribut":{"id":6,"nom":"Dimensions","description":"Dimensions du produit (L x l x H)","type_valeur":"texte","groupe":{"id":2,"nom":"Caract\u00e9ristiques g\u00e9n\u00e9rales"}},"valeur":"240x220 cm"},{"id":15,"attribut_id":7,"attribut":{"id":7,"nom":"Fils au cm\u00b2","description":"Nombre de fils au centim\u00e8tre carr\u00e9 (qualit\u00e9 du tissu)","type_valeur":"nombre","groupe":{"id":3,"nom":"Caract\u00e9ristiques du linge de lit"}},"valeur":"80.000000"},{"id":16,"attribut_id":8,"attribut":{"id":8,"nom":"Entretien","description":"Instructions d''entretien du produit","type_valeur":"texte","groupe":{"id":3,"nom":"Caract\u00e9ristiques du linge de lit"}},"valeur":"Lavable en machine \u00e0 40\u00b0C, ne pas utiliser d''eau de javel, s\u00e9chage \u00e0 basse temp\u00e9rature"},{"id":17,"attribut_id":9,"attribut":{"id":9,"nom":"Certification","description":"Certifications et labels du produit","type_valeur":"texte","groupe":{"id":3,"nom":"Caract\u00e9ristiques du linge de lit"}},"valeur":"OEKO-TEX Standard 100, Coton biologique"},{"id":18,"attribut_id":10,"attribut":{"id":10,"nom":"Saison","description":"Saison recommand\u00e9e pour l''utilisation","type_valeur":"texte","groupe":{"id":3,"nom":"Caract\u00e9ristiques du linge de lit"}},"valeur":"Toutes saisons"},{"id":19,"attribut_id":11,"attribut":{"id":11,"nom":"Contenu du set","description":"\u00c9l\u00e9ments inclus dans le set","type_valeur":"texte","groupe":{"id":3,"nom":"Caract\u00e9ristiques du linge de lit"}},"valeur":"1 housse de couette 240x220 cm, 2 taies d''oreiller 65x65 cm"},{"id":39,"attribut_id":12,"attribut":{"id":12,"nom":"Empreinte carbone","description":"Empreinte carbone du produit","type_valeur":"texte","groupe":{"id":4,"nom":"D\u00e9veloppement durable"}},"valeur":"Faible - 2.5 kg CO2e"},{"id":40,"attribut_id":13,"attribut":{"id":13,"nom":"Origine","description":"Pays d''origine du produit","type_valeur":"texte","groupe":{"id":4,"nom":"D\u00e9veloppement durable"}},"valeur":"Portugal"},{"id":41,"attribut_id":14,"attribut":{"id":14,"nom":"Recyclable","description":"Indique si le produit est recyclable","type_valeur":"booleen","groupe":{"id":4,"nom":"D\u00e9veloppement durable"}},"valeur":true}]'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/produits/{id}/variantes'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display a listing of the resource.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the produit.'
        required: true
        example: 3
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 3
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '[{"id":2,"produit_parent_id":3,"sku":"ELEGANCE-BLANC-140","prix_supplement":"0.00","stock":20,"actif":true,"created_at":"2025-04-09T21:08:14.000000Z","updated_at":"2025-04-09T21:08:14.000000Z","deleted_at":null,"valeurs":[{"id":2,"produit_variante_id":2,"attribut_id":3,"valeur_texte":"Blanc","valeur_nombre":null,"valeur_date":null,"valeur_booleen":null,"created_at":"2025-04-09T21:08:15.000000Z","updated_at":"2025-04-09T21:08:15.000000Z","attribut":{"id":3,"nom":"Couleur","description":"Couleur du produit","type_valeur":"texte","groupe_id":2,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-08T12:43:28.000000Z","updated_at":"2025-04-08T12:43:28.000000Z","deleted_at":null}},{"id":3,"produit_variante_id":2,"attribut_id":4,"valeur_texte":"140x200 cm","valeur_nombre":null,"valeur_date":null,"valeur_booleen":null,"created_at":"2025-04-09T21:08:15.000000Z","updated_at":"2025-04-09T21:08:15.000000Z","attribut":{"id":4,"nom":"Taille","description":"Taille du produit","type_valeur":"texte","groupe_id":2,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-09T20:37:11.000000Z","updated_at":"2025-04-09T20:37:11.000000Z","deleted_at":null}}]},{"id":3,"produit_parent_id":3,"sku":"ELEGANCE-BLANC-160","prix_supplement":"10.00","stock":15,"actif":true,"created_at":"2025-04-09T21:08:16.000000Z","updated_at":"2025-04-09T21:08:16.000000Z","deleted_at":null,"valeurs":[{"id":4,"produit_variante_id":3,"attribut_id":3,"valeur_texte":"Blanc","valeur_nombre":null,"valeur_date":null,"valeur_booleen":null,"created_at":"2025-04-09T21:08:16.000000Z","updated_at":"2025-04-09T21:08:16.000000Z","attribut":{"id":3,"nom":"Couleur","description":"Couleur du produit","type_valeur":"texte","groupe_id":2,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-08T12:43:28.000000Z","updated_at":"2025-04-08T12:43:28.000000Z","deleted_at":null}},{"id":5,"produit_variante_id":3,"attribut_id":4,"valeur_texte":"160x200 cm","valeur_nombre":null,"valeur_date":null,"valeur_booleen":null,"created_at":"2025-04-09T21:08:17.000000Z","updated_at":"2025-04-09T21:08:17.000000Z","attribut":{"id":4,"nom":"Taille","description":"Taille du produit","type_valeur":"texte","groupe_id":2,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-09T20:37:11.000000Z","updated_at":"2025-04-09T20:37:11.000000Z","deleted_at":null}}]},{"id":4,"produit_parent_id":3,"sku":"ELEGANCE-BLANC-180","prix_supplement":"20.00","stock":10,"actif":true,"created_at":"2025-04-09T21:08:17.000000Z","updated_at":"2025-04-09T21:08:17.000000Z","deleted_at":null,"valeurs":[{"id":6,"produit_variante_id":4,"attribut_id":3,"valeur_texte":"Blanc","valeur_nombre":null,"valeur_date":null,"valeur_booleen":null,"created_at":"2025-04-09T21:08:18.000000Z","updated_at":"2025-04-09T21:08:18.000000Z","attribut":{"id":3,"nom":"Couleur","description":"Couleur du produit","type_valeur":"texte","groupe_id":2,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-08T12:43:28.000000Z","updated_at":"2025-04-08T12:43:28.000000Z","deleted_at":null}},{"id":7,"produit_variante_id":4,"attribut_id":4,"valeur_texte":"180x200 cm","valeur_nombre":null,"valeur_date":null,"valeur_booleen":null,"created_at":"2025-04-09T21:08:18.000000Z","updated_at":"2025-04-09T21:08:18.000000Z","attribut":{"id":4,"nom":"Taille","description":"Taille du produit","type_valeur":"texte","groupe_id":2,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-09T20:37:11.000000Z","updated_at":"2025-04-09T20:37:11.000000Z","deleted_at":null}}]},{"id":5,"produit_parent_id":3,"sku":"ELEGANCE-BLANC-220","prix_supplement":"30.00","stock":5,"actif":true,"created_at":"2025-04-09T21:08:19.000000Z","updated_at":"2025-04-09T21:08:19.000000Z","deleted_at":null,"valeurs":[{"id":8,"produit_variante_id":5,"attribut_id":3,"valeur_texte":"Blanc","valeur_nombre":null,"valeur_date":null,"valeur_booleen":null,"created_at":"2025-04-09T21:08:19.000000Z","updated_at":"2025-04-09T21:08:19.000000Z","attribut":{"id":3,"nom":"Couleur","description":"Couleur du produit","type_valeur":"texte","groupe_id":2,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-08T12:43:28.000000Z","updated_at":"2025-04-08T12:43:28.000000Z","deleted_at":null}},{"id":9,"produit_variante_id":5,"attribut_id":4,"valeur_texte":"220x240 cm","valeur_nombre":null,"valeur_date":null,"valeur_booleen":null,"created_at":"2025-04-09T21:08:19.000000Z","updated_at":"2025-04-09T21:08:19.000000Z","attribut":{"id":4,"nom":"Taille","description":"Taille du produit","type_valeur":"texte","groupe_id":2,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-09T20:37:11.000000Z","updated_at":"2025-04-09T20:37:11.000000Z","deleted_at":null}}]},{"id":6,"produit_parent_id":3,"sku":"ELEGANCE-BEIGE-140","prix_supplement":"0.00","stock":18,"actif":true,"created_at":"2025-04-09T21:08:20.000000Z","updated_at":"2025-04-09T21:08:20.000000Z","deleted_at":null,"valeurs":[{"id":10,"produit_variante_id":6,"attribut_id":3,"valeur_texte":"Beige","valeur_nombre":null,"valeur_date":null,"valeur_booleen":null,"created_at":"2025-04-09T21:08:20.000000Z","updated_at":"2025-04-09T21:08:20.000000Z","attribut":{"id":3,"nom":"Couleur","description":"Couleur du produit","type_valeur":"texte","groupe_id":2,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-08T12:43:28.000000Z","updated_at":"2025-04-08T12:43:28.000000Z","deleted_at":null}},{"id":11,"produit_variante_id":6,"attribut_id":4,"valeur_texte":"140x200 cm","valeur_nombre":null,"valeur_date":null,"valeur_booleen":null,"created_at":"2025-04-09T21:08:21.000000Z","updated_at":"2025-04-09T21:08:21.000000Z","attribut":{"id":4,"nom":"Taille","description":"Taille du produit","type_valeur":"texte","groupe_id":2,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-09T20:37:11.000000Z","updated_at":"2025-04-09T20:37:11.000000Z","deleted_at":null}}]},{"id":7,"produit_parent_id":3,"sku":"ELEGANCE-BEIGE-160","prix_supplement":"10.00","stock":12,"actif":true,"created_at":"2025-04-09T21:08:21.000000Z","updated_at":"2025-04-09T21:08:21.000000Z","deleted_at":null,"valeurs":[{"id":12,"produit_variante_id":7,"attribut_id":3,"valeur_texte":"Beige","valeur_nombre":null,"valeur_date":null,"valeur_booleen":null,"created_at":"2025-04-09T21:08:22.000000Z","updated_at":"2025-04-09T21:08:22.000000Z","attribut":{"id":3,"nom":"Couleur","description":"Couleur du produit","type_valeur":"texte","groupe_id":2,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-08T12:43:28.000000Z","updated_at":"2025-04-08T12:43:28.000000Z","deleted_at":null}},{"id":13,"produit_variante_id":7,"attribut_id":4,"valeur_texte":"160x200 cm","valeur_nombre":null,"valeur_date":null,"valeur_booleen":null,"created_at":"2025-04-09T21:08:22.000000Z","updated_at":"2025-04-09T21:08:22.000000Z","attribut":{"id":4,"nom":"Taille","description":"Taille du produit","type_valeur":"texte","groupe_id":2,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-09T20:37:11.000000Z","updated_at":"2025-04-09T20:37:11.000000Z","deleted_at":null}}]},{"id":8,"produit_parent_id":3,"sku":"ELEGANCE-BEIGE-180","prix_supplement":"20.00","stock":8,"actif":true,"created_at":"2025-04-09T21:08:23.000000Z","updated_at":"2025-04-09T21:08:23.000000Z","deleted_at":null,"valeurs":[{"id":14,"produit_variante_id":8,"attribut_id":3,"valeur_texte":"Beige","valeur_nombre":null,"valeur_date":null,"valeur_booleen":null,"created_at":"2025-04-09T21:08:23.000000Z","updated_at":"2025-04-09T21:08:23.000000Z","attribut":{"id":3,"nom":"Couleur","description":"Couleur du produit","type_valeur":"texte","groupe_id":2,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-08T12:43:28.000000Z","updated_at":"2025-04-08T12:43:28.000000Z","deleted_at":null}},{"id":15,"produit_variante_id":8,"attribut_id":4,"valeur_texte":"180x200 cm","valeur_nombre":null,"valeur_date":null,"valeur_booleen":null,"created_at":"2025-04-09T21:08:23.000000Z","updated_at":"2025-04-09T21:08:23.000000Z","attribut":{"id":4,"nom":"Taille","description":"Taille du produit","type_valeur":"texte","groupe_id":2,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-09T20:37:11.000000Z","updated_at":"2025-04-09T20:37:11.000000Z","deleted_at":null}}]},{"id":9,"produit_parent_id":3,"sku":"ELEGANCE-BEIGE-220","prix_supplement":"30.00","stock":4,"actif":true,"created_at":"2025-04-09T21:08:24.000000Z","updated_at":"2025-04-09T21:08:24.000000Z","deleted_at":null,"valeurs":[{"id":16,"produit_variante_id":9,"attribut_id":3,"valeur_texte":"Beige","valeur_nombre":null,"valeur_date":null,"valeur_booleen":null,"created_at":"2025-04-09T21:08:24.000000Z","updated_at":"2025-04-09T21:08:24.000000Z","attribut":{"id":3,"nom":"Couleur","description":"Couleur du produit","type_valeur":"texte","groupe_id":2,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-08T12:43:28.000000Z","updated_at":"2025-04-08T12:43:28.000000Z","deleted_at":null}},{"id":17,"produit_variante_id":9,"attribut_id":4,"valeur_texte":"220x240 cm","valeur_nombre":null,"valeur_date":null,"valeur_booleen":null,"created_at":"2025-04-09T21:08:25.000000Z","updated_at":"2025-04-09T21:08:25.000000Z","attribut":{"id":4,"nom":"Taille","description":"Taille du produit","type_valeur":"texte","groupe_id":2,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-09T20:37:11.000000Z","updated_at":"2025-04-09T20:37:11.000000Z","deleted_at":null}}]},{"id":10,"produit_parent_id":3,"sku":"ELEGANCE-GRIS-140","prix_supplement":"0.00","stock":15,"actif":true,"created_at":"2025-04-09T21:08:25.000000Z","updated_at":"2025-04-09T21:08:25.000000Z","deleted_at":null,"valeurs":[{"id":18,"produit_variante_id":10,"attribut_id":3,"valeur_texte":"Gris clair","valeur_nombre":null,"valeur_date":null,"valeur_booleen":null,"created_at":"2025-04-09T21:08:26.000000Z","updated_at":"2025-04-09T21:08:26.000000Z","attribut":{"id":3,"nom":"Couleur","description":"Couleur du produit","type_valeur":"texte","groupe_id":2,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-08T12:43:28.000000Z","updated_at":"2025-04-08T12:43:28.000000Z","deleted_at":null}},{"id":19,"produit_variante_id":10,"attribut_id":4,"valeur_texte":"140x200 cm","valeur_nombre":null,"valeur_date":null,"valeur_booleen":null,"created_at":"2025-04-09T21:08:26.000000Z","updated_at":"2025-04-09T21:08:26.000000Z","attribut":{"id":4,"nom":"Taille","description":"Taille du produit","type_valeur":"texte","groupe_id":2,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-09T20:37:11.000000Z","updated_at":"2025-04-09T20:37:11.000000Z","deleted_at":null}}]},{"id":11,"produit_parent_id":3,"sku":"ELEGANCE-GRIS-160","prix_supplement":"10.00","stock":10,"actif":true,"created_at":"2025-04-09T21:08:27.000000Z","updated_at":"2025-04-09T21:08:27.000000Z","deleted_at":null,"valeurs":[{"id":20,"produit_variante_id":11,"attribut_id":3,"valeur_texte":"Gris clair","valeur_nombre":null,"valeur_date":null,"valeur_booleen":null,"created_at":"2025-04-09T21:08:27.000000Z","updated_at":"2025-04-09T21:08:27.000000Z","attribut":{"id":3,"nom":"Couleur","description":"Couleur du produit","type_valeur":"texte","groupe_id":2,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-08T12:43:28.000000Z","updated_at":"2025-04-08T12:43:28.000000Z","deleted_at":null}},{"id":21,"produit_variante_id":11,"attribut_id":4,"valeur_texte":"160x200 cm","valeur_nombre":null,"valeur_date":null,"valeur_booleen":null,"created_at":"2025-04-09T21:08:28.000000Z","updated_at":"2025-04-09T21:08:28.000000Z","attribut":{"id":4,"nom":"Taille","description":"Taille du produit","type_valeur":"texte","groupe_id":2,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-09T20:37:11.000000Z","updated_at":"2025-04-09T20:37:11.000000Z","deleted_at":null}}]},{"id":12,"produit_parent_id":3,"sku":"ELEGANCE-GRIS-180","prix_supplement":"20.00","stock":5,"actif":true,"created_at":"2025-04-09T21:08:28.000000Z","updated_at":"2025-04-09T21:08:28.000000Z","deleted_at":null,"valeurs":[{"id":22,"produit_variante_id":12,"attribut_id":3,"valeur_texte":"Gris clair","valeur_nombre":null,"valeur_date":null,"valeur_booleen":null,"created_at":"2025-04-09T21:08:28.000000Z","updated_at":"2025-04-09T21:08:28.000000Z","attribut":{"id":3,"nom":"Couleur","description":"Couleur du produit","type_valeur":"texte","groupe_id":2,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-08T12:43:28.000000Z","updated_at":"2025-04-08T12:43:28.000000Z","deleted_at":null}},{"id":23,"produit_variante_id":12,"attribut_id":4,"valeur_texte":"180x200 cm","valeur_nombre":null,"valeur_date":null,"valeur_booleen":null,"created_at":"2025-04-09T21:08:29.000000Z","updated_at":"2025-04-09T21:08:29.000000Z","attribut":{"id":4,"nom":"Taille","description":"Taille du produit","type_valeur":"texte","groupe_id":2,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-09T20:37:11.000000Z","updated_at":"2025-04-09T20:37:11.000000Z","deleted_at":null}}]},{"id":13,"produit_parent_id":3,"sku":"ELEGANCE-GRIS-220","prix_supplement":"30.00","stock":3,"actif":true,"created_at":"2025-04-09T21:08:29.000000Z","updated_at":"2025-04-09T21:08:29.000000Z","deleted_at":null,"valeurs":[{"id":24,"produit_variante_id":13,"attribut_id":3,"valeur_texte":"Gris clair","valeur_nombre":null,"valeur_date":null,"valeur_booleen":null,"created_at":"2025-04-09T21:08:30.000000Z","updated_at":"2025-04-09T21:08:30.000000Z","attribut":{"id":3,"nom":"Couleur","description":"Couleur du produit","type_valeur":"texte","groupe_id":2,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-08T12:43:28.000000Z","updated_at":"2025-04-08T12:43:28.000000Z","deleted_at":null}},{"id":25,"produit_variante_id":13,"attribut_id":4,"valeur_texte":"220x240 cm","valeur_nombre":null,"valeur_date":null,"valeur_booleen":null,"created_at":"2025-04-09T21:08:30.000000Z","updated_at":"2025-04-09T21:08:30.000000Z","attribut":{"id":4,"nom":"Taille","description":"Taille du produit","type_valeur":"texte","groupe_id":2,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-09T20:37:11.000000Z","updated_at":"2025-04-09T20:37:11.000000Z","deleted_at":null}}]}]'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/attributs/filtrables
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Récupérer les attributs filtrables pour le frontoffice'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '[{"id":2,"nom":"Caract\u00e9ristiques g\u00e9n\u00e9rales","attributs":[{"id":3,"nom":"Couleur","description":"Couleur du produit","type_valeur":"texte","groupe":{"id":2,"nom":"Caract\u00e9ristiques g\u00e9n\u00e9rales"},"valeurs_disponibles":["Blanc","Blanc cass\u00e9","Gris","Gris perle"]},{"id":6,"nom":"Dimensions","description":"Dimensions du produit (L x l x H)","type_valeur":"texte","groupe":{"id":2,"nom":"Caract\u00e9ristiques g\u00e9n\u00e9rales"},"valeurs_disponibles":["240x220 cm"]},{"id":5,"nom":"Materiau","description":"Materiau principal du produit","type_valeur":"texte","groupe":{"id":2,"nom":"Caract\u00e9ristiques g\u00e9n\u00e9rales"},"valeurs_disponibles":["Coton peign\u00e9","Coton \u00e9gyptien","Flanelle","Satin de coton"]},{"id":4,"nom":"Taille","description":"Taille du produit","type_valeur":"texte","groupe":{"id":2,"nom":"Caract\u00e9ristiques g\u00e9n\u00e9rales"},"valeurs_disponibles":["240x220 cm"]}]},{"id":3,"nom":"Caract\u00e9ristiques du linge de lit","attributs":[{"id":9,"nom":"Certification","description":"Certifications et labels du produit","type_valeur":"texte","groupe":{"id":3,"nom":"Caract\u00e9ristiques du linge de lit"},"valeurs_disponibles":["OEKO-TEX Standard 100","OEKO-TEX Standard 100, Commerce \u00e9quitable","OEKO-TEX Standard 100, Coton biologique","OEKO-TEX Standard 100, Fabrication \u00e9thique"]},{"id":11,"nom":"Contenu du set","description":"\u00c9l\u00e9ments inclus dans le set","type_valeur":"texte","groupe":{"id":3,"nom":"Caract\u00e9ristiques du linge de lit"},"valeurs_disponibles":["1 housse de couette 240x220 cm, 2 taies d''oreiller 65x65 cm","1 housse de couette 240x220 cm, 2 taies d''oreiller 65x65 cm, 1 drap-housse 160x200 cm","1 housse de couette 240x220 cm, 2 taies d''oreiller 65x65 cm, 2 taies d''oreiller d\u00e9coratives 40x40 cm"]},{"id":8,"nom":"Entretien","description":"Instructions d''entretien du produit","type_valeur":"texte","groupe":{"id":3,"nom":"Caract\u00e9ristiques du linge de lit"},"valeurs_disponibles":["Lavable en machine \u00e0 30\u00b0C, s\u00e9chage \u00e0 basse temp\u00e9rature, ne pas repasser","Lavable en machine \u00e0 40\u00b0C, ne pas utiliser d''eau de javel, s\u00e9chage \u00e0 basse temp\u00e9rature","Lavable en machine \u00e0 40\u00b0C, repassage facile","Lavage d\u00e9licat \u00e0 30\u00b0C, repassage \u00e0 basse temp\u00e9rature, nettoyage \u00e0 sec recommand\u00e9"]},{"id":7,"nom":"Fils au cm\u00b2","description":"Nombre de fils au centim\u00e8tre carr\u00e9 (qualit\u00e9 du tissu)","type_valeur":"nombre","groupe":{"id":3,"nom":"Caract\u00e9ristiques du linge de lit"},"valeurs_disponibles":{"min":60,"max":120}},{"id":10,"nom":"Saison","description":"Saison recommand\u00e9e pour l''utilisation","type_valeur":"texte","groupe":{"id":3,"nom":"Caract\u00e9ristiques du linge de lit"},"valeurs_disponibles":["Automne\/Hiver","Printemps\/\u00c9t\u00e9","Toutes saisons"]}]},{"id":4,"nom":"D\u00e9veloppement durable","attributs":[{"id":12,"nom":"Empreinte carbone","description":"Empreinte carbone du produit","type_valeur":"texte","groupe":{"id":4,"nom":"D\u00e9veloppement durable"},"valeurs_disponibles":["Faible - 2.2 kg CO2e","Faible - 2.5 kg CO2e","Moyenne - 3.1 kg CO2e","Moyenne - 3.8 kg CO2e"]},{"id":13,"nom":"Origine","description":"Pays d''origine du produit","type_valeur":"texte","groupe":{"id":4,"nom":"D\u00e9veloppement durable"},"valeurs_disponibles":["Espagne","France","Italie","Portugal"]},{"id":14,"nom":"Recyclable","description":"Indique si le produit est recyclable","type_valeur":"booleen","groupe":{"id":4,"nom":"D\u00e9veloppement durable"},"valeurs_disponibles":[true,false]}]}]'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/produits/filtrer
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Filtrer les produits par attributs'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"error":"probleme de r\u00e9cup\u00e9ration des donn\u00e9es SQLSTATE[22P02]: Invalid text representation: 7 ERROR:  invalid input syntax for type bigint: \"filtrer\"\nCONTEXT:  unnamed portal parameter $1 = ''...'' (Connection: pgsql, SQL: select * from \"produits\" where \"produits\".\"id\" = filtrer limit 1)"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/produits/{id}/attributs'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Définir les attributs d'un produit"
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the produit.'
        required: true
        example: 3
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 3
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/produits/{id}/attributs/{attributId}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Mettre à jour un attribut spécifique d'un produit"
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the produit.'
        required: true
        example: 3
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      attributId:
        name: attributId
        description: ''
        required: true
        example: 3
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 3
      attributId: 3
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/produits/{id}/attributs/{attributId}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Supprimer un attribut d'un produit"
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the produit.'
        required: true
        example: 3
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      attributId:
        name: attributId
        description: ''
        required: true
        example: 3
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 3
      attributId: 3
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/produits/search
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"error":"probleme de r\u00e9cup\u00e9ration des donn\u00e9es SQLSTATE[22P02]: Invalid text representation: 7 ERROR:  invalid input syntax for type bigint: \"search\"\nCONTEXT:  unnamed portal parameter $1 = ''...'' (Connection: pgsql, SQL: select * from \"produits\" where \"produits\".\"id\" = search limit 1)"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/produits/perpages
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"error":"probleme de r\u00e9cup\u00e9ration des donn\u00e9es SQLSTATE[22P02]: Invalid text representation: 7 ERROR:  invalid input syntax for type bigint: \"perpages\"\nCONTEXT:  unnamed portal parameter $1 = ''...'' (Connection: pgsql, SQL: select * from \"produits\" where \"produits\".\"id\" = perpages limit 1)"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/sous_sousCategories
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display a listing of the resource.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '[{"id":4,"nom_sous_sous_categorie":"Parrure de lit","description_sous_sous_categorie":"Parrure","sous_categorie_id":21,"created_at":"2025-04-09T00:00:00.000000Z","updated_at":"2025-04-09T00:00:00.000000Z"},{"id":5,"nom_sous_sous_categorie":"Parure-wire","description_sous_sous_categorie":"Wire","sous_categorie_id":21,"created_at":"2025-04-09T00:00:00.000000Z","updated_at":"2025-04-09T00:00:00.000000Z"}]'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/sous_sousCategories
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Store a newly created resource in storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/sous_sousCategories/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified resource.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the sous sousCategory.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"error":"probleme de r\u00e9cup\u00e9ration des donn\u00e9es SQLSTATE[22P02]: Invalid text representation: 7 ERROR:  invalid input syntax for type bigint: \"architecto\"\nCONTEXT:  unnamed portal parameter $1 = ''...'' (Connection: pgsql, SQL: select * from \"sous_sous_categories\" where \"sous_sous_categories\".\"id\" = architecto limit 1)"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/sous_sousCategories/{id}/attributs'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Récupère les attributs associés à la sous-catégorie parente d'une sous-sous-catégorie"
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the sous sousCategory.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 500
        content: '{"error":"Probl\u00e8me de r\u00e9cup\u00e9ration des attributs","message":"SQLSTATE[22P02]: Invalid text representation: 7 ERROR:  invalid input syntax for type bigint: \"architecto\"\nCONTEXT:  unnamed portal parameter $1 = ''...'' (Connection: pgsql, SQL: select * from \"sous_sous_categories\" where \"sous_sous_categories\".\"id\" = architecto limit 1)"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/sous_sousCategories/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update the specified resource in storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the sous sousCategory.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/sous_sousCategories/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove the specified resource from storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the sous sousCategory.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/categories
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display a listing of the resource.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '[{"id":3,"nom_categorie":"Chaises","description_categorie":"Chaises, fauteuils et autres assises","image_categorie":"https:\/\/encrypted-tbn0.gstatic.com\/images?q=tbn:ANd9GcTbutzpXgvzVTx5j2vhj3JRZkCLgPiYo26yIw&s","created_at":"2025-03-26T00:09:30.000000Z","updated_at":"2025-03-26T00:09:30.000000Z","featured":false,"featured_order":0},{"id":4,"nom_categorie":"Horloges","description_categorie":"Horloges murales et de table","image_categorie":"https:\/\/www.j-line.be\/media\/images\/HiRes\/52511.jpg&v=acce8ca4dbbe17022ca0467257e143d4&fit=contain&w=600","created_at":"2025-03-26T00:09:48.000000Z","updated_at":"2025-03-26T00:09:48.000000Z","featured":false,"featured_order":0},{"id":5,"nom_categorie":"Miroirs","description_categorie":"Miroirs de d\u00e9coration pour la maison","image_categorie":"https:\/\/www.j-line.be\/media\/images\/HiRes\/50802.jpg&fit=contain&q=50&h=160&w=200","created_at":"2025-03-26T00:12:11.000000Z","updated_at":"2025-03-26T00:12:11.000000Z","featured":false,"featured_order":0},{"id":6,"nom_categorie":"D\u00e9coration murale","description_categorie":"Objets d\u00e9coratifs pour les murs","image_categorie":"https:\/\/www.j-line.be\/media\/categories\/wall-decoration-9296.jpeg&fit=crop-50-50&w=1300&h=1000","created_at":"2025-03-26T00:12:24.000000Z","updated_at":"2025-03-26T00:12:24.000000Z","featured":false,"featured_order":0},{"id":7,"nom_categorie":"Cadres photo","description_categorie":"Cadres pour vos photos et souvenirs","image_categorie":"https:\/\/www.j-line.be\/media\/categories\/photoframes-9310.jpeg&fit=crop-55-50&w=1040&h=800","created_at":"2025-03-26T00:12:46.000000Z","updated_at":"2025-03-26T00:12:46.000000Z","featured":false,"featured_order":0},{"id":9,"nom_categorie":"Textiles","description_categorie":"Rideaux, coussins et textiles d''int\u00e9rieur","image_categorie":"https:\/\/www.j-line.be\/media\/categories\/textiles-9298.jpeg&fit=crop-56-50&w=1300&h=1000","created_at":"2025-03-26T00:13:35.000000Z","updated_at":"2025-03-26T00:13:35.000000Z","featured":false,"featured_order":0},{"id":10,"nom_categorie":"Art de la table","description_categorie":"Vaisselle et accessoires de table","image_categorie":"https:\/\/encrypted-tbn0.gstatic.com\/images?q=tbn:ANd9GcQN3CWsB06y0Xq6P0NtYqJszN6gaF2KswhkbQ&s","created_at":"2025-03-26T00:13:58.000000Z","updated_at":"2025-03-26T00:13:58.000000Z","featured":false,"featured_order":0},{"id":11,"nom_categorie":"Figurines","description_categorie":"Figurines d\u00e9coratives","image_categorie":"https:\/\/cdn.moolwan.com\/5176384e-6349-4a98-8ffc-5d991579dcc3.webp","created_at":"2025-03-26T00:14:17.000000Z","updated_at":"2025-03-26T00:14:17.000000Z","featured":false,"featured_order":0},{"id":13,"nom_categorie":"Rangement","description_categorie":"Solutions de rangement et d''organisation","image_categorie":"https:\/\/images.ctfassets.net\/ltric1hkjv72\/60sUWFtvaC24w5GppGqSbq\/d9cdd9f4e5f3d54aa04c1595529e725a\/ECE8D6EC-E0C7-4352-905A-71A44405F339_1_105_c.jpeg","created_at":"2025-03-26T00:15:11.000000Z","updated_at":"2025-03-26T00:15:11.000000Z","featured":false,"featured_order":0},{"id":14,"nom_categorie":"Accessoires maison","description_categorie":"Accessoires divers pour la maison","image_categorie":"https:\/\/m.media-amazon.com\/images\/I\/71OToU18D5L.jpg","created_at":"2025-03-26T00:15:38.000000Z","updated_at":"2025-03-26T00:15:38.000000Z","featured":false,"featured_order":0},{"id":15,"nom_categorie":"Accessoires de jardin","description_categorie":"D\u00e9coration et accessoires pour le jardin","image_categorie":"https:\/\/www.milatonie.com\/img\/c\/163.jpg","created_at":"2025-03-26T00:16:05.000000Z","updated_at":"2025-03-26T00:16:05.000000Z","featured":false,"featured_order":0},{"id":16,"nom_categorie":"Fleurs","description_categorie":"Fleurs artificielles ou naturelles","image_categorie":"https:\/\/m.media-amazon.com\/images\/I\/718wQwGiNZL._AC_SL1500_.jpg","created_at":"2025-03-26T00:16:25.000000Z","updated_at":"2025-03-26T00:16:25.000000Z","featured":false,"featured_order":0},{"id":17,"nom_categorie":"Pots de fleurs","description_categorie":"Pots et jardini\u00e8res","image_categorie":"https:\/\/www.elho.com\/media\/r1xljovi\/elho-haarlem-3-6-204818.jpg?rxy=0.49624060150375937,0.647451963241437&width=1440&height=450&v=1db76f9e0ee27c0","created_at":"2025-03-26T00:16:47.000000Z","updated_at":"2025-03-26T00:16:47.000000Z","featured":false,"featured_order":0},{"id":18,"nom_categorie":"Vases","description_categorie":"Vases pour fleurs et d\u00e9coration","image_categorie":"https:\/\/m.media-amazon.com\/images\/I\/81OBlS1H9VL.jpg","created_at":"2025-03-26T00:17:04.000000Z","updated_at":"2025-03-26T00:17:04.000000Z","featured":false,"featured_order":0},{"id":19,"nom_categorie":"Bougeoirs","description_categorie":"Bougeoirs et chandeliers","image_categorie":"https:\/\/fiollahome.com\/cdn\/shop\/files\/set-de-3-bougeoirs-dores-elegance-et-brillance-fiolla-home-967904.jpg?v=1722029291&width=2048","created_at":"2025-03-26T00:17:21.000000Z","updated_at":"2025-03-26T00:17:21.000000Z","featured":false,"featured_order":0},{"id":20,"nom_categorie":"Bougies","description_categorie":"Bougies d\u00e9coratives","image_categorie":"https:\/\/bricola.tn\/24138-home_default\/bougie-parfumee-bubble-cube-de-luxe.jpg","created_at":"2025-03-26T00:17:49.000000Z","updated_at":"2025-03-26T00:17:49.000000Z","featured":false,"featured_order":0},{"id":22,"nom_categorie":"Peluches","description_categorie":"Peluches pour enfants et d\u00e9coration","image_categorie":"https:\/\/www.francebleu.fr\/s3\/cruiser-production\/2021\/12\/32b18b48-2f0e-4c8b-9ae7-351ea3ed7e0a\/1200x680_soft-toys-3158361_1280.jpg","created_at":"2025-03-26T00:18:32.000000Z","updated_at":"2025-03-26T00:18:32.000000Z","featured":false,"featured_order":0},{"id":23,"nom_categorie":"Accessoires de mode","description_categorie":"Sacs, foulards et accessoires de mode","image_categorie":"https:\/\/wwd.com\/wp-content\/uploads\/2023\/04\/best-beach-bag.jpg?w=911&h=510&crop=1","created_at":"2025-03-26T00:18:49.000000Z","updated_at":"2025-03-26T00:18:49.000000Z","featured":false,"featured_order":0},{"id":24,"nom_categorie":"Mat\u00e9riaux de d\u00e9coration","description_categorie":"Mat\u00e9riaux et fournitures pour la d\u00e9coration","image_categorie":"https:\/\/www.j-line.be\/media\/categories\/decoration-materials-9110.jpeg&fit=crop-50-32&w=2000&h=1539","created_at":"2025-03-26T00:19:07.000000Z","updated_at":"2025-03-26T00:19:07.000000Z","featured":false,"featured_order":0},{"id":25,"nom_categorie":"Linge de maison","description_categorie":"Linge","image_categorie":"https:\/\/www.carreblanc.com\/media\/wysiwyg\/mg2\/cms-categorie\/promo\/250205-archives-adorees\/list-mod-7-3-perso-min.jpg","created_at":"2025-04-09T00:00:00.000000Z","updated_at":"2025-04-09T00:00:00.000000Z","featured":false,"featured_order":0},{"id":8,"nom_categorie":"\u00c9clairage","description_categorie":"Lampes et luminaires","image_categorie":"https:\/\/www.j-line.be\/media\/categories\/lighting-9297.jpeg&fit=crop-50-50&w=1302&h=1001","created_at":"2025-03-26T00:13:14.000000Z","updated_at":"2025-04-18T11:34:46.000000Z","featured":true,"featured_order":0},{"id":12,"nom_categorie":"Objets d\u00e9coratifs","description_categorie":"Objets divers pour la d\u00e9coration","image_categorie":"https:\/\/assets.weimgs.com\/weimgs\/rk\/images\/wcm\/products\/202511\/0012\/asher-ceramic-objects-q.jpg","created_at":"2025-03-26T00:14:44.000000Z","updated_at":"2025-04-18T11:36:53.000000Z","featured":true,"featured_order":1},{"id":21,"nom_categorie":"Bougies parfum\u00e9es & Parfums d''ambiance","description_categorie":"Bougies parfum\u00e9es et senteurs pour la maison","image_categorie":"https:\/\/maison-berger.fr\/cdn\/shop\/files\/800x800-maison-berger-duality-012022-def-5-v2.jpg?crop=center&height=800&v=1705494614&width=800","created_at":"2025-03-26T00:18:09.000000Z","updated_at":"2025-04-18T19:15:34.000000Z","featured":true,"featured_order":2},{"id":2,"nom_categorie":"Meubles","description_categorie":"Cat\u00e9gorie de meubles pour la maison","image_categorie":"https:\/\/encrypted-tbn0.gstatic.com\/images?q=tbn:ANd9GcSFhLI1iVQdfkrHexpecT9kQohI1s1y1RLftQ&s","created_at":"2025-03-26T00:09:08.000000Z","updated_at":"2025-04-18T20:10:53.000000Z","featured":true,"featured_order":3}]'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/categories
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Store a newly created resource in storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/categories/featured
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get all featured categories.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '[{"id":8,"nom_categorie":"\u00c9clairage","description_categorie":"Lampes et luminaires","image_categorie":"https:\/\/www.j-line.be\/media\/categories\/lighting-9297.jpeg&fit=crop-50-50&w=1302&h=1001","created_at":"2025-03-26T00:13:14.000000Z","updated_at":"2025-04-18T11:34:46.000000Z","featured":true,"featured_order":0,"images":[]},{"id":12,"nom_categorie":"Objets d\u00e9coratifs","description_categorie":"Objets divers pour la d\u00e9coration","image_categorie":"https:\/\/assets.weimgs.com\/weimgs\/rk\/images\/wcm\/products\/202511\/0012\/asher-ceramic-objects-q.jpg","created_at":"2025-03-26T00:14:44.000000Z","updated_at":"2025-04-18T11:36:53.000000Z","featured":true,"featured_order":1,"images":[]},{"id":21,"nom_categorie":"Bougies parfum\u00e9es & Parfums d''ambiance","description_categorie":"Bougies parfum\u00e9es et senteurs pour la maison","image_categorie":"https:\/\/maison-berger.fr\/cdn\/shop\/files\/800x800-maison-berger-duality-012022-def-5-v2.jpg?crop=center&height=800&v=1705494614&width=800","created_at":"2025-03-26T00:18:09.000000Z","updated_at":"2025-04-18T19:15:34.000000Z","featured":true,"featured_order":2,"images":[]},{"id":2,"nom_categorie":"Meubles","description_categorie":"Cat\u00e9gorie de meubles pour la maison","image_categorie":"https:\/\/encrypted-tbn0.gstatic.com\/images?q=tbn:ANd9GcSFhLI1iVQdfkrHexpecT9kQohI1s1y1RLftQ&s","created_at":"2025-03-26T00:09:08.000000Z","updated_at":"2025-04-18T20:10:53.000000Z","featured":true,"featured_order":3,"images":[]}]'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/categories/featured/reorder
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Reorder featured categories.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/categories/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified resource.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the category.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"error":"probleme de r\u00e9cup\u00e9ration des donn\u00e9es SQLSTATE[22P02]: Invalid text representation: 7 ERROR:  invalid input syntax for type bigint: \"architecto\"\nCONTEXT:  unnamed portal parameter $1 = ''...'' (Connection: pgsql, SQL: select * from \"categories\" where \"categories\".\"id\" = architecto limit 1)"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/categories/{id}/sousCategories'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get all subcategories for a specific category'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the category.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 500
        content: '{"error":"Probl\u00e8me de r\u00e9cup\u00e9ration des sous-cat\u00e9gories","message":"SQLSTATE[22P02]: Invalid text representation: 7 ERROR:  invalid input syntax for type bigint: \"architecto\"\nCONTEXT:  unnamed portal parameter $1 = ''...'' (Connection: pgsql, SQL: select * from \"sous_categories\" where \"categorie_id\" = architecto)"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/categories/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update the specified resource in storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the category.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/categories/{id}/featured'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Set a category as featured or not.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the category.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/categories/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove the specified resource from storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the category.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/sousCategories
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display a listing of the resource.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '[{"id":4,"nom_sous_categorie":"Library cabinet","description_sous_categorie":"Cabinet pour biblioth\u00e8que","categorie_id":2,"created_at":"2025-03-26T00:35:19.000000Z","updated_at":"2025-03-26T00:35:19.000000Z"},{"id":5,"nom_sous_categorie":"Sideboard","description_sous_categorie":"Buffet ou meuble de rangement","categorie_id":2,"created_at":"2025-03-26T00:35:44.000000Z","updated_at":"2025-03-26T00:35:44.000000Z"},{"id":6,"nom_sous_categorie":"Cabinet","description_sous_categorie":"Meuble de rangement","categorie_id":2,"created_at":"2025-03-26T00:36:11.000000Z","updated_at":"2025-03-26T00:36:11.000000Z"},{"id":7,"nom_sous_categorie":"Shelf","description_sous_categorie":"\u00c9tag\u00e8re de rangement","categorie_id":2,"created_at":"2025-03-26T00:36:43.000000Z","updated_at":"2025-03-26T00:36:43.000000Z"},{"id":8,"nom_sous_categorie":"Dresser","description_sous_categorie":"Commode ou meuble de rangement","categorie_id":2,"created_at":"2025-03-26T00:36:55.000000Z","updated_at":"2025-03-26T00:36:55.000000Z"},{"id":9,"nom_sous_categorie":"Console","description_sous_categorie":"Meuble console","categorie_id":2,"created_at":"2025-03-26T00:37:06.000000Z","updated_at":"2025-03-26T00:37:06.000000Z"},{"id":10,"nom_sous_categorie":"TV cabinet","description_sous_categorie":"Meuble pour t\u00e9l\u00e9vision","categorie_id":2,"created_at":"2025-03-26T00:37:17.000000Z","updated_at":"2025-03-26T00:37:17.000000Z"},{"id":11,"nom_sous_categorie":"Bar","description_sous_categorie":"Bar ou meuble de bar","categorie_id":2,"created_at":"2025-03-26T00:37:29.000000Z","updated_at":"2025-03-26T00:37:29.000000Z"},{"id":12,"nom_sous_categorie":"Desk","description_sous_categorie":"Bureau ou table de travail","categorie_id":2,"created_at":"2025-03-26T00:37:38.000000Z","updated_at":"2025-03-26T00:37:38.000000Z"},{"id":13,"nom_sous_categorie":"Bedside table","description_sous_categorie":"Table de chevet","categorie_id":2,"created_at":"2025-03-26T00:37:49.000000Z","updated_at":"2025-03-26T00:37:49.000000Z"},{"id":14,"nom_sous_categorie":"Side table","description_sous_categorie":"Table d''appoint","categorie_id":2,"created_at":"2025-03-26T00:38:05.000000Z","updated_at":"2025-03-26T00:38:05.000000Z"},{"id":15,"nom_sous_categorie":"Coffeetable","description_sous_categorie":"Table basse","categorie_id":2,"created_at":"2025-03-26T00:38:14.000000Z","updated_at":"2025-03-26T00:38:14.000000Z"},{"id":16,"nom_sous_categorie":"Table","description_sous_categorie":"Table de salle \u00e0 manger ou table d''appoint","categorie_id":2,"created_at":"2025-03-26T00:38:24.000000Z","updated_at":"2025-03-26T00:38:24.000000Z"},{"id":17,"nom_sous_categorie":"Hanging rack","description_sous_categorie":"Porte-manteau suspendu","categorie_id":2,"created_at":"2025-03-26T00:38:37.000000Z","updated_at":"2025-03-26T00:38:37.000000Z"},{"id":18,"nom_sous_categorie":"Pedestal","description_sous_categorie":"Piedestal d\u00e9coratif","categorie_id":2,"created_at":"2025-03-26T00:38:47.000000Z","updated_at":"2025-03-26T00:38:47.000000Z"},{"id":19,"nom_sous_categorie":"Miscellaneous","description_sous_categorie":"Divers articles de d\u00e9coration","categorie_id":2,"created_at":"2025-03-26T00:38:58.000000Z","updated_at":"2025-03-26T00:38:58.000000Z"},{"id":21,"nom_sous_categorie":"Linge de lit","description_sous_categorie":"Lit","categorie_id":25,"created_at":"2025-04-09T00:00:00.000000Z","updated_at":"2025-04-09T00:00:00.000000Z"},{"id":22,"nom_sous_categorie":"Linge de Bain","description_sous_categorie":"Bain","categorie_id":25,"created_at":"2025-04-09T00:00:00.000000Z","updated_at":"2025-04-09T00:00:00.000000Z"},{"id":23,"nom_sous_categorie":"Literie","description_sous_categorie":"Literie","categorie_id":25,"created_at":"2025-04-09T00:00:00.000000Z","updated_at":"2025-04-09T00:00:00.000000Z"},{"id":24,"nom_sous_categorie":"Enfant","description_sous_categorie":"Enfant","categorie_id":25,"created_at":"2025-04-09T00:00:00.000000Z","updated_at":"2025-04-09T00:00:00.000000Z"}]'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/sousCategories
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Store a newly created resource in storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/sousCategories/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified resource.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the sousCategory.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 500
        content: |-
          {
              "message": "Server Error"
          }
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/sousCategories/{id}/caracteristiques'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the sousCategory.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 500
        content: |-
          {
              "message": "Server Error"
          }
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/sousCategories/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update the specified resource in storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the sousCategory.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/sousCategories/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove the specified resource from storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the sousCategory.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/commandes
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display a listing of the resource.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '[]'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/commandes
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Store a newly created resource in storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/commandes/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified resource.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the commande.'
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 16
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"error":"probleme de r\u00e9cup\u00e9ration des donn\u00e9es No query results for model [App\\Models\\Commande] 16"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/commandes/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update the specified resource in storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the commande.'
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 16
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/commandes/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove the specified resource from storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the commande.'
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 16
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/clients
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display a listing of the clients.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '[{"id":10,"name":"iheb iheb","email":"<EMAIL>","email_verified_at":null,"created_at":"2025-04-05T15:28:35.000000Z","updated_at":"2025-04-05T15:28:35.000000Z","keycloak_id":"3982c8da-05cf-4743-8351-52ea5b126e20","roles":["default-roles-jiheneline","offline_access","client","uma_authorization"],"point_de_vente_id":null,"remise_personnelle":"0.00","groupe_client_id":null,"profil_remise":"standard","remise_effective":0,"partenaire":null,"point_de_vente":null,"groupe_client":null},{"id":8,"name":"Keycloak user","email":"<EMAIL>","email_verified_at":null,"created_at":"2025-04-05T11:45:31.000000Z","updated_at":"2025-04-05T15:39:36.000000Z","keycloak_id":"6361ac60-3a92-4a7d-8856-e4e1f36e896d","roles":["default-roles-jiheneline","offline_access","client","uma_authorization"],"point_de_vente_id":null,"remise_personnelle":"0.00","groupe_client_id":null,"profil_remise":"standard","remise_effective":0,"partenaire":null,"point_de_vente":null,"groupe_client":null},{"id":1,"name":"Youssef Mrabet","email":"<EMAIL>","email_verified_at":null,"created_at":"2025-03-24T22:41:06.000000Z","updated_at":"2025-04-06T13:48:52.000000Z","keycloak_id":"5b6d0ab9-edf1-46d6-98e4-5a49fcb3b85a","roles":["default-roles-jiheneline","offline_access","client","uma_authorization"],"point_de_vente_id":null,"remise_personnelle":"10.50","groupe_client_id":null,"profil_remise":"standard","remise_effective":10.5,"partenaire":{"id":1,"created_at":"2025-04-04T11:39:01.000000Z","updated_at":"2025-04-04T11:40:04.000000Z","user_id":1,"remise":"15.00","description":"Partenaire premium","statut":"actif"},"point_de_vente":null,"groupe_client":null},{"id":4,"name":"sirine gouiaa","email":"<EMAIL>","email_verified_at":null,"created_at":"2025-04-05T00:12:26.000000Z","updated_at":"2025-04-08T21:06:15.000000Z","keycloak_id":"837cf09b-ef65-423b-94dd-7bb627e976aa","roles":["default-roles-jiheneline","offline_access","client","uma_authorization"],"point_de_vente_id":null,"remise_personnelle":"0.00","groupe_client_id":null,"profil_remise":"standard","remise_effective":0,"partenaire":null,"point_de_vente":null,"groupe_client":null},{"id":12,"name":"youssef youssef","email":"<EMAIL>","email_verified_at":null,"created_at":"2025-04-24T11:57:18.000000Z","updated_at":"2025-04-24T11:57:18.000000Z","keycloak_id":"fdc7332f-1b8f-4af6-90fd-c234dc4eab80","roles":["default-roles-jiheneline","offline_access","client","uma_authorization"],"point_de_vente_id":null,"remise_personnelle":"0.00","groupe_client_id":null,"profil_remise":"standard","remise_effective":0,"partenaire":null,"point_de_vente":null,"groupe_client":null},{"id":2,"name":"trigui islem","email":"<EMAIL>","email_verified_at":null,"created_at":"2025-03-25T20:30:35.000000Z","updated_at":"2025-04-05T12:14:44.000000Z","keycloak_id":"94db4834-65bb-46e2-a4a7-ba53280a89d5","roles":["default-roles-jiheneline","offline_access","uma_authorization","client"],"point_de_vente_id":null,"remise_personnelle":"0.00","groupe_client_id":null,"profil_remise":"standard","remise_effective":0,"partenaire":null,"point_de_vente":null,"groupe_client":null},{"id":3,"name":"Ranime Chelly","email":"<EMAIL>","email_verified_at":null,"created_at":"2025-04-01T18:32:22.000000Z","updated_at":"2025-04-05T12:14:45.000000Z","keycloak_id":"4b3ba74c-2eaa-4f4c-ae1f-e4c7ea19ba7d","roles":["default-roles-jiheneline","offline_access","uma_authorization","client"],"point_de_vente_id":null,"remise_personnelle":"0.00","groupe_client_id":null,"profil_remise":"standard","remise_effective":0,"partenaire":null,"point_de_vente":null,"groupe_client":null},{"id":5,"name":"dd eya","email":"<EMAIL>","email_verified_at":null,"created_at":"2025-04-05T11:00:50.000000Z","updated_at":"2025-04-05T12:14:47.000000Z","keycloak_id":"5473c720-b445-4f64-bab0-98b4e455b4b0","roles":["default-roles-jiheneline","offline_access","uma_authorization","client"],"point_de_vente_id":null,"remise_personnelle":"0.00","groupe_client_id":null,"profil_remise":"standard","remise_effective":0,"partenaire":null,"point_de_vente":null,"groupe_client":null},{"id":6,"name":"dd eya","email":"<EMAIL>","email_verified_at":null,"created_at":"2025-04-05T11:08:19.000000Z","updated_at":"2025-04-05T12:14:48.000000Z","keycloak_id":"e85eae6c-9e0d-4706-94c6-4336a26336a0","roles":["default-roles-jiheneline","offline_access","uma_authorization","client"],"point_de_vente_id":null,"remise_personnelle":"0.00","groupe_client_id":null,"profil_remise":"standard","remise_effective":0,"partenaire":null,"point_de_vente":null,"groupe_client":null},{"id":7,"name":"User User","email":"<EMAIL>","email_verified_at":null,"created_at":"2025-04-05T11:17:52.000000Z","updated_at":"2025-04-05T12:14:48.000000Z","keycloak_id":"c8e06497-6fac-425f-a7e5-373b6cfe21ab","roles":["default-roles-jiheneline","offline_access","uma_authorization","client"],"point_de_vente_id":null,"remise_personnelle":"0.00","groupe_client_id":null,"profil_remise":"standard","remise_effective":0,"partenaire":null,"point_de_vente":null,"groupe_client":null},{"id":9,"name":"safa safa","email":"<EMAIL>","email_verified_at":null,"created_at":"2025-04-05T14:07:54.000000Z","updated_at":"2025-04-05T14:07:54.000000Z","keycloak_id":"9fadd9bd-1dd8-4c77-a3f0-1e61727329f0","roles":["default-roles-jiheneline","offline_access","client","uma_authorization"],"point_de_vente_id":null,"remise_personnelle":"0.00","groupe_client_id":null,"profil_remise":"standard","remise_effective":0,"partenaire":null,"point_de_vente":null,"groupe_client":null}]'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/clients/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified client.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the client.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 500
        content: '{"error":"Probl\u00e8me de r\u00e9cup\u00e9ration du client","message":"SQLSTATE[22P02]: Invalid text representation: 7 ERROR:  invalid input syntax for type bigint: \"architecto\"\nCONTEXT:  unnamed portal parameter $1 = ''...'' (Connection: pgsql, SQL: select * from \"users\" where \"users\".\"id\" = architecto limit 1)"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/clients/{id}/derniere-commande'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get the latest order for a specific client.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the client.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 500
        content: '{"error":"Probl\u00e8me de r\u00e9cup\u00e9ration de la derni\u00e8re commande","message":"SQLSTATE[22P02]: Invalid text representation: 7 ERROR:  invalid input syntax for type bigint: \"architecto\"\nCONTEXT:  unnamed portal parameter $1 = ''...'' (Connection: pgsql, SQL: select * from \"users\" where \"users\".\"id\" = architecto limit 1)"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/clients/{id}/commandes'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get all orders for a specific client.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the client.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 500
        content: '{"error":"Probl\u00e8me de r\u00e9cup\u00e9ration des commandes","message":"SQLSTATE[22P02]: Invalid text representation: 7 ERROR:  invalid input syntax for type bigint: \"architecto\"\nCONTEXT:  unnamed portal parameter $1 = ''...'' (Connection: pgsql, SQL: select * from \"users\" where \"users\".\"id\" = architecto limit 1)"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/clients/{id}/remise'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Update a client's discount."
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the client.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/clients/{id}/type'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Update a client's profile."
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the client.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/clients/{id}/profil-remise'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Update a client's profile."
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the client.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/partenaires
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display a listing of all partners.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 500
        content: '{"error":"Probl\u00e8me de r\u00e9cup\u00e9ration des partenaires","message":"SQLSTATE[42703]: Undefined column: 7 ERROR:  column \"type_client\" does not exist\nLINE 1: ...t * from \"users\" where (\"roles\")::jsonb @> $1 and \"type_clie...\n                                                             ^ (Connection: pgsql, SQL: select * from \"users\" where (\"roles\")::jsonb @> \"partenaire\" and \"type_client\" = partenaire)"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/partenaires
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Store a newly created partner in storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/partenaires/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified partner.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the partenaire.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"id":1,"created_at":"2025-04-04T11:39:01.000000Z","updated_at":"2025-04-04T11:40:04.000000Z","user_id":1,"remise":"15.00","description":"Partenaire premium","statut":"actif","user":{"id":1,"name":"Youssef Mrabet","email":"<EMAIL>","email_verified_at":null,"created_at":"2025-03-24T22:41:06.000000Z","updated_at":"2025-04-06T13:48:52.000000Z","keycloak_id":"5b6d0ab9-edf1-46d6-98e4-5a49fcb3b85a","roles":["default-roles-jiheneline","offline_access","client","uma_authorization"],"point_de_vente_id":null,"remise_personnelle":"10.50","groupe_client_id":null,"profil_remise":"standard"}}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/partenaires/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update the specified partner in storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the partenaire.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/partenaires/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove the partner status from a user.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the partenaire.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/points-de-vente
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display a listing of all points of sale.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '[{"id":1,"nom":"Boutique Centre Ville","adresse":"123 Rue Principale","telephone":"71234567","email":"<EMAIL>","remise":"8.50","description":"Point de vente principal","statut":"actif","created_at":"2025-04-04T11:40:23.000000Z","updated_at":"2025-04-04T11:40:23.000000Z","users_count":0}]'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/points-de-vente
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Store a newly created point of sale in storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/points-de-vente/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified point of sale.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the points de vente.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 500
        content: '{"error":"Probl\u00e8me de r\u00e9cup\u00e9ration du point de vente","message":"SQLSTATE[22P02]: Invalid text representation: 7 ERROR:  invalid input syntax for type bigint: \"architecto\"\nCONTEXT:  unnamed portal parameter $1 = ''...'' (Connection: pgsql, SQL: select * from \"points_de_vente\" where \"points_de_vente\".\"id\" = architecto limit 1)"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/points-de-vente/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update the specified point of sale in storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the points de vente.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/points-de-vente/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove the specified point of sale from storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the points de vente.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/points-de-vente/{id}/clients'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Add a user to a point of sale.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the points de vente.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/points-de-vente/{id}/clients/{userId}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove a user from a point of sale.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the points de vente.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      userId:
        name: userId
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
      userId: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/groupes-clients
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display a listing of all client groups.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '[]'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/groupes-clients
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Store a newly created client group in storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/groupes-clients/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified client group.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the groupes client.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 500
        content: '{"error":"Probl\u00e8me de r\u00e9cup\u00e9ration du groupe de clients","message":"SQLSTATE[22P02]: Invalid text representation: 7 ERROR:  invalid input syntax for type bigint: \"architecto\"\nCONTEXT:  unnamed portal parameter $1 = ''...'' (Connection: pgsql, SQL: select * from \"groupes_clients\" where \"groupes_clients\".\"id\" = architecto limit 1)"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/groupes-clients/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update the specified client group in storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the groupes client.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/groupes-clients/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove the specified client group from storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the groupes client.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/groupes-clients/{id}/clients'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Add a user to a client group.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the groupes client.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/groupes-clients/{id}/clients/{userId}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove a user from a client group.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the groupes client.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      userId:
        name: userId
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
      userId: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/collections
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Afficher la liste des collections'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '[{"id":1,"nom":"Collection \u00c9t\u00e9 2025","description":"Produits tendance pour l''\u00e9t\u00e9","image":"ete_2025.jpg","active":true,"date_debut":"2025-05-01T00:00:00.000000Z","date_fin":"2025-08-31T00:00:00.000000Z","created_at":"2025-04-04T21:00:48.000000Z","updated_at":"2025-04-04T21:00:48.000000Z"},{"id":2,"nom":"chambre","description":"kkkk","image":"kkk","active":true,"date_debut":"2025-04-30T00:00:00.000000Z","date_fin":"2025-05-02T00:00:00.000000Z","created_at":"2025-04-17T23:48:59.000000Z","updated_at":"2025-04-17T23:48:59.000000Z"},{"id":3,"nom":"chaima tast","description":"chaima tast","image":null,"active":true,"date_debut":"2025-04-24T00:00:00.000000Z","date_fin":"2025-04-27T00:00:00.000000Z","created_at":"2025-04-19T10:56:42.000000Z","updated_at":"2025-04-19T10:56:42.000000Z"}]'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/collections
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Créer une nouvelle collection'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/collections/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Afficher une collection spécifique'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the collection.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"id":1,"nom":"Collection \u00c9t\u00e9 2025","description":"Produits tendance pour l''\u00e9t\u00e9","image":"ete_2025.jpg","active":true,"date_debut":"2025-05-01T00:00:00.000000Z","date_fin":"2025-08-31T00:00:00.000000Z","created_at":"2025-04-04T21:00:48.000000Z","updated_at":"2025-04-04T21:00:48.000000Z","produits":[{"id":3,"nom_produit":"Parure de lit \u00c9l\u00e9gance","description_produit":"Parure de lit en coton \u00e9gyptien, douce et \u00e9l\u00e9gante pour un sommeil r\u00e9parateur","image_produit":"https:\/\/www.carreblanc.com\/media\/catalog\/product\/cache\/c687aa7517cf01e65c009f6943c2b1e9\/2\/0\/2023-parure-housse-de-couette-percale-coton-blanc-brode-elegance_1.jpg","prix_produit":129.99,"quantite_produit":50,"marque_id":8,"sous_sous_categorie_id":4,"created_at":"2025-04-09T20:51:37.000000Z","updated_at":"2025-04-09T20:51:37.000000Z","reference":"8-1","pivot":{"collection_id":1,"produit_id":3,"ordre":1,"featured":true,"created_at":"2025-04-16T19:17:16.000000Z","updated_at":"2025-04-16T19:17:16.000000Z"}},{"id":4,"nom_produit":"Parure de lit Satin Luxe","description_produit":"Parure de lit en satin de coton, luxueuse et brillante pour un confort optimal","image_produit":"https:\/\/www.carreblanc.com\/media\/catalog\/product\/cache\/c687aa7517cf01e65c009f6943c2b1e9\/2\/0\/2023-parure-housse-de-couette-satin-coton-blanc-satin-luxe_1.jpg","prix_produit":159.99,"quantite_produit":30,"marque_id":8,"sous_sous_categorie_id":4,"created_at":"2025-04-09T20:51:40.000000Z","updated_at":"2025-04-09T20:51:40.000000Z","reference":"8-4","pivot":{"collection_id":1,"produit_id":4,"ordre":2,"featured":false,"created_at":"2025-04-16T19:17:17.000000Z","updated_at":"2025-04-16T19:17:17.000000Z"}},{"id":5,"nom_produit":"Parure de lit Cosy","description_produit":"Parure de lit en flanelle, chaude et douce pour les nuits d''hiver","image_produit":"https:\/\/www.carreblanc.com\/media\/catalog\/product\/cache\/c687aa7517cf01e65c009f6943c2b1e9\/2\/0\/2023-parure-housse-de-couette-flanelle-gris-cosy_1.jpg","prix_produit":99.99,"quantite_produit":40,"marque_id":8,"sous_sous_categorie_id":4,"created_at":"2025-04-09T20:51:43.000000Z","updated_at":"2025-04-09T20:51:43.000000Z","reference":"8-5","pivot":{"collection_id":1,"produit_id":5,"ordre":3,"featured":false,"created_at":"2025-04-16T19:17:18.000000Z","updated_at":"2025-04-16T19:17:18.000000Z"}}]}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/collections/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Mettre à jour une collection existante'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the collection.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/collections/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Supprimer une collection'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the collection.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/collections/{id}/produits'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Ajouter des produits à une collection'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the collection.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/collections/{id}/produits/{produitId}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Supprimer un produit d'une collection"
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the collection.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      produitId:
        name: produitId
        description: ''
        required: true
        example: 3
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
      produitId: 3
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/collections/{id}/produits'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Récupérer les produits d'une collection"
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the collection.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '[{"id":3,"nom_produit":"Parure de lit \u00c9l\u00e9gance","description_produit":"Parure de lit en coton \u00e9gyptien, douce et \u00e9l\u00e9gante pour un sommeil r\u00e9parateur","image_produit":"https:\/\/www.carreblanc.com\/media\/catalog\/product\/cache\/c687aa7517cf01e65c009f6943c2b1e9\/2\/0\/2023-parure-housse-de-couette-percale-coton-blanc-brode-elegance_1.jpg","prix_produit":129.99,"quantite_produit":50,"marque_id":8,"sous_sous_categorie_id":4,"created_at":"2025-04-09T20:51:37.000000Z","updated_at":"2025-04-09T20:51:37.000000Z","reference":"8-1","pivot":{"collection_id":1,"produit_id":3,"ordre":1,"featured":true,"created_at":"2025-04-16T19:17:16.000000Z","updated_at":"2025-04-16T19:17:16.000000Z"}},{"id":4,"nom_produit":"Parure de lit Satin Luxe","description_produit":"Parure de lit en satin de coton, luxueuse et brillante pour un confort optimal","image_produit":"https:\/\/www.carreblanc.com\/media\/catalog\/product\/cache\/c687aa7517cf01e65c009f6943c2b1e9\/2\/0\/2023-parure-housse-de-couette-satin-coton-blanc-satin-luxe_1.jpg","prix_produit":159.99,"quantite_produit":30,"marque_id":8,"sous_sous_categorie_id":4,"created_at":"2025-04-09T20:51:40.000000Z","updated_at":"2025-04-09T20:51:40.000000Z","reference":"8-4","pivot":{"collection_id":1,"produit_id":4,"ordre":2,"featured":false,"created_at":"2025-04-16T19:17:17.000000Z","updated_at":"2025-04-16T19:17:17.000000Z"}},{"id":5,"nom_produit":"Parure de lit Cosy","description_produit":"Parure de lit en flanelle, chaude et douce pour les nuits d''hiver","image_produit":"https:\/\/www.carreblanc.com\/media\/catalog\/product\/cache\/c687aa7517cf01e65c009f6943c2b1e9\/2\/0\/2023-parure-housse-de-couette-flanelle-gris-cosy_1.jpg","prix_produit":99.99,"quantite_produit":40,"marque_id":8,"sous_sous_categorie_id":4,"created_at":"2025-04-09T20:51:43.000000Z","updated_at":"2025-04-09T20:51:43.000000Z","reference":"8-5","pivot":{"collection_id":1,"produit_id":5,"ordre":3,"featured":false,"created_at":"2025-04-16T19:17:18.000000Z","updated_at":"2025-04-16T19:17:18.000000Z"}}]'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/collections/{id}/produits-featured'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Récupérer les produits mis en avant d'une collection"
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the collection.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '[{"id":3,"nom_produit":"Parure de lit \u00c9l\u00e9gance","description_produit":"Parure de lit en coton \u00e9gyptien, douce et \u00e9l\u00e9gante pour un sommeil r\u00e9parateur","image_produit":"https:\/\/www.carreblanc.com\/media\/catalog\/product\/cache\/c687aa7517cf01e65c009f6943c2b1e9\/2\/0\/2023-parure-housse-de-couette-percale-coton-blanc-brode-elegance_1.jpg","prix_produit":129.99,"quantite_produit":50,"marque_id":8,"sous_sous_categorie_id":4,"created_at":"2025-04-09T20:51:37.000000Z","updated_at":"2025-04-09T20:51:37.000000Z","reference":"8-1","pivot":{"collection_id":1,"produit_id":3,"ordre":1,"featured":true,"created_at":"2025-04-16T19:17:16.000000Z","updated_at":"2025-04-16T19:17:16.000000Z"}}]'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/paiements
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display a listing of the resource.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '[]'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/paiements
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Store a newly created resource in storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/paiements/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified resource.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the paiement.'
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 16
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/paiements/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update the specified resource in storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the paiement.'
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 16
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/paiements/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove the specified resource from storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the paiement.'
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 16
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/admin/clients/{id}/derniere-commande'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get the latest order for a specific client.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the client.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"error":"Unauthenticated"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/promotions
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Afficher la liste des promotions'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"status":"success","data":{"current_page":1,"data":[{"id":1,"nom":"Test Promotion","code":null,"description":null,"type":"pourcentage","valeur":"10.00","statut":"active","date_debut":null,"date_fin":null,"priorit\u00e9":0,"cumulable":false,"conditions":null,"created_at":"2025-04-06T10:30:11.000000Z","updated_at":"2025-04-06T10:30:11.000000Z","deleted_at":null,"event_id":null,"image":null,"featured":false},{"id":3,"nom":"chaimaTest","code":"hhh","description":"bbb","type":"pourcentage","valeur":"10.00","statut":"active","date_debut":"2025-04-17T23:00:00.000000Z","date_fin":"2025-04-25T23:00:00.000000Z","priorit\u00e9":0,"cumulable":true,"conditions":null,"created_at":"2025-04-18T00:15:43.000000Z","updated_at":"2025-04-18T00:15:43.000000Z","deleted_at":null,"event_id":null,"image":null,"featured":false},{"id":5,"nom":"ete","code":"etete","description":"fghjkl","type":"pourcentage","valeur":"10.00","statut":"active","date_debut":"2025-04-23T23:00:00.000000Z","date_fin":"2025-04-25T23:00:00.000000Z","priorit\u00e9":0,"cumulable":true,"conditions":null,"created_at":"2025-04-18T01:29:54.000000Z","updated_at":"2025-04-18T01:29:54.000000Z","deleted_at":null,"event_id":null,"image":null,"featured":false}],"first_page_url":"http:\/\/0.0.0.0:8000\/api\/promotions?page=1","from":1,"last_page":1,"last_page_url":"http:\/\/0.0.0.0:8000\/api\/promotions?page=1","links":[{"url":null,"label":"&laquo; Previous","active":false},{"url":"http:\/\/0.0.0.0:8000\/api\/promotions?page=1","label":"1","active":true},{"url":null,"label":"Next &raquo;","active":false}],"next_page_url":null,"path":"http:\/\/0.0.0.0:8000\/api\/promotions","per_page":15,"prev_page_url":null,"to":3,"total":3}}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/promotions
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Créer une nouvelle promotion'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      nom:
        name: nom
        description: 'Must not be greater than 255 characters.'
        required: true
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      code:
        name: code
        description: 'Must not be greater than 50 characters.'
        required: false
        example: 'n'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      description:
        name: description
        description: ''
        required: false
        example: 'Eius et animi quos velit et.'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      type:
        name: type
        description: ''
        required: true
        example: montant_fixe
        type: string
        enumValues:
          - pourcentage
          - montant_fixe
          - gratuit
        exampleWasSpecified: false
        nullable: false
        custom: []
      valeur:
        name: valeur
        description: 'Must be at least 0.'
        required: true
        example: 60
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      statut:
        name: statut
        description: ''
        required: true
        example: programmée
        type: string
        enumValues:
          - active
          - inactive
          - programmée
        exampleWasSpecified: false
        nullable: false
        custom: []
      date_debut:
        name: date_debut
        description: 'Must be a valid date.'
        required: false
        example: '2025-04-27T15:58:15'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      date_fin:
        name: date_fin
        description: 'Must be a valid date. Must be a date after or equal to <code>date_debut</code>.'
        required: false
        example: '2051-05-21'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      priorité:
        name: priorité
        description: ''
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      cumulable:
        name: cumulable
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      conditions:
        name: conditions
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      nom: b
      code: 'n'
      description: 'Eius et animi quos velit et.'
      type: montant_fixe
      valeur: 60
      statut: programmée
      date_debut: '2025-04-27T15:58:15'
      date_fin: '2051-05-21'
      priorité: 16
      cumulable: false
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/promotions/featured
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Récupérer les promotions mises en avant'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"status":"success","data":[]}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/promotions/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Afficher une promotion spécifique'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the promotion.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 500
        content: |-
          {
              "message": "Server Error"
          }
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/promotions/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Mettre à jour une promotion'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the promotion.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      nom:
        name: nom
        description: 'Must not be greater than 255 characters.'
        required: true
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      code:
        name: code
        description: ''
        required: false
        example: null
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      description:
        name: description
        description: ''
        required: false
        example: 'Eius et animi quos velit et.'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      type:
        name: type
        description: ''
        required: true
        example: pourcentage
        type: string
        enumValues:
          - pourcentage
          - montant_fixe
          - gratuit
        exampleWasSpecified: false
        nullable: false
        custom: []
      valeur:
        name: valeur
        description: 'Must be at least 0.'
        required: true
        example: 60
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      statut:
        name: statut
        description: ''
        required: true
        example: active
        type: string
        enumValues:
          - active
          - inactive
          - programmée
        exampleWasSpecified: false
        nullable: false
        custom: []
      date_debut:
        name: date_debut
        description: 'Must be a valid date.'
        required: false
        example: '2025-04-27T15:58:18'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      date_fin:
        name: date_fin
        description: 'Must be a valid date. Must be a date after or equal to <code>date_debut</code>.'
        required: false
        example: '2051-05-21'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      priorité:
        name: priorité
        description: ''
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      cumulable:
        name: cumulable
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      conditions:
        name: conditions
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      nom: b
      description: 'Eius et animi quos velit et.'
      type: pourcentage
      valeur: 60
      statut: active
      date_debut: '2025-04-27T15:58:18'
      date_fin: '2051-05-21'
      priorité: 16
      cumulable: true
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/promotions/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Supprimer une promotion'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the promotion.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/promotions/{id}/products'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Récupérer les produits associés à une promotion'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the promotion.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"status":"success","data":{"promotion":{"id":1,"nom":"Test Promotion","code":null,"type":"pourcentage","valeur":"10.00","statut":"active"},"produits":{"current_page":1,"data":[],"first_page_url":"http:\/\/0.0.0.0:8000\/api\/promotions\/1\/products?page=1","from":null,"last_page":1,"last_page_url":"http:\/\/0.0.0.0:8000\/api\/promotions\/1\/products?page=1","links":[{"url":null,"label":"&laquo; Previous","active":false},{"url":"http:\/\/0.0.0.0:8000\/api\/promotions\/1\/products?page=1","label":"1","active":true},{"url":null,"label":"Next &raquo;","active":false}],"next_page_url":null,"path":"http:\/\/0.0.0.0:8000\/api\/promotions\/1\/products","per_page":15,"prev_page_url":null,"to":null,"total":0}}}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/promotions/{id}/related'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Récupérer les promotions similaires à une promotion donnée'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the promotion.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"status":"success","data":[]}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/promotion-events
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Afficher la liste des événements promotionnels'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"status":"success","data":{"current_page":1,"data":[],"first_page_url":"http:\/\/0.0.0.0:8000\/api\/promotion-events?page=1","from":null,"last_page":1,"last_page_url":"http:\/\/0.0.0.0:8000\/api\/promotion-events?page=1","links":[{"url":null,"label":"&laquo; Previous","active":false},{"url":"http:\/\/0.0.0.0:8000\/api\/promotion-events?page=1","label":"1","active":true},{"url":null,"label":"Next &raquo;","active":false}],"next_page_url":null,"path":"http:\/\/0.0.0.0:8000\/api\/promotion-events","per_page":15,"prev_page_url":null,"to":null,"total":0}}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/promotion-events
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Créer un nouvel événement promotionnel'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      nom:
        name: nom
        description: 'Must not be greater than 255 characters.'
        required: true
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      code:
        name: code
        description: 'Must not be greater than 50 characters.'
        required: true
        example: 'n'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      description:
        name: description
        description: ''
        required: false
        example: 'Eius et animi quos velit et.'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      couleur:
        name: couleur
        description: 'Must not be greater than 20 characters.'
        required: false
        example: vdljnikhwaykcmyu
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      icone:
        name: icone
        description: 'Must not be greater than 255 characters.'
        required: false
        example: w
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      actif:
        name: actif
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      date_debut:
        name: date_debut
        description: 'Must be a valid date.'
        required: false
        example: '2025-04-27T15:58:23'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      date_fin:
        name: date_fin
        description: 'Must be a valid date. Must be a date after or equal to <code>date_debut</code>.'
        required: false
        example: '2051-05-21'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      nom: b
      code: 'n'
      description: 'Eius et animi quos velit et.'
      couleur: vdljnikhwaykcmyu
      icone: w
      actif: true
      date_debut: '2025-04-27T15:58:23'
      date_fin: '2051-05-21'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/promotion-events/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Afficher un événement promotionnel spécifique'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the promotion event.'
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 16
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 404
        content: |-
          {
              "message": "No query results for model [App\\Models\\PromotionEvent] 16"
          }
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/promotion-events/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Mettre à jour un événement promotionnel'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the promotion event.'
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 16
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      nom:
        name: nom
        description: 'Must not be greater than 255 characters.'
        required: true
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      code:
        name: code
        description: ''
        required: false
        example: null
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      description:
        name: description
        description: ''
        required: false
        example: 'Eius et animi quos velit et.'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      couleur:
        name: couleur
        description: 'Must not be greater than 20 characters.'
        required: false
        example: vdljnikhwaykcmyu
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      icone:
        name: icone
        description: 'Must not be greater than 255 characters.'
        required: false
        example: w
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      actif:
        name: actif
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      date_debut:
        name: date_debut
        description: 'Must be a valid date.'
        required: false
        example: '2025-04-27T15:58:24'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      date_fin:
        name: date_fin
        description: 'Must be a valid date. Must be a date after or equal to <code>date_debut</code>.'
        required: false
        example: '2051-05-21'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      nom: b
      description: 'Eius et animi quos velit et.'
      couleur: vdljnikhwaykcmyu
      icone: w
      actif: false
      date_debut: '2025-04-27T15:58:24'
      date_fin: '2051-05-21'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/promotion-events/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Supprimer un événement promotionnel'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the promotion event.'
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 16
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/promotion-events/{id}/promotions'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Récupérer les promotions associées à un événement'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the promotion event.'
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 16
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 404
        content: |-
          {
              "message": "No query results for model [App\\Models\\PromotionEvent] 16"
          }
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/regle-remises
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Afficher la liste des règles de remise'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"current_page":1,"data":[{"id":1,"nom":"Remise Premium","description":"Remise standard pour les clients premium","type_client":"premium","valeur":"15.00","type":"pourcentage","priorit\u00e9":0,"active":true,"conditions_supplementaires":null,"created_at":"2025-04-06T11:05:17.000000Z","updated_at":"2025-04-06T11:05:17.000000Z"}],"first_page_url":"http:\/\/0.0.0.0:8000\/api\/regle-remises?page=1","from":1,"last_page":1,"last_page_url":"http:\/\/0.0.0.0:8000\/api\/regle-remises?page=1","links":[{"url":null,"label":"&laquo; Previous","active":false},{"url":"http:\/\/0.0.0.0:8000\/api\/regle-remises?page=1","label":"1","active":true},{"url":null,"label":"Next &raquo;","active":false}],"next_page_url":null,"path":"http:\/\/0.0.0.0:8000\/api\/regle-remises","per_page":15,"prev_page_url":null,"to":1,"total":1}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/regle-remises
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Créer une nouvelle règle de remise'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      nom:
        name: nom
        description: 'Must not be greater than 255 characters.'
        required: true
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      description:
        name: description
        description: ''
        required: false
        example: 'Eius et animi quos velit et.'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      type_client:
        name: type_client
        description: ''
        required: true
        example: affilie
        type: string
        enumValues:
          - standard
          - premium
          - affilie
          - groupe
        exampleWasSpecified: false
        nullable: false
        custom: []
      valeur:
        name: valeur
        description: 'Must be at least 0.'
        required: true
        example: 60
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      type:
        name: type
        description: ''
        required: true
        example: montant_fixe
        type: string
        enumValues:
          - pourcentage
          - montant_fixe
        exampleWasSpecified: false
        nullable: false
        custom: []
      priorité:
        name: priorité
        description: ''
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      active:
        name: active
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      conditions_supplementaires:
        name: conditions_supplementaires
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      nom: b
      description: 'Eius et animi quos velit et.'
      type_client: affilie
      valeur: 60
      type: montant_fixe
      priorité: 16
      active: false
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/regle-remises/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Afficher une règle de remise spécifique'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the regle remise.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"id":1,"nom":"Remise Premium","description":"Remise standard pour les clients premium","type_client":"premium","valeur":"15.00","type":"pourcentage","priorit\u00e9":0,"active":true,"conditions_supplementaires":null,"created_at":"2025-04-06T11:05:17.000000Z","updated_at":"2025-04-06T11:05:17.000000Z"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
      - PATCH
    uri: 'api/regle-remises/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Mettre à jour une règle de remise'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the regle remise.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      nom:
        name: nom
        description: 'Must not be greater than 255 characters.'
        required: true
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      description:
        name: description
        description: ''
        required: false
        example: 'Eius et animi quos velit et.'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      type_client:
        name: type_client
        description: ''
        required: true
        example: standard
        type: string
        enumValues:
          - standard
          - premium
          - affilie
          - groupe
        exampleWasSpecified: false
        nullable: false
        custom: []
      valeur:
        name: valeur
        description: 'Must be at least 0.'
        required: true
        example: 60
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      type:
        name: type
        description: ''
        required: true
        example: montant_fixe
        type: string
        enumValues:
          - pourcentage
          - montant_fixe
        exampleWasSpecified: false
        nullable: false
        custom: []
      priorité:
        name: priorité
        description: ''
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      active:
        name: active
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      conditions_supplementaires:
        name: conditions_supplementaires
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      nom: b
      description: 'Eius et animi quos velit et.'
      type_client: standard
      valeur: 60
      type: montant_fixe
      priorité: 16
      active: true
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/regle-remises/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Supprimer une règle de remise'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the regle remise.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/produits/{produit}/promotions'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Associer une promotion à un produit'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      produit:
        name: produit
        description: 'The produit.'
        required: true
        example: 3
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      produit: 3
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/produits/{produit}/promotions/{promotion}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Détacher une promotion d'un produit"
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      produit:
        name: produit
        description: 'The produit.'
        required: true
        example: 3
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      promotion:
        name: promotion
        description: 'The promotion.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      produit: 3
      promotion: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/collections/{collection}/promotions'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Associer une promotion à une collection'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      collection:
        name: collection
        description: 'The collection.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      collection: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/collections/{collection}/promotions/{promotion}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Détacher une promotion d'une collection"
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      collection:
        name: collection
        description: 'The collection.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      promotion:
        name: promotion
        description: 'The promotion.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      collection: 1
      promotion: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/admin/promotions
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Afficher la liste des promotions'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"status":"success","data":{"current_page":1,"data":[{"id":1,"nom":"Test Promotion","code":null,"description":null,"type":"pourcentage","valeur":"10.00","statut":"active","date_debut":null,"date_fin":null,"priorit\u00e9":0,"cumulable":false,"conditions":null,"created_at":"2025-04-06T10:30:11.000000Z","updated_at":"2025-04-06T10:30:11.000000Z","deleted_at":null,"event_id":null,"image":null,"featured":false},{"id":3,"nom":"chaimaTest","code":"hhh","description":"bbb","type":"pourcentage","valeur":"10.00","statut":"active","date_debut":"2025-04-17T23:00:00.000000Z","date_fin":"2025-04-25T23:00:00.000000Z","priorit\u00e9":0,"cumulable":true,"conditions":null,"created_at":"2025-04-18T00:15:43.000000Z","updated_at":"2025-04-18T00:15:43.000000Z","deleted_at":null,"event_id":null,"image":null,"featured":false},{"id":5,"nom":"ete","code":"etete","description":"fghjkl","type":"pourcentage","valeur":"10.00","statut":"active","date_debut":"2025-04-23T23:00:00.000000Z","date_fin":"2025-04-25T23:00:00.000000Z","priorit\u00e9":0,"cumulable":true,"conditions":null,"created_at":"2025-04-18T01:29:54.000000Z","updated_at":"2025-04-18T01:29:54.000000Z","deleted_at":null,"event_id":null,"image":null,"featured":false}],"first_page_url":"http:\/\/0.0.0.0:8000\/api\/admin\/promotions?page=1","from":1,"last_page":1,"last_page_url":"http:\/\/0.0.0.0:8000\/api\/admin\/promotions?page=1","links":[{"url":null,"label":"&laquo; Previous","active":false},{"url":"http:\/\/0.0.0.0:8000\/api\/admin\/promotions?page=1","label":"1","active":true},{"url":null,"label":"Next &raquo;","active":false}],"next_page_url":null,"path":"http:\/\/0.0.0.0:8000\/api\/admin\/promotions","per_page":15,"prev_page_url":null,"to":3,"total":3}}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/promotions
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Créer une nouvelle promotion'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      nom:
        name: nom
        description: 'Must not be greater than 255 characters.'
        required: true
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      code:
        name: code
        description: 'Must not be greater than 50 characters.'
        required: false
        example: 'n'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      description:
        name: description
        description: ''
        required: false
        example: 'Eius et animi quos velit et.'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      type:
        name: type
        description: ''
        required: true
        example: montant_fixe
        type: string
        enumValues:
          - pourcentage
          - montant_fixe
          - gratuit
        exampleWasSpecified: false
        nullable: false
        custom: []
      valeur:
        name: valeur
        description: 'Must be at least 0.'
        required: true
        example: 60
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      statut:
        name: statut
        description: ''
        required: true
        example: active
        type: string
        enumValues:
          - active
          - inactive
          - programmée
        exampleWasSpecified: false
        nullable: false
        custom: []
      date_debut:
        name: date_debut
        description: 'Must be a valid date.'
        required: false
        example: '2025-04-27T15:58:36'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      date_fin:
        name: date_fin
        description: 'Must be a valid date. Must be a date after or equal to <code>date_debut</code>.'
        required: false
        example: '2051-05-21'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      priorité:
        name: priorité
        description: ''
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      cumulable:
        name: cumulable
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      conditions:
        name: conditions
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      nom: b
      code: 'n'
      description: 'Eius et animi quos velit et.'
      type: montant_fixe
      valeur: 60
      statut: active
      date_debut: '2025-04-27T15:58:36'
      date_fin: '2051-05-21'
      priorité: 16
      cumulable: true
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/promotions/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Afficher une promotion spécifique'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the promotion.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 500
        content: |-
          {
              "message": "Server Error"
          }
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
      - PATCH
    uri: 'api/admin/promotions/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Mettre à jour une promotion'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the promotion.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      nom:
        name: nom
        description: 'Must not be greater than 255 characters.'
        required: true
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      code:
        name: code
        description: ''
        required: false
        example: null
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      description:
        name: description
        description: ''
        required: false
        example: 'Eius et animi quos velit et.'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      type:
        name: type
        description: ''
        required: true
        example: pourcentage
        type: string
        enumValues:
          - pourcentage
          - montant_fixe
          - gratuit
        exampleWasSpecified: false
        nullable: false
        custom: []
      valeur:
        name: valeur
        description: 'Must be at least 0.'
        required: true
        example: 60
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      statut:
        name: statut
        description: ''
        required: true
        example: inactive
        type: string
        enumValues:
          - active
          - inactive
          - programmée
        exampleWasSpecified: false
        nullable: false
        custom: []
      date_debut:
        name: date_debut
        description: 'Must be a valid date.'
        required: false
        example: '2025-04-27T15:58:39'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      date_fin:
        name: date_fin
        description: 'Must be a valid date. Must be a date after or equal to <code>date_debut</code>.'
        required: false
        example: '2051-05-21'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      priorité:
        name: priorité
        description: ''
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      cumulable:
        name: cumulable
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      conditions:
        name: conditions
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      nom: b
      description: 'Eius et animi quos velit et.'
      type: pourcentage
      valeur: 60
      statut: inactive
      date_debut: '2025-04-27T15:58:39'
      date_fin: '2051-05-21'
      priorité: 16
      cumulable: true
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/admin/promotions/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Supprimer une promotion'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the promotion.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/admin/regle-remises
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Afficher la liste des règles de remise'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"current_page":1,"data":[{"id":1,"nom":"Remise Premium","description":"Remise standard pour les clients premium","type_client":"premium","valeur":"15.00","type":"pourcentage","priorit\u00e9":0,"active":true,"conditions_supplementaires":null,"created_at":"2025-04-06T11:05:17.000000Z","updated_at":"2025-04-06T11:05:17.000000Z"}],"first_page_url":"http:\/\/0.0.0.0:8000\/api\/admin\/regle-remises?page=1","from":1,"last_page":1,"last_page_url":"http:\/\/0.0.0.0:8000\/api\/admin\/regle-remises?page=1","links":[{"url":null,"label":"&laquo; Previous","active":false},{"url":"http:\/\/0.0.0.0:8000\/api\/admin\/regle-remises?page=1","label":"1","active":true},{"url":null,"label":"Next &raquo;","active":false}],"next_page_url":null,"path":"http:\/\/0.0.0.0:8000\/api\/admin\/regle-remises","per_page":15,"prev_page_url":null,"to":1,"total":1}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/regle-remises
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Créer une nouvelle règle de remise'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      nom:
        name: nom
        description: 'Must not be greater than 255 characters.'
        required: true
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      description:
        name: description
        description: ''
        required: false
        example: 'Eius et animi quos velit et.'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      type_client:
        name: type_client
        description: ''
        required: true
        example: premium
        type: string
        enumValues:
          - standard
          - premium
          - affilie
          - groupe
        exampleWasSpecified: false
        nullable: false
        custom: []
      valeur:
        name: valeur
        description: 'Must be at least 0.'
        required: true
        example: 60
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      type:
        name: type
        description: ''
        required: true
        example: montant_fixe
        type: string
        enumValues:
          - pourcentage
          - montant_fixe
        exampleWasSpecified: false
        nullable: false
        custom: []
      priorité:
        name: priorité
        description: ''
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      active:
        name: active
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      conditions_supplementaires:
        name: conditions_supplementaires
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      nom: b
      description: 'Eius et animi quos velit et.'
      type_client: premium
      valeur: 60
      type: montant_fixe
      priorité: 16
      active: true
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/regle-remises/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Afficher une règle de remise spécifique'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the regle remise.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"id":1,"nom":"Remise Premium","description":"Remise standard pour les clients premium","type_client":"premium","valeur":"15.00","type":"pourcentage","priorit\u00e9":0,"active":true,"conditions_supplementaires":null,"created_at":"2025-04-06T11:05:17.000000Z","updated_at":"2025-04-06T11:05:17.000000Z"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
      - PATCH
    uri: 'api/admin/regle-remises/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Mettre à jour une règle de remise'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the regle remise.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      nom:
        name: nom
        description: 'Must not be greater than 255 characters.'
        required: true
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      description:
        name: description
        description: ''
        required: false
        example: 'Eius et animi quos velit et.'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      type_client:
        name: type_client
        description: ''
        required: true
        example: premium
        type: string
        enumValues:
          - standard
          - premium
          - affilie
          - groupe
        exampleWasSpecified: false
        nullable: false
        custom: []
      valeur:
        name: valeur
        description: 'Must be at least 0.'
        required: true
        example: 60
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      type:
        name: type
        description: ''
        required: true
        example: montant_fixe
        type: string
        enumValues:
          - pourcentage
          - montant_fixe
        exampleWasSpecified: false
        nullable: false
        custom: []
      priorité:
        name: priorité
        description: ''
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      active:
        name: active
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      conditions_supplementaires:
        name: conditions_supplementaires
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      nom: b
      description: 'Eius et animi quos velit et.'
      type_client: premium
      valeur: 60
      type: montant_fixe
      priorité: 16
      active: true
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/admin/regle-remises/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Supprimer une règle de remise'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the regle remise.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/admin/groupes-attributs
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display a listing of the resource.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '[{"id":2,"nom":"Caract\u00e9ristiques g\u00e9n\u00e9rales","description":"Groupe par d\u00e9faut pour les caract\u00e9ristiques migr\u00e9es","created_at":"2025-04-08T12:36:05.000000Z","updated_at":"2025-04-08T12:36:05.000000Z","deleted_at":null,"attributs":[{"id":3,"nom":"Couleur","description":"Couleur du produit","type_valeur":"texte","groupe_id":2,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-08T12:43:28.000000Z","updated_at":"2025-04-08T12:43:28.000000Z","deleted_at":null},{"id":4,"nom":"Taille","description":"Taille du produit","type_valeur":"texte","groupe_id":2,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-09T20:37:11.000000Z","updated_at":"2025-04-09T20:37:11.000000Z","deleted_at":null},{"id":5,"nom":"Materiau","description":"Materiau principal du produit","type_valeur":"texte","groupe_id":2,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-09T20:38:25.000000Z","updated_at":"2025-04-09T20:38:25.000000Z","deleted_at":null},{"id":6,"nom":"Dimensions","description":"Dimensions du produit (L x l x H)","type_valeur":"texte","groupe_id":2,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-09T20:39:03.000000Z","updated_at":"2025-04-09T20:39:03.000000Z","deleted_at":null}]},{"id":3,"nom":"Caract\u00e9ristiques du linge de lit","description":"Attributs sp\u00e9cifiques aux produits de literie","created_at":"2025-04-18T19:54:03.000000Z","updated_at":"2025-04-18T19:54:03.000000Z","deleted_at":null,"attributs":[{"id":7,"nom":"Fils au cm\u00b2","description":"Nombre de fils au centim\u00e8tre carr\u00e9 (qualit\u00e9 du tissu)","type_valeur":"nombre","groupe_id":3,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-18T19:54:19.000000Z","updated_at":"2025-04-18T19:54:19.000000Z","deleted_at":null},{"id":8,"nom":"Entretien","description":"Instructions d''entretien du produit","type_valeur":"texte","groupe_id":3,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-18T19:54:35.000000Z","updated_at":"2025-04-18T19:54:35.000000Z","deleted_at":null},{"id":9,"nom":"Certification","description":"Certifications et labels du produit","type_valeur":"texte","groupe_id":3,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-18T19:54:51.000000Z","updated_at":"2025-04-18T19:54:51.000000Z","deleted_at":null},{"id":10,"nom":"Saison","description":"Saison recommand\u00e9e pour l''utilisation","type_valeur":"texte","groupe_id":3,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-18T19:55:06.000000Z","updated_at":"2025-04-18T19:55:06.000000Z","deleted_at":null},{"id":11,"nom":"Contenu du set","description":"\u00c9l\u00e9ments inclus dans le set","type_valeur":"texte","groupe_id":3,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-18T19:55:30.000000Z","updated_at":"2025-04-18T19:55:30.000000Z","deleted_at":null}]},{"id":4,"nom":"D\u00e9veloppement durable","description":"Caract\u00e9ristiques li\u00e9es au d\u00e9veloppement durable et \u00e0 l''impact environnemental","created_at":"2025-04-18T19:59:15.000000Z","updated_at":"2025-04-18T19:59:15.000000Z","deleted_at":null,"attributs":[{"id":12,"nom":"Empreinte carbone","description":"Empreinte carbone du produit","type_valeur":"texte","groupe_id":4,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-18T19:59:39.000000Z","updated_at":"2025-04-18T19:59:39.000000Z","deleted_at":null},{"id":13,"nom":"Origine","description":"Pays d''origine du produit","type_valeur":"texte","groupe_id":4,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-18T20:00:15.000000Z","updated_at":"2025-04-18T20:00:15.000000Z","deleted_at":null},{"id":14,"nom":"Recyclable","description":"Indique si le produit est recyclable","type_valeur":"booleen","groupe_id":4,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-18T20:00:43.000000Z","updated_at":"2025-04-18T20:00:43.000000Z","deleted_at":null}]}]'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/groupes-attributs
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Store a newly created resource in storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/groupes-attributs/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified resource.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the groupes attribut.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 404
        content: '{"error":"Groupe d''attributs non trouv\u00e9","message":"SQLSTATE[22P02]: Invalid text representation: 7 ERROR:  invalid input syntax for type bigint: \"architecto\"\nCONTEXT:  unnamed portal parameter $1 = ''...'' (Connection: pgsql, SQL: select * from \"groupes_attributs\" where \"groupes_attributs\".\"id\" = architecto and \"groupes_attributs\".\"deleted_at\" is null limit 1)"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
      - PATCH
    uri: 'api/admin/groupes-attributs/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update the specified resource in storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the groupes attribut.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/admin/groupes-attributs/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove the specified resource from storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the groupes attribut.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/admin/attributs
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display a listing of the resource.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '[{"id":3,"nom":"Couleur","description":"Couleur du produit","type_valeur":"texte","groupe_id":2,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-08T12:43:28.000000Z","updated_at":"2025-04-08T12:43:28.000000Z","deleted_at":null,"groupe":{"id":2,"nom":"Caract\u00e9ristiques g\u00e9n\u00e9rales","description":"Groupe par d\u00e9faut pour les caract\u00e9ristiques migr\u00e9es","created_at":"2025-04-08T12:36:05.000000Z","updated_at":"2025-04-08T12:36:05.000000Z","deleted_at":null}},{"id":4,"nom":"Taille","description":"Taille du produit","type_valeur":"texte","groupe_id":2,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-09T20:37:11.000000Z","updated_at":"2025-04-09T20:37:11.000000Z","deleted_at":null,"groupe":{"id":2,"nom":"Caract\u00e9ristiques g\u00e9n\u00e9rales","description":"Groupe par d\u00e9faut pour les caract\u00e9ristiques migr\u00e9es","created_at":"2025-04-08T12:36:05.000000Z","updated_at":"2025-04-08T12:36:05.000000Z","deleted_at":null}},{"id":5,"nom":"Materiau","description":"Materiau principal du produit","type_valeur":"texte","groupe_id":2,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-09T20:38:25.000000Z","updated_at":"2025-04-09T20:38:25.000000Z","deleted_at":null,"groupe":{"id":2,"nom":"Caract\u00e9ristiques g\u00e9n\u00e9rales","description":"Groupe par d\u00e9faut pour les caract\u00e9ristiques migr\u00e9es","created_at":"2025-04-08T12:36:05.000000Z","updated_at":"2025-04-08T12:36:05.000000Z","deleted_at":null}},{"id":6,"nom":"Dimensions","description":"Dimensions du produit (L x l x H)","type_valeur":"texte","groupe_id":2,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-09T20:39:03.000000Z","updated_at":"2025-04-09T20:39:03.000000Z","deleted_at":null,"groupe":{"id":2,"nom":"Caract\u00e9ristiques g\u00e9n\u00e9rales","description":"Groupe par d\u00e9faut pour les caract\u00e9ristiques migr\u00e9es","created_at":"2025-04-08T12:36:05.000000Z","updated_at":"2025-04-08T12:36:05.000000Z","deleted_at":null}},{"id":7,"nom":"Fils au cm\u00b2","description":"Nombre de fils au centim\u00e8tre carr\u00e9 (qualit\u00e9 du tissu)","type_valeur":"nombre","groupe_id":3,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-18T19:54:19.000000Z","updated_at":"2025-04-18T19:54:19.000000Z","deleted_at":null,"groupe":{"id":3,"nom":"Caract\u00e9ristiques du linge de lit","description":"Attributs sp\u00e9cifiques aux produits de literie","created_at":"2025-04-18T19:54:03.000000Z","updated_at":"2025-04-18T19:54:03.000000Z","deleted_at":null}},{"id":8,"nom":"Entretien","description":"Instructions d''entretien du produit","type_valeur":"texte","groupe_id":3,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-18T19:54:35.000000Z","updated_at":"2025-04-18T19:54:35.000000Z","deleted_at":null,"groupe":{"id":3,"nom":"Caract\u00e9ristiques du linge de lit","description":"Attributs sp\u00e9cifiques aux produits de literie","created_at":"2025-04-18T19:54:03.000000Z","updated_at":"2025-04-18T19:54:03.000000Z","deleted_at":null}},{"id":9,"nom":"Certification","description":"Certifications et labels du produit","type_valeur":"texte","groupe_id":3,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-18T19:54:51.000000Z","updated_at":"2025-04-18T19:54:51.000000Z","deleted_at":null,"groupe":{"id":3,"nom":"Caract\u00e9ristiques du linge de lit","description":"Attributs sp\u00e9cifiques aux produits de literie","created_at":"2025-04-18T19:54:03.000000Z","updated_at":"2025-04-18T19:54:03.000000Z","deleted_at":null}},{"id":10,"nom":"Saison","description":"Saison recommand\u00e9e pour l''utilisation","type_valeur":"texte","groupe_id":3,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-18T19:55:06.000000Z","updated_at":"2025-04-18T19:55:06.000000Z","deleted_at":null,"groupe":{"id":3,"nom":"Caract\u00e9ristiques du linge de lit","description":"Attributs sp\u00e9cifiques aux produits de literie","created_at":"2025-04-18T19:54:03.000000Z","updated_at":"2025-04-18T19:54:03.000000Z","deleted_at":null}},{"id":11,"nom":"Contenu du set","description":"\u00c9l\u00e9ments inclus dans le set","type_valeur":"texte","groupe_id":3,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-18T19:55:30.000000Z","updated_at":"2025-04-18T19:55:30.000000Z","deleted_at":null,"groupe":{"id":3,"nom":"Caract\u00e9ristiques du linge de lit","description":"Attributs sp\u00e9cifiques aux produits de literie","created_at":"2025-04-18T19:54:03.000000Z","updated_at":"2025-04-18T19:54:03.000000Z","deleted_at":null}},{"id":12,"nom":"Empreinte carbone","description":"Empreinte carbone du produit","type_valeur":"texte","groupe_id":4,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-18T19:59:39.000000Z","updated_at":"2025-04-18T19:59:39.000000Z","deleted_at":null,"groupe":{"id":4,"nom":"D\u00e9veloppement durable","description":"Caract\u00e9ristiques li\u00e9es au d\u00e9veloppement durable et \u00e0 l''impact environnemental","created_at":"2025-04-18T19:59:15.000000Z","updated_at":"2025-04-18T19:59:15.000000Z","deleted_at":null}},{"id":13,"nom":"Origine","description":"Pays d''origine du produit","type_valeur":"texte","groupe_id":4,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-18T20:00:15.000000Z","updated_at":"2025-04-18T20:00:15.000000Z","deleted_at":null,"groupe":{"id":4,"nom":"D\u00e9veloppement durable","description":"Caract\u00e9ristiques li\u00e9es au d\u00e9veloppement durable et \u00e0 l''impact environnemental","created_at":"2025-04-18T19:59:15.000000Z","updated_at":"2025-04-18T19:59:15.000000Z","deleted_at":null}},{"id":14,"nom":"Recyclable","description":"Indique si le produit est recyclable","type_valeur":"booleen","groupe_id":4,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-18T20:00:43.000000Z","updated_at":"2025-04-18T20:00:43.000000Z","deleted_at":null,"groupe":{"id":4,"nom":"D\u00e9veloppement durable","description":"Caract\u00e9ristiques li\u00e9es au d\u00e9veloppement durable et \u00e0 l''impact environnemental","created_at":"2025-04-18T19:59:15.000000Z","updated_at":"2025-04-18T19:59:15.000000Z","deleted_at":null}}]'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/attributs
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Store a newly created resource in storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/attributs/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified resource.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the attribut.'
        required: true
        example: 3
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 3
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"id":3,"nom":"Couleur","description":"Couleur du produit","type_valeur":"texte","groupe_id":2,"obligatoire":false,"filtrable":true,"comparable":true,"created_at":"2025-04-08T12:43:28.000000Z","updated_at":"2025-04-08T12:43:28.000000Z","deleted_at":null,"groupe":{"id":2,"nom":"Caract\u00e9ristiques g\u00e9n\u00e9rales","description":"Groupe par d\u00e9faut pour les caract\u00e9ristiques migr\u00e9es","created_at":"2025-04-08T12:36:05.000000Z","updated_at":"2025-04-08T12:36:05.000000Z","deleted_at":null},"sous_categories":[{"id":21,"nom_sous_categorie":"Linge de lit","description_sous_categorie":"Lit","categorie_id":25,"created_at":"2025-04-09T00:00:00.000000Z","updated_at":"2025-04-09T00:00:00.000000Z","pivot":{"attribut_id":3,"sous_categorie_id":21,"obligatoire":true,"created_at":"2025-04-09T17:18:46.000000Z","updated_at":"2025-04-09T17:18:46.000000Z"}}],"produit_valeurs":[{"id":3,"produit_id":3,"attribut_id":3,"valeur_texte":"Blanc","valeur_nombre":null,"valeur_date":null,"valeur_booleen":null,"created_at":"2025-04-09T20:51:38.000000Z","updated_at":"2025-04-09T20:51:38.000000Z"},{"id":7,"produit_id":4,"attribut_id":3,"valeur_texte":"Blanc cass\u00e9","valeur_nombre":null,"valeur_date":null,"valeur_booleen":null,"created_at":"2025-04-09T20:51:41.000000Z","updated_at":"2025-04-09T20:51:41.000000Z"},{"id":11,"produit_id":5,"attribut_id":3,"valeur_texte":"Gris","valeur_nombre":null,"valeur_date":null,"valeur_booleen":null,"created_at":"2025-04-09T20:51:44.000000Z","updated_at":"2025-04-09T20:51:44.000000Z"},{"id":30,"produit_id":18,"attribut_id":3,"valeur_texte":"Gris perle","valeur_nombre":null,"valeur_date":null,"valeur_booleen":null,"created_at":"2025-04-18T19:58:06.000000Z","updated_at":"2025-04-18T19:58:06.000000Z"}]}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
      - PATCH
    uri: 'api/admin/attributs/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update the specified resource in storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the attribut.'
        required: true
        example: 3
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 3
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/admin/attributs/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove the specified resource from storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the attribut.'
        required: true
        example: 3
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 3
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/admin/attributs/{id}/sous-categories/{sousCategorieId}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Associer un attribut à une sous-catégorie'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the attribut.'
        required: true
        example: 3
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      sousCategorieId:
        name: sousCategorieId
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 3
      sousCategorieId: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/admin/attributs/{id}/sous-categories/{sousCategorieId}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Détacher un attribut d'une sous-catégorie"
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the attribut.'
        required: true
        example: 3
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      sousCategorieId:
        name: sousCategorieId
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 3
      sousCategorieId: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/produits/{produit_id}/variantes'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Store a newly created resource in storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      produit_id:
        name: produit_id
        description: 'The ID of the produit.'
        required: true
        example: 3
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      produit_id: 3
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/variantes/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified resource.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the variante.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 404
        content: '{"error":"Variante non trouv\u00e9e","message":"SQLSTATE[22P02]: Invalid text representation: 7 ERROR:  invalid input syntax for type bigint: \"architecto\"\nCONTEXT:  unnamed portal parameter $1 = ''...'' (Connection: pgsql, SQL: select * from \"produit_variantes\" where \"produit_variantes\".\"id\" = architecto and \"produit_variantes\".\"deleted_at\" is null limit 1)"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
      - PATCH
    uri: 'api/variantes/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update the specified resource in storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the variante.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/variantes/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove the specified resource from storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the variante.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PATCH
    uri: 'api/variantes/{id}/stock'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Mettre à jour le stock d'une variante"
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the variante.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/images/upload
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Upload an image for a model.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: multipart/form-data
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      model_type:
        name: model_type
        description: ''
        required: true
        example: marque
        type: string
        enumValues:
          - produit
          - categorie
          - sous_categorie
          - sous_sous_categorie
          - collection
          - marque
          - produit_variante
          - carousel_slide
        exampleWasSpecified: false
        nullable: false
        custom: []
      model_id:
        name: model_id
        description: ''
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      image:
        name: image
        description: 'Must be an image. Must not be greater than 10240 kilobytes.'
        required: true
        example: null
        type: file
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      is_primary:
        name: is_primary
        description: '10MB max.'
        required: false
        example: '0'
        type: string
        enumValues:
          - 'true'
          - 'false'
          - '0'
          - '1'
        exampleWasSpecified: false
        nullable: true
        custom: []
      alt_text:
        name: alt_text
        description: 'Must not be greater than 255 characters.'
        required: false
        example: 'n'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      title:
        name: title
        description: 'Must not be greater than 255 characters.'
        required: false
        example: g
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      model_type: marque
      model_id: 16
      is_primary: '0'
      alt_text: 'n'
      title: g
    fileParameters:
      image: null
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/images/upload-multiple
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Upload multiple images for a model.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: multipart/form-data
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      model_type:
        name: model_type
        description: ''
        required: true
        example: collection
        type: string
        enumValues:
          - produit
          - categorie
          - sous_categorie
          - sous_sous_categorie
          - collection
          - marque
          - produit_variante
          - carousel_slide
        exampleWasSpecified: false
        nullable: false
        custom: []
      model_id:
        name: model_id
        description: ''
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      images:
        name: images
        description: 'Must be an image. Must not be greater than 10240 kilobytes.'
        required: true
        example:
          - null
        type: 'file[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      alt_text:
        name: alt_text
        description: '10MB max. Must not be greater than 255 characters.'
        required: false
        example: 'n'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      title:
        name: title
        description: 'Must not be greater than 255 characters.'
        required: false
        example: g
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      model_type: collection
      model_id: 16
      alt_text: 'n'
      title: g
    fileParameters:
      images:
        - null
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/images/get
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get all images for a model.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      model_type:
        name: model_type
        description: ''
        required: true
        example: sous_sous_categorie
        type: string
        enumValues:
          - produit
          - categorie
          - sous_categorie
          - sous_sous_categorie
          - collection
          - marque
          - produit_variante
          - carousel_slide
        exampleWasSpecified: false
        nullable: false
        custom: []
      model_id:
        name: model_id
        description: ''
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      model_type: sous_sous_categorie
      model_id: 16
    fileParameters: []
    responses:
      -
        status: 404
        content: '{"error":"Model not found"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/images/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update image details.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the image.'
        required: true
        example: 2
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 2
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      is_primary:
        name: is_primary
        description: ''
        required: false
        example: 'false'
        type: string
        enumValues:
          - 'true'
          - 'false'
          - '0'
          - '1'
        exampleWasSpecified: false
        nullable: true
        custom: []
      alt_text:
        name: alt_text
        description: 'Must not be greater than 255 characters.'
        required: false
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      title:
        name: title
        description: 'Must not be greater than 255 characters.'
        required: false
        example: 'n'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      order:
        name: order
        description: 'Must be at least 0.'
        required: false
        example: 84
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      is_primary: 'false'
      alt_text: b
      title: 'n'
      order: 84
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/images/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Delete an image.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the image.'
        required: true
        example: 2
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 2
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/images/reorder
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Reorder images for a model.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      model_type:
        name: model_type
        description: ''
        required: true
        example: sous_sous_categorie
        type: string
        enumValues:
          - produit
          - categorie
          - sous_categorie
          - sous_sous_categorie
          - collection
          - marque
          - produit_variante
          - carousel_slide
        exampleWasSpecified: false
        nullable: false
        custom: []
      model_id:
        name: model_id
        description: ''
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      image_ids:
        name: image_ids
        description: 'The <code>id</code> of an existing record in the images table.'
        required: true
        example:
          - 16
        type: 'integer[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      model_type: sous_sous_categorie
      model_id: 16
      image_ids:
        - 16
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/images/serve/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Serve an image from storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the serve.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 500
        content: |-
          {
              "message": "Server Error"
          }
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/images/thumbnail/{id}/{size}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Serve a thumbnail of an image from storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the thumbnail.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      size:
        name: size
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
      size: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 400
        content: |-
          {
              "message": "Invalid thumbnail size"
          }
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/images/file/{path}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Serve an image by path from storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      path:
        name: path
        description: ''
        required: true
        example: '|{+-0p'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      path: '|{+-0p'
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 404
        content: |-
          {
              "message": "Image not found in storage"
          }
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/carousels
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display a listing of the resource.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '[{"id":1,"nom":"Nouveau Carrousel","description":"Description du nouveau carrousel","actif":true,"ordre":0,"created_at":"2025-04-18T11:42:03.000000Z","updated_at":"2025-04-18T11:42:03.000000Z","deleted_at":null,"slides":[{"id":1,"carousel_id":1,"titre":"Collection d''\u00e9t\u00e9","description":"Nouvelle collection 2025","bouton_texte":"En savoir plus","bouton_lien":"\/collection-ete","ordre":0,"actif":true,"created_at":"2025-04-18T11:42:11.000000Z","updated_at":"2025-04-18T20:10:31.000000Z","deleted_at":null},{"id":2,"carousel_id":1,"titre":"Pack parisien","description":"Pack parisien 2024","bouton_texte":"En savoir plus","bouton_lien":"\/pack-parisien","ordre":1,"actif":true,"created_at":"2025-04-18T12:26:29.000000Z","updated_at":"2025-04-18T19:50:26.000000Z","deleted_at":null}]}]'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/carousels
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Store a newly created resource in storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/carousels/actifs
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get active carousels for frontoffice.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '[{"id":1,"nom":"Nouveau Carrousel","description":"Description du nouveau carrousel","actif":true,"ordre":0,"created_at":"2025-04-18T11:42:03.000000Z","updated_at":"2025-04-18T11:42:03.000000Z","deleted_at":null,"slides":[{"id":1,"carousel_id":1,"titre":"Collection d''\u00e9t\u00e9","description":"Nouvelle collection 2025","bouton_texte":"En savoir plus","bouton_lien":"\/collection-ete","ordre":0,"actif":true,"created_at":"2025-04-18T11:42:11.000000Z","updated_at":"2025-04-18T20:10:31.000000Z","deleted_at":null},{"id":2,"carousel_id":1,"titre":"Pack parisien","description":"Pack parisien 2024","bouton_texte":"En savoir plus","bouton_lien":"\/pack-parisien","ordre":1,"actif":true,"created_at":"2025-04-18T12:26:29.000000Z","updated_at":"2025-04-18T19:50:26.000000Z","deleted_at":null}]}]'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/carousels/slides
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display a listing of the resource.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 422
        content: '{"errors":{"carousel_id":["The carousel id field is required."]}}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/carousels/slides
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Store a newly created resource in storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/carousels/slides/reorder
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Reorder slides.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/carousels/slides/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified resource.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the slide.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 404
        content: '{"error":"Slide non trouv\u00e9","message":"SQLSTATE[22P02]: Invalid text representation: 7 ERROR:  invalid input syntax for type bigint: \"architecto\"\nCONTEXT:  unnamed portal parameter $1 = ''...'' (Connection: pgsql, SQL: select * from \"carousel_slides\" where \"carousel_slides\".\"id\" = architecto and \"carousel_slides\".\"deleted_at\" is null limit 1)"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/carousels/slides/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update the specified resource in storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the slide.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/carousels/slides/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove the specified resource from storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the slide.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/carousels/{id}/slides'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get slides for a specific carousel.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the carousel.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '[{"id":1,"carousel_id":1,"titre":"Collection d''\u00e9t\u00e9","description":"Nouvelle collection 2025","bouton_texte":"En savoir plus","bouton_lien":"\/collection-ete","ordre":0,"actif":true,"created_at":"2025-04-18T11:42:11.000000Z","updated_at":"2025-04-18T20:10:31.000000Z","deleted_at":null,"primary_image_url":"0.0.0.0:8000\/api\/images\/serve\/9","images":[{"id":9,"path":"carousels\/1\/slides\/1\/bu0_p2xXKO0ybx.jpg","filename":"BU0.jpg","disk":"s3","mime_type":"image\/jpeg","size":645540,"alt_text":"Image de diapositive","title":"Diapositive","imageable_type":"App\\Models\\CarouselSlide","imageable_id":1,"is_primary":true,"order":0,"metadata":{"width":2048,"height":1073,"original_filename":"BU0.jpg","extension":"jpg"},"created_at":"2025-04-18T18:40:03.000000Z","updated_at":"2025-04-18T18:40:03.000000Z","deleted_at":null}]},{"id":2,"carousel_id":1,"titre":"Pack parisien","description":"Pack parisien 2024","bouton_texte":"En savoir plus","bouton_lien":"\/pack-parisien","ordre":1,"actif":true,"created_at":"2025-04-18T12:26:29.000000Z","updated_at":"2025-04-18T19:50:26.000000Z","deleted_at":null,"primary_image_url":"0.0.0.0:8000\/api\/images\/serve\/10","images":[{"id":10,"path":"carousels\/1\/slides\/2\/co0_DfeS7xH0Cx.webp","filename":"CO0.webp","disk":"s3","mime_type":"image\/webp","size":196946,"alt_text":"Image de diapositive","title":"Diapositive","imageable_type":"App\\Models\\CarouselSlide","imageable_id":2,"is_primary":true,"order":0,"metadata":{"width":2880,"height":1916,"original_filename":"CO0.webp","extension":"webp"},"created_at":"2025-04-18T18:41:45.000000Z","updated_at":"2025-04-18T18:41:45.000000Z","deleted_at":null}]}]'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/carousels/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified resource.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the carousel.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"id":1,"nom":"Nouveau Carrousel","description":"Description du nouveau carrousel","actif":true,"ordre":0,"created_at":"2025-04-18T11:42:03.000000Z","updated_at":"2025-04-18T11:42:03.000000Z","deleted_at":null,"slides":[{"id":1,"carousel_id":1,"titre":"Collection d''\u00e9t\u00e9","description":"Nouvelle collection 2025","bouton_texte":"En savoir plus","bouton_lien":"\/collection-ete","ordre":0,"actif":true,"created_at":"2025-04-18T11:42:11.000000Z","updated_at":"2025-04-18T20:10:31.000000Z","deleted_at":null},{"id":2,"carousel_id":1,"titre":"Pack parisien","description":"Pack parisien 2024","bouton_texte":"En savoir plus","bouton_lien":"\/pack-parisien","ordre":1,"actif":true,"created_at":"2025-04-18T12:26:29.000000Z","updated_at":"2025-04-18T19:50:26.000000Z","deleted_at":null}]}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/carousels/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update the specified resource in storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the carousel.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/carousels/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove the specified resource from storage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the carousel.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/wishlist
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Récupérer la liste de souhaits de l'utilisateur connecté"
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"status":"error","message":"Vous devez \u00eatre connect\u00e9 pour acc\u00e9der \u00e0 votre liste de souhaits"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/wishlist/items
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Ajouter un produit à la liste de souhaits'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      produit_id:
        name: produit_id
        description: 'The <code>id</code> of an existing record in the produits table.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      variante_id:
        name: variante_id
        description: 'The <code>id</code> of an existing record in the produit_variantes table.'
        required: false
        example: null
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      note:
        name: note
        description: 'Must not be greater than 1000 characters.'
        required: false
        example: 'n'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      produit_id: architecto
      note: 'n'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/wishlist/items/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Supprimer un item de la liste de souhaits'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the item.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/wishlist/check/{produitId}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Vérifier si un produit est dans la liste de souhaits'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      produitId:
        name: produitId
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      produitId: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"status":"success","data":{"in_wishlist":false}}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/wishlist/items/{id}/move-to-cart'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Déplacer un item de la liste de souhaits vers le panier'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the item.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      quantite:
        name: quantite
        description: 'Must be at least 1. Must not be greater than 100.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      quantite: 1
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/stock/produits/{produitId}/historique'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Obtenir l'historique des mouvements de stock d'un produit"
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      produitId:
        name: produitId
        description: ''
        required: true
        example: 3
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      produitId: 3
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"produit":{"id":3,"nom":"Parure de lit \u00c9l\u00e9gance","reference":"8-1","quantite_actuelle":50,"en_stock":true,"stock_limite":false},"historique":{"current_page":1,"data":[],"first_page_url":"http:\/\/0.0.0.0:8000\/api\/stock\/produits\/3\/historique?page=1","from":null,"last_page":1,"last_page_url":"http:\/\/0.0.0.0:8000\/api\/stock\/produits\/3\/historique?page=1","links":[{"url":null,"label":"&laquo; Previous","active":false},{"url":"http:\/\/0.0.0.0:8000\/api\/stock\/produits\/3\/historique?page=1","label":"1","active":true},{"url":null,"label":"Next &raquo;","active":false}],"next_page_url":null,"path":"http:\/\/0.0.0.0:8000\/api\/stock\/produits\/3\/historique","per_page":15,"prev_page_url":null,"to":null,"total":0}}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/stock/produits/{produitId}/ajouter'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Ajouter du stock à un produit'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      produitId:
        name: produitId
        description: ''
        required: true
        example: 3
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      produitId: 3
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/stock/produits/{produitId}/retirer'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Retirer du stock d'un produit"
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      produitId:
        name: produitId
        description: ''
        required: true
        example: 3
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      produitId: 3
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/stock/produits/{produitId}/ajuster'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Ajuster le stock d'un produit à une valeur spécifique"
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      produitId:
        name: produitId
        description: ''
        required: true
        example: 3
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      produitId: 3
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/stock/ruptures
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Obtenir les produits en rupture de stock'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"count":0,"produits":[]}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/stock/limites
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Obtenir les produits en stock limité'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"count":1,"seuil":5,"produits":[{"id":18,"nom":"Parure de lit classy","reference":"5-6","quantite":1,"marque":"J-Line","image":"0.0.0.0:8000\/api\/images\/serve\/13"}]}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/partner-requests
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display partner requests'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"status":"success","data":[{"id":9,"user_id":null,"company_name":"Test Company","business_type":"Retail","motivation":"Test motivation","website":"https:\/\/example.com","phone":"+*********0","address":"123 Test St","status":"pending","admin_notes":null,"processed_at":null,"processed_by":null,"created_at":"2025-04-27T15:48:42.000000Z","updated_at":"2025-04-27T15:48:42.000000Z","name":"Test User","email":"<EMAIL>"},{"id":8,"user_id":null,"company_name":"fff","business_type":"Retail","motivation":"gggggggggggggggggggggggggg","website":"https:\/\/www.carreblanc.com\/","phone":"+***********","address":"gggggggggggggggggggggggggggggggggggggg","status":"pending","admin_notes":null,"processed_at":null,"processed_by":null,"created_at":"2025-04-27T14:45:36.000000Z","updated_at":"2025-04-27T14:45:36.000000Z","name":"sirine","email":"<EMAIL>"},{"id":7,"user_id":null,"company_name":"fff","business_type":"Retail","motivation":"ghghghghghghghghghghghghg","website":"https:\/\/www.carreblanc.com\/","phone":"+***********","address":"hjhjhjhjhjhjhjhjhjhj","status":"pending","admin_notes":null,"processed_at":null,"processed_by":null,"created_at":"2025-04-27T14:44:56.000000Z","updated_at":"2025-04-27T14:44:56.000000Z","name":"sirine","email":"<EMAIL>"},{"id":6,"user_id":null,"company_name":"fff","business_type":"Retail","motivation":"bbbbbbbbbbbbbbbbb","website":"https:\/\/www.carreblanc.com\/","phone":"+***********","address":"ertyujhgffghjk","status":"pending","admin_notes":null,"processed_at":null,"processed_by":null,"created_at":"2025-04-27T14:41:52.000000Z","updated_at":"2025-04-27T14:41:52.000000Z","name":"sirine","email":"<EMAIL>"},{"id":5,"user_id":null,"company_name":"fff","business_type":"Retail","motivation":"dfssssssssssssssssss","website":"https:\/\/www.carreblanc.com\/","phone":"+***********","address":"fesddddddddddddddddddd","status":"pending","admin_notes":null,"processed_at":null,"processed_by":null,"created_at":"2025-04-27T14:31:32.000000Z","updated_at":"2025-04-27T14:31:32.000000Z","name":"sirine","email":"<EMAIL>"},{"id":4,"user_id":null,"company_name":"fff","business_type":"Retail","motivation":"ddddddddddddddddddddddddddddddddd","website":"https:\/\/www.carreblanc.com\/","phone":"+***********","address":"dddddddddddddddddddddddd","status":"pending","admin_notes":null,"processed_at":null,"processed_by":null,"created_at":"2025-04-27T14:28:12.000000Z","updated_at":"2025-04-27T14:28:12.000000Z","name":"sirine","email":"<EMAIL>"},{"id":3,"user_id":null,"company_name":"fff","business_type":"Retail","motivation":"azertyuiop","website":"https:\/\/www.carreblanc.com\/","phone":"+***********","address":"azertyuiop","status":"pending","admin_notes":null,"processed_at":null,"processed_by":null,"created_at":"2025-04-27T14:16:06.000000Z","updated_at":"2025-04-27T14:16:06.000000Z","name":"sirine","email":"<EMAIL>"},{"id":2,"user_id":null,"company_name":"fff","business_type":"Retail","motivation":"dddssdsdgjdsfgjdsfjd","website":"https:\/\/www.carreblanc.com\/","phone":"+***********","address":"dssdsds","status":"pending","admin_notes":null,"processed_at":null,"processed_by":null,"created_at":"2025-04-27T12:56:06.000000Z","updated_at":"2025-04-27T12:56:06.000000Z","name":"fff","email":"<EMAIL>"},{"id":1,"user_id":null,"company_name":"fff","business_type":"Retail","motivation":"dsss","website":"https:\/\/www.carreblanc.com\/","phone":"+***********","address":"ddddd","status":"pending","admin_notes":null,"processed_at":null,"processed_by":null,"created_at":"2025-04-27T12:49:53.000000Z","updated_at":"2025-04-27T12:49:53.000000Z","name":"fff","email":"<EMAIL>"}]}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/partner-requests
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Submit a new partner request'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      company_name:
        name: company_name
        description: 'Must not be greater than 255 characters.'
        required: true
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      business_type:
        name: business_type
        description: 'Must not be greater than 255 characters.'
        required: false
        example: 'n'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      motivation:
        name: motivation
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      website:
        name: website
        description: 'Must be a valid URL. Must not be greater than 255 characters.'
        required: false
        example: 'n'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      phone:
        name: phone
        description: 'Must not be greater than 20 characters.'
        required: true
        example: gzmiyvdljnikhway
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address:
        name: address
        description: 'Must not be greater than 255 characters.'
        required: true
        example: k
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      company_name: b
      business_type: 'n'
      motivation: architecto
      website: 'n'
      phone: gzmiyvdljnikhway
      address: k
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/partner-requests/status
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get the status of the latest partner request'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 422
        content: '{"status":"error","message":"Email is required to check status","errors":{"email":["The email field is required."]}}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/partner-requests/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified partner request'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the partner request.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 403
        content: '{"status":"error","message":"Vous n''\u00eates pas autoris\u00e9 \u00e0 voir cette demande"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/distributor-requests
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display distributor requests'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"status":"success","data":[{"id":1,"user_id":null,"company_name":"Test Company","business_type":"Retail","motivation":"Test motivation","website":"https:\/\/example.com","phone":"+*********0","address":"123 Test St","city":"Test City","postal_code":"12345","country":"Test Country","tax_id":"*********","registration_number":"*********","has_physical_store":true,"years_in_business":5,"product_categories_interested":"Electronics, Home Appliances","status":"pending","admin_notes":null,"processed_at":null,"processed_by":null,"created_at":"2025-04-27T15:49:11.000000Z","updated_at":"2025-04-27T15:49:11.000000Z","name":"Test User","email":"<EMAIL>"}]}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/distributor-requests
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Submit a new distributor request'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      company_name:
        name: company_name
        description: 'Must not be greater than 255 characters.'
        required: true
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      business_type:
        name: business_type
        description: 'Must not be greater than 255 characters.'
        required: false
        example: 'n'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      motivation:
        name: motivation
        description: ''
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      website:
        name: website
        description: 'Must be a valid URL. Must not be greater than 255 characters.'
        required: false
        example: 'n'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      phone:
        name: phone
        description: 'Must not be greater than 20 characters.'
        required: true
        example: gzmiyvdljnikhway
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address:
        name: address
        description: 'Must not be greater than 255 characters.'
        required: true
        example: k
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      city:
        name: city
        description: 'Must not be greater than 255 characters.'
        required: true
        example: c
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      postal_code:
        name: postal_code
        description: 'Must not be greater than 20 characters.'
        required: false
        example: myuwpwlvqwrsitcp
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      country:
        name: country
        description: 'Must not be greater than 255 characters.'
        required: true
        example: s
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      tax_id:
        name: tax_id
        description: 'Must not be greater than 50 characters.'
        required: false
        example: c
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      registration_number:
        name: registration_number
        description: 'Must not be greater than 50 characters.'
        required: false
        example: q
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      has_physical_store:
        name: has_physical_store
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      years_in_business:
        name: years_in_business
        description: 'Must be at least 0.'
        required: false
        example: 50
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      product_categories_interested:
        name: product_categories_interested
        description: ''
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      company_name: b
      business_type: 'n'
      motivation: architecto
      website: 'n'
      phone: gzmiyvdljnikhway
      address: k
      city: c
      postal_code: myuwpwlvqwrsitcp
      country: s
      tax_id: c
      registration_number: q
      has_physical_store: false
      years_in_business: 50
      product_categories_interested: architecto
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/distributor-requests/status
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get the status of the latest distributor request'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 422
        content: '{"status":"error","message":"Email is required to check status","errors":{"email":["The email field is required."]}}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/distributor-requests/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified distributor request'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the distributor request.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 403
        content: '{"status":"error","message":"Vous n''\u00eates pas autoris\u00e9 \u00e0 voir cette demande"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/admin/partner-requests
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display a listing of partner requests'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 403
        content: '{"status":"error","message":"Acc\u00e8s non autoris\u00e9"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/partner-requests/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified partner request'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the partner request.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 403
        content: '{"status":"error","message":"Acc\u00e8s non autoris\u00e9"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/admin/partner-requests/{id}/approve'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Approve a partner request'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the partner request.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      remise:
        name: remise
        description: 'Must be at least 0. Must not be greater than 100.'
        required: true
        example: 1
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      admin_notes:
        name: admin_notes
        description: ''
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      remise: 1
      admin_notes: architecto
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/admin/partner-requests/{id}/reject'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Reject a partner request'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the partner request.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      admin_notes:
        name: admin_notes
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      admin_notes: architecto
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/admin/distributor-requests
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display a listing of distributor requests'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 403
        content: '{"status":"error","message":"Acc\u00e8s non autoris\u00e9"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/distributor-requests/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified distributor request'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the distributor request.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 403
        content: '{"status":"error","message":"Acc\u00e8s non autoris\u00e9"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          vary: Origin
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/admin/distributor-requests/{id}/approve'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Approve a distributor request'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the distributor request.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      remise:
        name: remise
        description: 'Must be at least 0. Must not be greater than 100.'
        required: true
        example: 1
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      admin_notes:
        name: admin_notes
        description: ''
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      remise: 1
      admin_notes: architecto
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/admin/distributor-requests/{id}/reject'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Reject a distributor request'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the distributor request.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      admin_notes:
        name: admin_notes
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      admin_notes: architecto
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
