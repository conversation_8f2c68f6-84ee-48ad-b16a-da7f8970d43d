<?php

namespace App\Jobs;

use App\Models\Commande;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendOrderConfirmationEmail implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 120; // 2 minutes
    public $tries = 3;

    protected Commande $order;

    /**
     * Create a new job instance.
     */
    public function __construct(Commande $order)
    {
        $this->order = $order;
        $this->onQueue('emails');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('Sending order confirmation email', [
                'order_id' => $this->order->id,
                'customer_email' => $this->order->email_client
            ]);

            // Get customer information
            $customer = $this->getCustomerInfo();

            // Prepare email data
            $emailData = $this->prepareEmailData();

            // Send order confirmation email
            $this->sendConfirmationEmail($customer, $emailData);

            // Send invoice if available
            $this->sendInvoiceEmail($customer, $emailData);

            Log::info('Order confirmation email sent successfully', [
                'order_id' => $this->order->id,
                'email' => $customer['email']
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send order confirmation email', [
                'order_id' => $this->order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * Get customer information
     */
    private function getCustomerInfo(): array
    {
        // Try to get user if exists
        $user = null;
        if ($this->order->utilisateur_id) {
            $user = User::find($this->order->utilisateur_id);
        }

        return [
            'email' => $this->order->email_client ?? $user?->email,
            'name' => $this->order->nom_client ?? $user?->name,
            'first_name' => $this->order->prenom_client ?? $user?->first_name,
            'user' => $user
        ];
    }

    /**
     * Prepare email data
     */
    private function prepareEmailData(): array
    {
        // Load order relationships
        $this->order->load(['items.produit', 'adresse_livraison', 'adresse_facturation']);

        return [
            'order' => $this->order,
            'order_number' => $this->order->numero_commande,
            'order_date' => $this->order->created_at->format('d/m/Y'),
            'items' => $this->order->items,
            'shipping_address' => $this->order->adresse_livraison,
            'billing_address' => $this->order->adresse_facturation,
            'total_ht' => $this->order->total_ht,
            'total_ttc' => $this->order->total_ttc,
            'tva_amount' => $this->order->total_ttc - $this->order->total_ht,
            'payment_method' => $this->order->mode_paiement,
            'estimated_delivery' => $this->calculateEstimatedDelivery(),
            'tracking_info' => $this->getTrackingInfo(),
            'support_email' => config('mail.support_email', '<EMAIL>'),
            'support_phone' => config('app.support_phone', '+33 1 23 45 67 89')
        ];
    }

    /**
     * Send order confirmation email
     */
    private function sendConfirmationEmail(array $customer, array $emailData): void
    {
        if (!$customer['email']) {
            Log::warning('No email address for order confirmation', [
                'order_id' => $this->order->id
            ]);
            return;
        }

        // Check if mailable class exists, otherwise use simple email
        if (class_exists('App\Mail\OrderConfirmation')) {
            Mail::to($customer['email'])
                ->send(new \App\Mail\OrderConfirmation($emailData));
        } else {
            // Fallback to simple HTML email
            $this->sendSimpleConfirmationEmail($customer, $emailData);
        }
    }

    /**
     * Send simple HTML confirmation email
     */
    private function sendSimpleConfirmationEmail(array $customer, array $emailData): void
    {
        $subject = 'Confirmation de votre commande #' . $emailData['order_number'];

        $htmlContent = $this->buildEmailHtml($customer, $emailData);

        Mail::html($htmlContent, function ($message) use ($customer, $subject) {
            $message->to($customer['email'], $customer['name'])
                ->subject($subject)
                ->from(config('mail.from.address'), config('mail.from.name'));
        });
    }

    /**
     * Build HTML email content
     */
    private function buildEmailHtml(array $customer, array $emailData): string
    {
        $html = '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Confirmation de commande</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #f8f9fa; padding: 20px; text-align: center; }
                .order-info { background: #fff; border: 1px solid #ddd; padding: 20px; margin: 20px 0; }
                .items-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                .items-table th, .items-table td { border: 1px solid #ddd; padding: 10px; text-align: left; }
                .items-table th { background: #f8f9fa; }
                .total { font-weight: bold; font-size: 1.2em; }
                .footer { background: #f8f9fa; padding: 20px; text-align: center; font-size: 0.9em; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Merci pour votre commande !</h1>
                    <p>Bonjour ' . htmlspecialchars($customer['name']) . ',</p>
                    <p>Nous avons bien reçu votre commande et elle est en cours de traitement.</p>
                </div>

                <div class="order-info">
                    <h2>Détails de votre commande</h2>
                    <p><strong>Numéro de commande :</strong> ' . htmlspecialchars($emailData['order_number']) . '</p>
                    <p><strong>Date de commande :</strong> ' . htmlspecialchars($emailData['order_date']) . '</p>
                    <p><strong>Mode de paiement :</strong> ' . htmlspecialchars($emailData['payment_method']) . '</p>
                </div>

                <table class="items-table">
                    <thead>
                        <tr>
                            <th>Produit</th>
                            <th>Quantité</th>
                            <th>Prix unitaire</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>';

        foreach ($emailData['items'] as $item) {
            $html .= '
                        <tr>
                            <td>' . htmlspecialchars($item->produit->nom_produit ?? 'Produit') . '</td>
                            <td>' . $item->quantite . '</td>
                            <td>' . number_format($item->prix_unitaire, 2) . ' €</td>
                            <td>' . number_format($item->prix_total, 2) . ' €</td>
                        </tr>';
        }

        $html .= '
                    </tbody>
                    <tfoot>
                        <tr class="total">
                            <td colspan="3">Total TTC</td>
                            <td>' . number_format($emailData['total_ttc'], 2) . ' €</td>
                        </tr>
                    </tfoot>
                </table>

                <div class="order-info">
                    <h3>Livraison estimée</h3>
                    <p>' . htmlspecialchars($emailData['estimated_delivery']) . '</p>
                </div>

                <div class="footer">
                    <p>Si vous avez des questions, contactez-nous :</p>
                    <p>Email : ' . htmlspecialchars($emailData['support_email']) . '</p>
                    <p>Téléphone : ' . htmlspecialchars($emailData['support_phone']) . '</p>
                </div>
            </div>
        </body>
        </html>';

        return $html;
    }

    /**
     * Send invoice email if available
     */
    private function sendInvoiceEmail(array $customer, array $emailData): void
    {
        if (!$this->order->invoice_number || !$customer['email']) {
            return;
        }

        try {
            // Check if invoice PDF exists
            $invoicePath = storage_path('app/invoices/' . $this->order->invoice_number . '.pdf');

            if (file_exists($invoicePath)) {
                Mail::send('emails.invoice', $emailData, function ($message) use ($customer, $invoicePath) {
                    $message->to($customer['email'], $customer['name'])
                        ->subject('Facture - Commande #' . $this->order->numero_commande)
                        ->attach($invoicePath, [
                            'as' => 'facture-' . $this->order->numero_commande . '.pdf',
                            'mime' => 'application/pdf'
                        ]);
                });
            }

        } catch (\Exception $e) {
            Log::warning('Failed to send invoice email', [
                'order_id' => $this->order->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Calculate estimated delivery date
     */
    private function calculateEstimatedDelivery(): string
    {
        $deliveryDays = 3; // Default delivery time

        // Adjust based on shipping method if available
        if ($this->order->methode_livraison) {
            $deliveryDays = match ($this->order->methode_livraison) {
                'express' => 1,
                'standard' => 3,
                'economy' => 5,
                default => 3
            };
        }

        $estimatedDate = now()->addDays($deliveryDays);

        // Skip weekends
        while ($estimatedDate->isWeekend()) {
            $estimatedDate->addDay();
        }

        return $estimatedDate->format('d/m/Y');
    }

    /**
     * Get tracking information if available
     */
    private function getTrackingInfo(): ?array
    {
        if ($this->order->tracking_number) {
            return [
                'number' => $this->order->tracking_number,
                'url' => $this->order->tracking_url,
                'carrier' => $this->order->carrier
            ];
        }

        return null;
    }

    /**
     * Handle job failure
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('SendOrderConfirmationEmail job failed', [
            'order_id' => $this->order->id,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts()
        ]);

        // Mark order as having email issues
        $this->order->update([
            'email_sent' => false,
            'email_error' => $exception->getMessage()
        ]);
    }
}
