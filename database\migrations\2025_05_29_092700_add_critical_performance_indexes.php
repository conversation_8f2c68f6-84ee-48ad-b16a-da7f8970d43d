<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add critical indexes for performance optimization

        // Products table indexes (check if table and columns exist)
        if (Schema::hasTable('produits')) {
            if (Schema::hasColumn('produits', 'marque_id')) {
                DB::statement('CREATE INDEX IF NOT EXISTS idx_produits_marque_id ON produits(marque_id)');
            }
            if (Schema::hasColumn('produits', 'quantite_produit')) {
                DB::statement('CREATE INDEX IF NOT EXISTS idx_produits_stock ON produits(quantite_produit)');
            }
            if (Schema::hasColumn('produits', 'prix_produit')) {
                DB::statement('CREATE INDEX IF NOT EXISTS idx_produits_prix ON produits(prix_produit)');
            }
            if (Schema::hasColumn('produits', 'nom_produit')) {
                DB::statement('CREATE INDEX IF NOT EXISTS idx_produits_nom ON produits(nom_produit)');
            }
            if (Schema::hasColumn('produits', 'sous_sous_categorie_id')) {
                DB::statement('CREATE INDEX IF NOT EXISTS idx_produits_categorie ON produits(sous_sous_categorie_id)');
            }
        }

        // Images table indexes - check if table and columns exist
        if (Schema::hasTable('images')) {
            if (Schema::hasColumn('images', 'imageable_type') && Schema::hasColumn('images', 'imageable_id')) {
                DB::statement('CREATE INDEX IF NOT EXISTS idx_images_imageable ON images(imageable_type, imageable_id)');
            }
            if (Schema::hasColumn('images', 'is_primary')) {
                DB::statement('CREATE INDEX IF NOT EXISTS idx_images_primary ON images(is_primary)');
            }
        }

        // Promotions table indexes (check if table and columns exist)
        if (Schema::hasTable('promotions') && Schema::hasColumn('promotions', 'statut')) {
            DB::statement('CREATE INDEX IF NOT EXISTS idx_promotions_active ON promotions(statut, date_debut, date_fin)');
        }
        if (Schema::hasTable('promotions')) {
            DB::statement('CREATE INDEX IF NOT EXISTS idx_promotions_dates ON promotions(date_debut, date_fin)');
        }

        // Pivot table indexes (check if tables exist)
        if (Schema::hasTable('produit_promotion')) {
            DB::statement('CREATE INDEX IF NOT EXISTS idx_produit_promotion_produit ON produit_promotion(produit_id)');
            DB::statement('CREATE INDEX IF NOT EXISTS idx_produit_promotion_promotion ON produit_promotion(promotion_id)');
        }
        if (Schema::hasTable('collection_produit')) {
            DB::statement('CREATE INDEX IF NOT EXISTS idx_collection_produit_produit ON collection_produit(produit_id)');
            DB::statement('CREATE INDEX IF NOT EXISTS idx_collection_produit_collection ON collection_produit(collection_id)');
        }

        // Commandes table indexes (check if table and columns exist)
        if (Schema::hasTable('commandes') && Schema::hasColumn('commandes', 'client_id')) {
            DB::statement('CREATE INDEX IF NOT EXISTS idx_commandes_client_id ON commandes(client_id)');
        }
        if (Schema::hasTable('commandes') && Schema::hasColumn('commandes', 'status')) {
            DB::statement('CREATE INDEX IF NOT EXISTS idx_commandes_status ON commandes(status)');
        }
        if (Schema::hasTable('commandes')) {
            DB::statement('CREATE INDEX IF NOT EXISTS idx_commandes_date ON commandes(created_at)');
        }

        // Panier table indexes (check if table exists)
        if (Schema::hasTable('paniers') && Schema::hasColumn('paniers', 'client_id')) {
            DB::statement('CREATE INDEX IF NOT EXISTS idx_panier_client_id ON paniers(client_id)');
        }
        if (Schema::hasTable('panier_items')) {
            DB::statement('CREATE INDEX IF NOT EXISTS idx_panier_items_panier_id ON panier_items(panier_id)');
            DB::statement('CREATE INDEX IF NOT EXISTS idx_panier_items_produit_id ON panier_items(produit_id)');
        }

        // Users table indexes (check if table exists)
        if (Schema::hasTable('users')) {
            DB::statement('CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)');
            if (Schema::hasColumn('users', 'profil_remise')) {
                DB::statement('CREATE INDEX IF NOT EXISTS idx_users_profil_remise ON users(profil_remise)');
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the indexes
        DB::statement('DROP INDEX IF EXISTS idx_produits_marque_id');
        DB::statement('DROP INDEX IF EXISTS idx_produits_stock');
        DB::statement('DROP INDEX IF EXISTS idx_produits_categorie');
        DB::statement('DROP INDEX IF EXISTS idx_produits_prix');
        DB::statement('DROP INDEX IF EXISTS idx_produits_nom');
        DB::statement('DROP INDEX IF EXISTS idx_images_imageable');
        DB::statement('DROP INDEX IF EXISTS idx_images_primary');
        DB::statement('DROP INDEX IF EXISTS idx_promotions_active');
        DB::statement('DROP INDEX IF EXISTS idx_promotions_dates');
        DB::statement('DROP INDEX IF EXISTS idx_produit_promotion_produit');
        DB::statement('DROP INDEX IF EXISTS idx_produit_promotion_promotion');
        DB::statement('DROP INDEX IF EXISTS idx_collection_produit_produit');
        DB::statement('DROP INDEX IF EXISTS idx_collection_produit_collection');
        DB::statement('DROP INDEX IF EXISTS idx_commandes_client_id');
        DB::statement('DROP INDEX IF EXISTS idx_commandes_status');
        DB::statement('DROP INDEX IF EXISTS idx_commandes_date');
        DB::statement('DROP INDEX IF EXISTS idx_panier_client_id');
        DB::statement('DROP INDEX IF EXISTS idx_panier_items_panier_id');
        DB::statement('DROP INDEX IF EXISTS idx_panier_items_produit_id');
        DB::statement('DROP INDEX IF EXISTS idx_users_email');
        DB::statement('DROP INDEX IF EXISTS idx_users_profil_remise');
    }
};
