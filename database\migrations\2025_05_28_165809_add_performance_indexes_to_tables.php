<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add indexes to produits table for better query performance
        Schema::table('produits', function (Blueprint $table) {
            $table->index(['prix_produit'], 'idx_produits_price');
            $table->index(['marque_id', 'sous_sous_categorie_id'], 'idx_produits_marque_category');
            $table->index(['created_at'], 'idx_produits_created_at');
            $table->index(['quantite_produit'], 'idx_produits_stock');
            $table->index(['reference'], 'idx_produits_reference');
        });

        // Add indexes to promotions table
        Schema::table('promotions', function (Blueprint $table) {
            // Check if columns exist before creating indexes
            if (
                Schema::hasColumn('promotions', 'statut') &&
                Schema::hasColumn('promotions', 'date_debut') &&
                Schema::hasColumn('promotions', 'date_fin')
            ) {
                $table->index(['statut', 'date_debut', 'date_fin'], 'idx_promotions_active_dates');
            }
            if (Schema::hasColumn('promotions', 'type')) {
                $table->index(['type'], 'idx_promotions_type');
            }
            if (Schema::hasColumn('promotions', 'code')) {
                $table->index(['code'], 'idx_promotions_code');
            }
            if (Schema::hasColumn('promotions', 'featured')) {
                $table->index(['featured'], 'idx_promotions_featured');
            }
        });

        // Add indexes to produit_promotion pivot table
        if (Schema::hasTable('produit_promotion')) {
            Schema::table('produit_promotion', function (Blueprint $table) {
                $table->index(['date_debut', 'date_fin'], 'idx_produit_promotion_dates');
            });
        }

        // Add indexes to images table for polymorphic relations
        Schema::table('images', function (Blueprint $table) {
            $table->index(['imageable_type', 'imageable_id', 'order'], 'idx_images_polymorphic_order');
        });

        // Add indexes to produit_caracteristiques table
        if (Schema::hasTable('produit_caracteristiques')) {
            Schema::table('produit_caracteristiques', function (Blueprint $table) {
                $table->index(['produit_id', 'attribut_id'], 'idx_produit_caracteristiques_lookup');
            });
        }

        // Add indexes to users table
        Schema::table('users', function (Blueprint $table) {
            $table->index(['email_verified_at'], 'idx_users_verified');
            $table->index(['created_at'], 'idx_users_created_at');
        });

        // Add indexes to orders table if exists and has expected columns
        if (Schema::hasTable('commandes')) {
            Schema::table('commandes', function (Blueprint $table) {
                // Check if columns exist before creating indexes
                if (Schema::hasColumn('commandes', 'status')) {
                    $table->index(['status'], 'idx_commandes_status');
                }
                if (Schema::hasColumn('commandes', 'utilisateur_id')) {
                    $table->index(['utilisateur_id'], 'idx_commandes_user');
                }
                $table->index(['created_at'], 'idx_commandes_created_at');
            });
        }

        // Add indexes to cart table if exists and has expected columns
        if (Schema::hasTable('paniers')) {
            Schema::table('paniers', function (Blueprint $table) {
                if (Schema::hasColumn('paniers', 'guest_id')) {
                    $table->index(['guest_id'], 'idx_paniers_guest_id');
                }
                if (Schema::hasColumn('paniers', 'utilisateur_id')) {
                    $table->index(['utilisateur_id'], 'idx_paniers_user_id');
                }
                $table->index(['created_at'], 'idx_paniers_created_at');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop indexes from produits table
        Schema::table('produits', function (Blueprint $table) {
            $table->dropIndex('idx_produits_price');
            $table->dropIndex('idx_produits_marque_category');
            $table->dropIndex('idx_produits_created_at');
            $table->dropIndex('idx_produits_stock');
            $table->dropIndex('idx_produits_reference');
        });

        // Drop indexes from promotions table
        Schema::table('promotions', function (Blueprint $table) {
            try {
                $table->dropIndex('idx_promotions_active_dates');
            } catch (\Exception $e) {
                // Index may not exist
            }
            try {
                $table->dropIndex('idx_promotions_type');
            } catch (\Exception $e) {
                // Index may not exist
            }
            try {
                $table->dropIndex('idx_promotions_code');
            } catch (\Exception $e) {
                // Index may not exist
            }
            try {
                $table->dropIndex('idx_promotions_featured');
            } catch (\Exception $e) {
                // Index may not exist
            }
        });

        // Drop indexes from produit_promotion pivot table
        if (Schema::hasTable('produit_promotion')) {
            Schema::table('produit_promotion', function (Blueprint $table) {
                $table->dropIndex('idx_produit_promotion_dates');
            });
        }

        // Drop indexes from images table
        Schema::table('images', function (Blueprint $table) {
            $table->dropIndex('idx_images_polymorphic_order');
        });

        // Drop indexes from produit_caracteristiques table
        if (Schema::hasTable('produit_caracteristiques')) {
            Schema::table('produit_caracteristiques', function (Blueprint $table) {
                $table->dropIndex('idx_produit_caracteristiques_lookup');
            });
        }

        // Drop indexes from users table
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex('idx_users_verified');
            $table->dropIndex('idx_users_created_at');
        });

        // Drop indexes from orders table if exists
        if (Schema::hasTable('commandes')) {
            Schema::table('commandes', function (Blueprint $table) {
                try {
                    $table->dropIndex('idx_commandes_status');
                } catch (\Exception $e) {
                    // Index may not exist
                }
                try {
                    $table->dropIndex('idx_commandes_user');
                } catch (\Exception $e) {
                    // Index may not exist
                }
                try {
                    $table->dropIndex('idx_commandes_created_at');
                } catch (\Exception $e) {
                    // Index may not exist
                }
            });
        }

        // Drop indexes from cart table if exists
        if (Schema::hasTable('paniers')) {
            Schema::table('paniers', function (Blueprint $table) {
                try {
                    $table->dropIndex('idx_paniers_guest_id');
                } catch (\Exception $e) {
                    // Index may not exist
                }
                try {
                    $table->dropIndex('idx_paniers_user_id');
                } catch (\Exception $e) {
                    // Index may not exist
                }
                try {
                    $table->dropIndex('idx_paniers_created_at');
                } catch (\Exception $e) {
                    // Index may not exist
                }
            });
        }
    }
};
