<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('produit_variantes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('produit_parent_id')->constrained('produits')->onDelete('cascade');
            $table->string('sku')->unique();
            $table->decimal('prix_supplement', 10, 2)->default(0);
            $table->integer('stock')->default(0);
            $table->boolean('actif')->default(true);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('produit_variantes');
    }
};
