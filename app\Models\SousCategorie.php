<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class SousCategorie extends Model
{
    use HasFactory;
    protected $fillable = [
        'nom_sous_categorie',
        'description_sous_categorie',
        'categorie_id'
    ];

    public function categorie()
    {
        return $this->belongsTo(Categorie::class);
    }

    public function sousSousCategories()
    {
        return $this->hasMany(sous_sousCategorie::class);
    }

    /**
     * Les attributs associés à cette sous-catégorie
     */
    public function attributs()
    {
        return $this->belongsToMany(Attribut::class, 'attribut_categorie', 'sous_categorie_id', 'attribut_id')
            ->withPivot('obligatoire')
            ->withTimestamps();
    }

    /**
     * Get all images for this subcategory
     */
    public function images(): MorphMany
    {
        return $this->morphMany(Image::class, 'imageable')->orderBy('order');
    }

    /**
     * Get the primary image for this subcategory
     */
    public function getPrimaryImageAttribute()
    {
        return $this->images()->where('is_primary', true)->first() ?? $this->images()->first();
    }

    /**
     * Get the primary image URL for this subcategory
     */
    public function getPrimaryImageUrlAttribute()
    {
        if ($this->primaryImage) {
            return $this->primaryImage->url;
        }

        return null;
    }
}
