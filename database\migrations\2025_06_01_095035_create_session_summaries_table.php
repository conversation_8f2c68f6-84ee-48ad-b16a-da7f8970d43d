<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('session_summaries', function (Blueprint $table) {
            $table->id();
            $table->float('session_duration')->comment('Durée de la session en minutes');
            $table->integer('total_predictions')->comment('Nombre total de prédictions');
            $table->integer('satisfied_count')->comment('Nombre de prédictions satisfaites');
            $table->integer('neutral_count')->comment('Nombre de prédictions neutres');
            $table->integer('unsatisfied_count')->comment('Nombre de prédictions non satisfaites');
            $table->float('average_confidence')->comment('Confiance moyenne des prédictions');
            $table->string('most_common_prediction')->comment('Prédiction la plus commune');
            $table->string('session_id')->nullable()->comment('ID de session optionnel');
            $table->string('user_agent')->nullable()->comment('User agent du navigateur');
            $table->ipAddress('ip_address')->nullable()->comment('Adresse IP de l\'utilisateur');
            $table->timestamps();

            // Index pour les performances
            $table->index(['created_at']);
            $table->index(['session_id']);
            $table->index(['most_common_prediction']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('session_summaries');
    }
};
