<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ListeSouhaitItem extends Model
{
    use HasFactory;

    protected $table = 'liste_souhait_items';

    protected $fillable = [
        'liste_souhait_id',
        'produit_id',
        'variante_id',
        'note',
        'prix_reference'
    ];

    protected $casts = [
        'prix_reference' => 'decimal:2',
    ];

    /**
     * Relation avec la liste de souhaits
     */
    public function listeSouhait()
    {
        return $this->belongsTo(ListeSouhait::class);
    }

    /**
     * Relation avec le produit
     */
    public function produit()
    {
        return $this->belongsTo(Produit::class);
    }

    /**
     * Relation avec la variante
     */
    public function variante()
    {
        return $this->belongsTo(ProduitVariante::class);
    }

    /**
     * Obtenir le prix actuel du produit
     */
    public function getPrixActuel()
    {
        $produit = $this->produit;

        if (!$produit) {
            return null;
        }

        $prix = $produit->prix_produit;

        if ($this->variante_id) {
            $variante = $this->variante;
            if ($variante) {
                $prix += $variante->prix_supplement;
            }
        }

        return $prix;
    }

    /**
     * Vérifier si le prix a changé depuis l'ajout à la liste
     */
    public function prixAChange()
    {
        $prixActuel = $this->getPrixActuel();

        if ($prixActuel === null || $this->prix_reference === null) {
            return false;
        }

        return abs($prixActuel - $this->prix_reference) > 0.01;
    }

    /**
     * Obtenir la différence de prix (positive si le prix a augmenté, négative s'il a baissé)
     */
    public function getDifferencePrix()
    {
        $prixActuel = $this->getPrixActuel();

        if ($prixActuel === null || $this->prix_reference === null) {
            return 0;
        }

        return $prixActuel - $this->prix_reference;
    }
}
