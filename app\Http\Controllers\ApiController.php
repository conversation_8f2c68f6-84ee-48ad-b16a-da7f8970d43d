<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\KeycloakService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Validation\ValidatesRequests;

abstract class ApiController extends Controller
{
    use AuthorizesRequests, ValidatesRequests;

    protected KeycloakService $keycloakService;

    public function __construct(KeycloakService $keycloakService)
    {
        $this->keycloakService = $keycloakService;
    }

    /**
     * Get the authenticated user from the request
     *
     * @param Request $request
     * @return mixed
     */
    protected function getUser(Request $request): mixed
    {
        return auth()->user();
    }

    /**
     * Get the user roles from the token
     *
     * @param Request $request
     * @return array
     */
    protected function getUserRoles(Request $request): array
    {
        $tokenPayload = $request->attributes->get('token_payload');
        return $tokenPayload->realm_access->roles ?? [];
    }

    /**
     * Check if the authenticated user has a specific role
     *
     * @param Request $request
     * @param string $role
     * @return bool
     */
    protected function userHasRole(Request $request, string $role): bool
    {
        $roles = $this->getUserRoles($request);
        return in_array($role, $roles);
    }

    /**
     * Check if user has any of the given roles
     *
     * @param Request $request
     * @param array $roles
     * @return bool
     */
    protected function userHasAnyRole(Request $request, array $roles): bool
    {
        $userRoles = $this->getUserRoles($request);
        return !empty(array_intersect($roles, $userRoles));
    }
}
