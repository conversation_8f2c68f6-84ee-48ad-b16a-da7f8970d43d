<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;

class NewsletterController extends Controller
{
    // Subscribe endpoint
    public function subscribe(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
        ]);
        $user = User::where('email', $request->input('email'))->first();
        if (!$user) {
            return response()->json(['error' => 'User not found'], 404);
        }
        $user->newsletter_subscribed = true;
        $user->save();
        return response()->json(['message' => 'Subscribed to newsletter successfully']);
    }

    // Unsubscribe endpoint
    public function unsubscribe(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
        ]);
        $user = User::where('email', $request->input('email'))->first();
        if (!$user) {
            return response()->json(['error' => 'User not found'], 404);
        }
        $user->newsletter_subscribed = false;
        $user->save();
        return response()->json(['message' => 'Unsubscribed from newsletter successfully']);
    }
}
