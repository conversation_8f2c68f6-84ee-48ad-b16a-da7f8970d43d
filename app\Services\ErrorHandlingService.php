<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use Throwable;

class ErrorHandlingService
{
    /**
     * Log and format application errors
     */
    public static function logAndFormatError(Throwable $exception, Request $request = null): array
    {
        $errorId = uniqid('error_');

        $context = [
            'error_id' => $errorId,
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
        ];

        if ($request) {
            $context['request'] = [
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'user_id' => auth()->id(),
                'headers' => $request->headers->all(),
                'input' => $request->except(['password', 'password_confirmation', 'token']),
            ];
        }

        // Log the error
        Log::error('Application error occurred', $context);

        // Return formatted error for API response
        return [
            'error_id' => $errorId,
            'message' => config('app.debug') ? $exception->getMessage() : 'Une erreur interne s\'est produite.',
            'debug' => config('app.debug') ? [
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTrace(),
            ] : null,
        ];
    }

    /**
     * Log slow database queries
     */
    public static function logSlowQuery(string $sql, array $bindings, float $time): void
    {
        Log::warning('Slow database query detected', [
            'sql' => $sql,
            'bindings' => $bindings,
            'time_ms' => $time,
            'backtrace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 10),
        ]);
    }

    /**
     * Log security events
     */
    public static function logSecurityEvent(string $event, array $context = []): void
    {
        Log::warning('Security event', array_merge([
            'event' => $event,
            'timestamp' => now()->toISOString(),
            'ip' => request()?->ip(),
            'user_agent' => request()?->userAgent(),
            'user_id' => auth()?->id(),
        ], $context));
    }

    /**
     * Log performance issues
     */
    public static function logPerformanceIssue(string $type, array $metrics, array $context = []): void
    {
        Log::warning('Performance issue detected', array_merge([
            'type' => $type,
            'metrics' => $metrics,
            'timestamp' => now()->toISOString(),
        ], $context));
    }

    /**
     * Log business logic errors
     */
    public static function logBusinessError(string $operation, string $error, array $context = []): void
    {
        Log::error('Business logic error', array_merge([
            'operation' => $operation,
            'error' => $error,
            'timestamp' => now()->toISOString(),
            'user_id' => auth()?->id(),
        ], $context));
    }

    /**
     * Log suspicious activity
     */
    public static function logSuspiciousActivity(string $activity, array $context = []): void
    {
        Log::alert('Suspicious activity detected', array_merge([
            'activity' => $activity,
            'timestamp' => now()->toISOString(),
            'ip' => request()?->ip(),
            'user_agent' => request()?->userAgent(),
            'user_id' => auth()?->id(),
        ], $context));
    }

    /**
     * Log successful operations for audit trail
     */
    public static function logAuditEvent(string $action, string $resource, array $context = []): void
    {
        Log::info('Audit event', array_merge([
            'action' => $action,
            'resource' => $resource,
            'timestamp' => now()->toISOString(),
            'user_id' => auth()?->id(),
            'ip' => request()?->ip(),
        ], $context));
    }
}
