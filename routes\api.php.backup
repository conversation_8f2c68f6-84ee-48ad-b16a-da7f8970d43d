<?php
use App\Http\Controllers\Auth\KeycloakVerificationController;
use App\Http\Controllers\PaiementController;
use App\Http\Controllers\MarqueController;
use App\Http\Controllers\ProduitController;
use App\Http\Controllers\sous_sousCategorieController;
use App\Http\Controllers\CategorieController;
use App\Http\Controllers\SousCategorieController;
use App\Http\Controllers\CommandeController;
use App\Http\Controllers\ClientController;
use App\Http\Controllers\CollectionController;
use App\Http\Controllers\GroupeClientController;
use App\Http\Controllers\ImageController;
use App\Http\Controllers\ImageProxyController;
use App\Http\Controllers\PartenaireController;
use App\Http\Controllers\PointDeVenteController;
use App\Http\Controllers\PromotionController;
use App\Http\Controllers\RegleRemiseController;
use App\Http\Controllers\GroupeAttributController;
use App\Http\Controllers\AttributController;
use App\Http\Controllers\ProduitVarianteController;
use App\Http\Controllers\PanierController;
use App\Http\Controllers\CarouselController;
use App\Http\Controllers\CarouselSlideController;
use App\Http\Controllers\PromotionEventController;
use App\Http\Controllers\StockController;
use App\Http\Controllers\ListeSouhaitController;
use App\Http\Controllers\NewsletterController;
use App\Http\Controllers\SystemMonitoringController;
use App\Http\Controllers\StripeWebhookController;
use Illuminate\Support\Facades\Route;

Route::prefix('auth')->group(
    function () {
        Route::post('verify', [KeycloakVerificationController::class, 'verify']);
        Route::post('logout', [KeycloakVerificationController::class, 'logout'])->name('logout');
        Route::post('refresh', [KeycloakVerificationController::class, 'refresh']);
        Route::get('user', [KeycloakVerificationController::class, 'user'])
            ->middleware(['protected']); // Add specific role requirement
    }
);

// Public API routes with lighter rate limiting
Route::middleware(['enhanced.rate.limit:public'])->group(function () {
    Route::get('marques', [MarqueController::class, "index"]);
    Route::get('marques/{id}', [MarqueController::class, "show"]);
    Route::get('marques/{id}/produits', [MarqueController::class, "getProduits"]);
    
    Route::get('produits', [ProduitController::class, "index"]);
    Route::get('produits/{id}', [ProduitController::class, "show"]);
    Route::get('produits/perpages', [ProduitController::class, "produitsPaginate"]);
    
    // Nouveau système d'attributs - Routes pour le frontoffice
    Route::get('produits/{id}/attributs', [ProduitController::class, 'getAttributs']);
    Route::get('produits/{id}/variantes', [ProduitVarianteController::class, 'index']);
    Route::get('attributs/filtrables', [AttributController::class, 'getFiltrableAttributes']);
});

// Admin routes for brands and products (stricter rate limiting)
Route::middleware(['enhanced.rate.limit:admin'])->group(function () {
    Route::post('marques', [MarqueController::class, "store"]);
    Route::put('marques/{id}', [MarqueController::class, "update"]);
    Route::delete('marques/{id}', [MarqueController::class, "destroy"]);
    
    Route::post('produits', [ProduitController::class, "store"]);
    Route::put('produits/{id}', [ProduitController::class, "update"]);
    Route::delete('produits/{id}', [ProduitController::class, "destroy"]);
    
    // Nouveau système d'attributs - Routes pour le backoffice
    Route::post('produits/{id}/attributs', [ProduitController::class, 'setAttributs']);
    Route::put('produits/{id}/attributs/{attributId}', [ProduitController::class, 'updateAttribut']);
    Route::delete('produits/{id}/attributs/{attributId}', [ProduitController::class, 'removeAttribut']);
});

// Public routes for categories with lighter rate limiting
Route::middleware(['enhanced.rate.limit:public'])->group(function () {
    Route::get("sous_sousCategories", [sous_sousCategorieController::class, "index"]);
    Route::get("sous_sousCategories/{id}", [sous_sousCategorieController::class, "show"]);
    Route::get("sous_sousCategories/{id}/attributs", [sous_sousCategorieController::class, "getAttributs"]);
    
    Route::get("categories", [CategorieController::class, "index"]);
    Route::get("categories/featured", [CategorieController::class, "getFeaturedCategories"]);
    Route::get("categories/{id}", [CategorieController::class, "show"]);
    Route::get("categories/{id}/sousCategories", [SousCategorieController::class, "getByCategorieId"]);
    
    Route::get("sousCategories", [SousCategorieController::class, "index"]);
    Route::get("sousCategories/{id}", [SousCategorieController::class, "show"]);
});

// Admin routes for categories with stricter rate limiting
Route::middleware(['enhanced.rate.limit:admin'])->group(function () {
    Route::post("sous_sousCategories", [sous_sousCategorieController::class, "store"]);
    Route::put("sous_sousCategories/{id}", [sous_sousCategorieController::class, "update"]);
    Route::delete("sous_sousCategories/{id}", [sous_sousCategorieController::class, "destroy"]);
    
    Route::post("categories", [CategorieController::class, "store"]);
    Route::post("categories/featured/reorder", [CategorieController::class, "reorderFeatured"]);
    Route::put("categories/{id}", [CategorieController::class, "update"]);
    Route::put("categories/{id}/featured", [CategorieController::class, "setFeatured"]);
    Route::delete("categories/{id}", [CategorieController::class, "destroy"]);
    
    Route::post("sousCategories", [SousCategorieController::class, "store"]);
    Route::put("sousCategories/{id}", [SousCategorieController::class, "update"]);
    Route::delete("sousCategories/{id}", [SousCategorieController::class, "destroy"]);
});

// Routes pour les commandes avec rate limiting pour checkout
Route::prefix('commandes')->middleware(['enhanced.rate.limit:checkout'])->group(function () {
    Route::get('/', [CommandeController::class, 'index'])->name('commandes.index'); // Existing: Get all orders
    Route::post('/', [CommandeController::class, 'store'])->name('commandes.store'); // Updated: Create order from cart
    Route::get('/{commande}', [CommandeController::class, 'show'])->name('commandes.show'); // Existing: Get a specific order
    Route::delete('/{commande}', [CommandeController::class, 'destroy'])->name('commandes.destroy'); // Existing: Delete an order (soft delete likely)

    Route::patch('/{commande}/status', [CommandeController::class, 'updateStatus'])->name('commandes.updateStatus'); // Existing: Update order status
    Route::post('/{commande}/pay', [CommandeController::class, 'processPayment'])->name('commandes.processPayment'); // New: Process payment for an order
    Route::post('/{commande}/cancel', [CommandeController::class, 'cancelOrder'])->name('commandes.cancelOrder'); // New: Cancel an order
});
// Public routes for clients (read operations)
Route::middleware(['enhanced.rate.limit:public'])->group(function () {
    Route::get("clients", [ClientController::class, "index"]);
    Route::get("clients/{id}", [ClientController::class, "show"]);
    Route::get("clients/{id}/derniere-commande", [ClientController::class, "getLatestOrder"]);
    Route::get("clients/{id}/commandes", [ClientController::class, "getOrders"]);
});

// Admin routes for clients (write operations)
Route::middleware(['enhanced.rate.limit:admin'])->group(function () {
    Route::put("clients/{id}/remise", [ClientController::class, "updateDiscount"]);
    Route::put("clients/{id}/type", [ClientController::class, "updateType"]);
    Route::put("clients/{id}/profil-remise", [ClientController::class, "updateType"]);
});

// Public routes for partners and points of sale (read operations)
Route::middleware(['enhanced.rate.limit:public'])->group(function () {
    Route::get("partenaires", [PartenaireController::class, "index"]);
    Route::get("partenaires/{id}", [PartenaireController::class, "show"]);
    
    Route::get("points-de-vente", [PointDeVenteController::class, "index"]);
    Route::get("points-de-vente/{id}", [PointDeVenteController::class, "show"]);
});

// Admin routes for partners and points of sale (write operations)
Route::middleware(['enhanced.rate.limit:admin'])->group(function () {
    Route::post("partenaires", [PartenaireController::class, "store"]);
    Route::put("partenaires/{id}", [PartenaireController::class, "update"]);
    Route::delete("partenaires/{id}", [PartenaireController::class, "destroy"]);
    
    Route::post("points-de-vente", [PointDeVenteController::class, "store"]);
    Route::put("points-de-vente/{id}", [PointDeVenteController::class, "update"]);
    Route::delete("points-de-vente/{id}", [PointDeVenteController::class, "destroy"]);
    Route::post("points-de-vente/{id}/clients", [PointDeVenteController::class, "addUser"]);
    Route::delete("points-de-vente/{id}/clients/{userId}", [PointDeVenteController::class, "removeUser"]);
});

// Public routes for client groups and collections (read operations)
Route::middleware(['enhanced.rate.limit:public'])->group(function () {
    Route::get("groupes-clients", [GroupeClientController::class, "index"]);
    Route::get("groupes-clients/{id}", [GroupeClientController::class, "show"]);
    
    Route::get("collections", [CollectionController::class, "index"]);
    Route::get("collections/{id}", [CollectionController::class, "show"]);
    Route::get("collections/{id}/produits", [CollectionController::class, "getProduits"]);
    Route::get("collections/{id}/produits-featured", [CollectionController::class, "getProduitsFeatured"]);
});

// Admin routes for client groups and collections (write operations)
Route::middleware(['enhanced.rate.limit:admin'])->group(function () {
    Route::post("groupes-clients", [GroupeClientController::class, "store"]);
    Route::put("groupes-clients/{id}", [GroupeClientController::class, "update"]);
    Route::delete("groupes-clients/{id}", [GroupeClientController::class, "destroy"]);
    Route::post("groupes-clients/{id}/clients", [GroupeClientController::class, "addUser"]);
    Route::delete("groupes-clients/{id}/clients/{userId}", [GroupeClientController::class, "removeUser"]);
    
    Route::post("collections", [CollectionController::class, "store"]);
    Route::put("collections/{id}", [CollectionController::class, "update"]);
    Route::delete("collections/{id}", [CollectionController::class, "destroy"]);
    Route::post("collections/{id}/produits", [CollectionController::class, "addProduits"]);
    Route::delete("collections/{id}/produits/{produitId}", [CollectionController::class, "removeProduit"]);
});

// Public routes for payments (read operations)  
Route::middleware(['enhanced.rate.limit:public'])->group(function () {
    Route::get("paiements", [PaiementController::class, "index"]);
    Route::get("paiements/{id}", [PaiementController::class, "show"]);
});

// Admin routes for payments (write operations)
Route::middleware(['enhanced.rate.limit:admin'])->group(function () {
    Route::post("paiements", [PaiementController::class, "store"]);
    Route::put("paiements/{id}", [PaiementController::class, "update"]);
    Route::delete("paiements/{id}", [PaiementController::class, "destroy"]);
});



// Protected routes (require authentication and specific roles)
Route::prefix('v1')->middleware(['protected', 'role:admin'])->group(function () {
    // Admin-only routes
    // Route::apiResource('users', UserController::class);

    // Route admin pour récupérer la dernière commande d'un client
    Route::get('admin/clients/{id}/derniere-commande', [ClientController::class, "getLatestOrder"]);
});

// Public routes for promotions (read operations)
Route::middleware(['enhanced.rate.limit:public'])->group(function () {
    Route::prefix('promotions')->group(function () {
        Route::get('/', [PromotionController::class, 'index']);
        Route::get('/featured', [PromotionController::class, 'getFeatured']);
        Route::get('/{id}', [PromotionController::class, 'show']);
        Route::get('/{id}/products', [PromotionController::class, 'getProducts']);
        Route::get('/{id}/related', [PromotionController::class, 'getRelated']);
    });
    
    Route::prefix('promotion-events')->group(function () {
        Route::get('/', [PromotionEventController::class, 'index']);
        Route::get('/{id}', [PromotionEventController::class, 'show']);
        Route::get('/{id}/promotions', [PromotionEventController::class, 'getPromotions']);
    });
});

// Admin routes for promotions (write operations)
Route::middleware(['enhanced.rate.limit:admin'])->group(function () {
    Route::prefix('promotions')->group(function () {
        Route::post('/', [PromotionController::class, 'store']);
        Route::put('/{id}', [PromotionController::class, 'update']);
        Route::delete('/{id}', [PromotionController::class, 'destroy']);
    });
    
    Route::prefix('promotion-events')->group(function () {
        Route::post('/', [PromotionEventController::class, 'store']);
        Route::put('/{id}', [PromotionEventController::class, 'update']);
        Route::delete('/{id}', [PromotionEventController::class, 'destroy']);
    });
    
    // Routes pour associer des promotions aux produits et collections
    Route::post('produits/{produit}/promotions', [ProduitController::class, 'attachPromotion']);
    Route::delete('produits/{produit}/promotions/{promotion}', [ProduitController::class, 'detachPromotion']);
    Route::post('collections/{collection}/promotions', [CollectionController::class, 'attachPromotion']);
    Route::delete('collections/{collection}/promotions/{promotion}', [CollectionController::class, 'detachPromotion']);
    
    // Routes admin pour les promotions et remises
    Route::prefix('admin')->group(function () {
        Route::apiResource('promotions', PromotionController::class);
        Route::apiResource('regle-remises', RegleRemiseController::class);
    });
    
    // Routes pour le nouveau système d'attributs - Administration
    Route::prefix('admin')->group(function () {
        // Gestion des groupes d'attributs
        Route::apiResource('groupes-attributs', GroupeAttributController::class);

        // Gestion des attributs
        Route::apiResource('attributs', AttributController::class);
        Route::post('attributs/{id}/sous-categories/{sousCategorieId}', [AttributController::class, 'attachToSousCategorie']);
        Route::delete('attributs/{id}/sous-categories/{sousCategorieId}', [AttributController::class, 'detachFromSousCategorie']);
    });
    
    // Gestion des variantes de produits
    Route::post('produits/{produit_id}/variantes', [ProduitVarianteController::class, 'store']);
    Route::apiResource('variantes', ProduitVarianteController::class)->except(['index', 'store']);
    Route::patch('variantes/{id}/stock', [ProduitVarianteController::class, 'updateStock']);
});

// Cart routes with enhanced rate limiting for cart operations
Route::middleware(['enhanced.rate.limit:checkout', \App\Http\Middleware\CookieCartMiddleware::class])->group(function () {
    // Routes pour le panier (ancienne version)
    Route::get('panier', [PanierController::class, 'index']);
    Route::post('panier/ajouter', [PanierController::class, 'addItem']);
    Route::put('panier/items/{itemId}', [PanierController::class, 'updateItem']);
    Route::delete('panier/items/{itemId}', [PanierController::class, 'removeItem']);
    Route::delete('panier/vider', [PanierController::class, 'clear']);

    // Routes pour le panier (nouvelle version conforme aux standards)
    Route::get('cart', [PanierController::class, 'index']);
    Route::post('cart/items', [PanierController::class, 'addItem']);
    Route::put('cart/items/{itemId}', [PanierController::class, 'updateItem']);
    Route::delete('cart/items/{itemId}', [PanierController::class, 'removeItem']);
    Route::delete('cart', [PanierController::class, 'clear']);
    Route::post('cart/merge', [PanierController::class, 'merge']);
});

// Routes pour la gestion des images avec rate limiting pour uploads
Route::prefix('images')->group(function () {
    // Upload routes with enhanced rate limiting
    Route::middleware(['enhanced.rate.limit:upload'])->group(function () {
        Route::post('upload', [ImageController::class, 'upload']);
        Route::post('upload-multiple', [ImageController::class, 'uploadMultiple']);
    });

    // Other image routes with standard rate limiting
    Route::get('get', [ImageController::class, 'getImages']);
    Route::put('{id}', [ImageController::class, 'update']);
    Route::patch('{id}', [ImageController::class, 'update']);
    Route::delete('{id}', [ImageController::class, 'destroy']);
    Route::post('reorder', [ImageController::class, 'reorder']);

    // Routes pour le proxy d'images
    Route::get('serve/{id}', [ImageProxyController::class, 'serve']);
    Route::get('thumbnail/{id}/{size}', [ImageProxyController::class, 'thumbnail']);
    Route::get('file/{path}', [ImageProxyController::class, 'serveByPath'])->where('path', '.*');
});

// Public routes for carousels and wishlist (read operations)
Route::middleware(['enhanced.rate.limit:public'])->group(function () {
    Route::prefix('carousels')->group(function () {
        Route::get('/', [CarouselController::class, 'index']);
        Route::get('/actifs', [CarouselController::class, 'getActiveCarousels']);
        Route::get('/slides', [CarouselSlideController::class, 'index']);
        Route::get('/slides/{id}', [CarouselSlideController::class, 'show']);
        Route::get('/{id}/slides', [CarouselController::class, 'getSlides']);
        Route::get('/{id}', [CarouselController::class, 'show']);
    });
    
    Route::prefix('wishlist')->group(function () {
        Route::get('/', [ListeSouhaitController::class, 'index']);
        Route::get('/check/{produitId}', [ListeSouhaitController::class, 'checkProduct']);
    });
    
    // Public stock routes (read-only)
    Route::prefix('stock')->group(function () {
        Route::get('produits/{produitId}/historique', [StockController::class, 'getHistorique']);
        Route::get('ruptures', [StockController::class, 'getProduitsEnRupture']);
        Route::get('limites', [StockController::class, 'getProduitsStockLimite']);
    });
});

// Admin routes for carousels and stock management (write operations)
Route::middleware(['enhanced.rate.limit:admin'])->group(function () {
    Route::prefix('carousels')->group(function () {
        Route::post('/', [CarouselController::class, 'store']);
        Route::post('/slides', [CarouselSlideController::class, 'store']);
        Route::post('/slides/reorder', [CarouselSlideController::class, 'reorder']);
        Route::put('/slides/{id}', [CarouselSlideController::class, 'update']);
        Route::delete('/slides/{id}', [CarouselSlideController::class, 'destroy']);
        Route::put('/{id}', [CarouselController::class, 'update']);
        Route::delete('/{id}', [CarouselController::class, 'destroy']);
    });
    
    // Admin stock routes (write operations)
    Route::prefix('stock')->group(function () {
        Route::post('produits/{produitId}/ajouter', [StockController::class, 'ajouterStock']);
        Route::post('produits/{produitId}/retirer', [StockController::class, 'retirerStock']);
        Route::post('produits/{produitId}/ajuster', [StockController::class, 'ajusterStock']);
    });
});

// User-specific routes for wishlist (authenticated operations)
Route::middleware(['enhanced.rate.limit:checkout'])->group(function () {
    Route::prefix('wishlist')->group(function () {
        Route::post('/items', [ListeSouhaitController::class, 'addItem']);
        Route::delete('/items/{id}', [ListeSouhaitController::class, 'removeItem']);
        Route::post('/items/{id}/move-to-cart', [ListeSouhaitController::class, 'moveToCart']);
    });
});

// Routes pour les demandes de partenariat
use App\Http\Controllers\PartnerRequestController;
use App\Http\Controllers\DistributorRequestController;
use App\Http\Controllers\Admin\PartnerRequestController as AdminPartnerRequestController;
use App\Http\Controllers\Admin\DistributorRequestController as AdminDistributorRequestController;

// Public routes for partner/distributor requests (read operations)
Route::middleware(['enhanced.rate.limit:public'])->group(function () {
    Route::prefix('partner-requests')->group(function () {
        Route::get('/', [PartnerRequestController::class, 'index']);
        Route::get('/status', [PartnerRequestController::class, 'status']);
        Route::get('/{id}', [PartnerRequestController::class, 'show']);
    });
    
    Route::prefix('distributor-requests')->group(function () {
        Route::get('/', [DistributorRequestController::class, 'index']);
        Route::get('/status', [DistributorRequestController::class, 'status']);
        Route::get('/{id}', [DistributorRequestController::class, 'show']);
    });
});

// Limited rate limiting for form submissions
Route::middleware(['enhanced.rate.limit:checkout'])->group(function () {
    Route::prefix('partner-requests')->group(function () {
        Route::post('/', [PartnerRequestController::class, 'store']);
    });
    
    Route::prefix('distributor-requests')->group(function () {
        Route::post('/', [DistributorRequestController::class, 'store']);
    });
    
    // Newsletter subscription endpoints
    Route::post('newsletter/subscribe', [NewsletterController::class, 'subscribe']);
    Route::post('newsletter/unsubscribe', [NewsletterController::class, 'unsubscribe']);
});

// Admin routes for partner/distributor requests (admin operations)
Route::middleware(['enhanced.rate.limit:admin'])->group(function () {
    Route::prefix('admin/partner-requests')->group(function () {
        Route::get('/', [AdminPartnerRequestController::class, 'index']);
        Route::get('/{id}', [AdminPartnerRequestController::class, 'show']);
        Route::post('/{id}/approve', [AdminPartnerRequestController::class, 'approve']);
        Route::post('/{id}/reject', [AdminPartnerRequestController::class, 'reject']);
    });
    
    Route::prefix('admin/distributor-requests')->group(function () {
        Route::get('/', [AdminDistributorRequestController::class, 'index']);
        Route::get('/{id}', [AdminDistributorRequestController::class, 'show']);
        Route::post('/{id}/approve', [AdminDistributorRequestController::class, 'approve']);
        Route::post('/{id}/reject', [AdminDistributorRequestController::class, 'reject']);
    });
});

// System monitoring endpoints with dedicated rate limiting
Route::middleware(['enhanced.rate.limit:monitoring'])->group(function () {
    Route::prefix('monitoring')->group(function () {
        Route::get('ping', [App\Http\Controllers\SystemMonitoringController::class, 'ping']);
        Route::get('health', [App\Http\Controllers\SystemMonitoringController::class, 'health']);
        Route::get('metrics', [App\Http\Controllers\SystemMonitoringController::class, 'metrics']);
        Route::get('performance', [App\Http\Controllers\SystemMonitoringController::class, 'performance']);
        Route::get('security', [App\Http\Controllers\SystemMonitoringController::class, 'security']);
        Route::get('status', [App\Http\Controllers\SystemMonitoringController::class, 'status']);
    });
});

// Webhook endpoints with dedicated rate limiting
Route::middleware(['enhanced.rate.limit:webhook'])->group(function () {
    Route::post('webhooks/stripe', [App\Http\Controllers\StripeWebhookController::class, 'handleWebhook']);
});

// Contact form routes
use App\Http\Controllers\ContactController;

Route::prefix('contact')->group(function () {
    Route::post('submit', [ContactController::class, 'submit']);
    Route::get('info', [ContactController::class, 'info']);
});
