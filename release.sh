#!/bin/bash
set -e

echo "Starting release process..."

# Copy environment file
echo "Copying environment file..."
cp .env.fly .env

# Generate application key only if not set
echo "Checking application key..."
if ! grep -q "APP_KEY=base64:" .env; then
    echo "Generating application key..."
    php artisan key:generate --force
else
    echo "Application key already exists"
fi

# Clear config cache
echo "Clearing config cache..."
php artisan config:clear || true

# Run database migrations
echo "Running database migrations..."
php artisan migrate --force

# Create storage link
echo "Creating storage link..."
php artisan storage:link || true

echo "Release process completed successfully!"

