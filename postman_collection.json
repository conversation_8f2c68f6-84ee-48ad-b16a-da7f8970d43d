{"info": {"name": "Laravel E-commerce API", "description": "Complete Laravel e-commerce API testing collection", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8000/api"}, {"key": "cart_id", "value": ""}, {"key": "order_id", "value": ""}, {"key": "item_id", "value": ""}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "item": [{"name": "Cart Management", "item": [{"name": "Get Cart", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/panier", "host": ["{{base_url}}"], "path": ["panier"]}}}, {"name": "Add Product to Cart", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"produit_id\": 1,\n  \"quantite\": 2,\n  \"variante_id\": null\n}"}, "url": {"raw": "{{base_url}}/panier/ajouter", "host": ["{{base_url}}"], "path": ["panier", "ajouter"]}}}, {"name": "Update Cart Item", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"quantite\": 3\n}"}, "url": {"raw": "{{base_url}}/panier/items/{{item_id}}", "host": ["{{base_url}}"], "path": ["panier", "items", "{{item_id}}"]}}}, {"name": "Remove Cart Item", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/panier/items/{{item_id}}", "host": ["{{base_url}}"], "path": ["panier", "items", "{{item_id}}"]}}}, {"name": "Clear Cart", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/panier/vider", "host": ["{{base_url}}"], "path": ["panier", "vider"]}}}]}, {"name": "Order Management", "item": [{"name": "Create Order from Cart", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"cart_id\": \"{{cart_id}}\",\n  \"adresse_livraison\": \"123 Test Street\",\n  \"ville_livraison\": \"Casablanca\",\n  \"code_postal_livraison\": \"20000\",\n  \"pays_livraison\": \"Maroc\",\n  \"notes\": \"Test order\"\n}"}, "url": {"raw": "{{base_url}}/commandes", "host": ["{{base_url}}"], "path": ["commandes"]}}}, {"name": "Get All Orders", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/commandes?with=user,produits,paiement&per_page=10", "host": ["{{base_url}}"], "path": ["commandes"], "query": [{"key": "with", "value": "user,produits,paiement"}, {"key": "per_page", "value": "10"}]}}}, {"name": "Get Order Details", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/commandes/{{order_id}}?with=user,produits,paiement", "host": ["{{base_url}}"], "path": ["commandes", "{{order_id}}"], "query": [{"key": "with", "value": "user,produits,paiement"}]}}}, {"name": "Process Payment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"payment_details\": {\n    \"method\": \"carte\",\n    \"transaction_id\": \"TXN_123456789\",\n    \"gateway_response\": {\n      \"status\": \"success\",\n      \"message\": \"Payment processed successfully\",\n      \"reference\": \"REF_987654321\"\n    }\n  }\n}"}, "url": {"raw": "{{base_url}}/commandes/{{order_id}}/pay", "host": ["{{base_url}}"], "path": ["commandes", "{{order_id}}", "pay"]}}}, {"name": "Update Order Status", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"confirmee\",\n  \"notes\": \"Order confirmed\",\n  \"send_notification\": true\n}"}, "url": {"raw": "{{base_url}}/commandes/{{order_id}}/status", "host": ["{{base_url}}"], "path": ["commandes", "{{order_id}}", "status"]}}}]}, {"name": "Product Management", "item": [{"name": "Get Products", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/produits?per_page=15", "host": ["{{base_url}}"], "path": ["produits"], "query": [{"key": "per_page", "value": "15"}]}}}, {"name": "Get Product Details", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/produits/1", "host": ["{{base_url}}"], "path": ["produits", "1"]}}}, {"name": "Search Products", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/produits/search?q=smartphone", "host": ["{{base_url}}"], "path": ["produits", "search"], "query": [{"key": "q", "value": "smartphone"}]}}}]}]}