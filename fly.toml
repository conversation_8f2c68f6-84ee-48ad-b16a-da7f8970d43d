# fly.toml app configuration file generated for laravel-api on 2025-03-24T15:19:00+01:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'laravel-api'
primary_region = 'fra'
console_command = 'php /var/www/html/artisan tinker'

[build]
[build.args]
NODE_VERSION = '18'
PHP_VERSION = '8.2'

[env]
APP_ENV = 'production'
LOG_CHANNEL = 'stderr'
LOG_LEVEL = 'info'
LOG_STDERR_FORMATTER = 'Monolog\Formatter\JsonFormatter'
SESSION_DRIVER = 'cookie'
SESSION_SECURE_COOKIE = 'true'

[deploy]
release_command = "/var/www/html/.fly/scripts/release.sh"

[http_service]
internal_port = 8080
force_https = true
auto_stop_machines = "stop"
auto_start_machines = true
min_machines_running = 1

[[vm]]
memory = '512mb'
cpu_kind = 'shared'
cpus = 1
