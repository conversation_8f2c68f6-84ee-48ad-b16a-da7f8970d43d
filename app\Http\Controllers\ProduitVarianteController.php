<?php

namespace App\Http\Controllers;

use App\Models\Attribut;
use App\Models\Produit;
use App\Models\ProduitVariante;
use App\Models\VarianteValeur;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use App\Http\Requests\StoreProduitVarianteRequest;
use App\Http\Requests\UpdateProduitVarianteRequest;
use App\Http\Requests\UpdateProduitVarianteStockRequest;

class ProduitVarianteController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request, string $produit_id = null)
    {
        try {
            // Filtrer par produit parent
            if (!$request->has('produit_id') && !$produit_id) {
                return response()->json([
                    'error' => 'Paramètre manquant',
                    'message' => 'Le paramètre produit_id est requis'
                ], 400);
            }

            $produitId = $produit_id ?? $request->input('produit_id');
            $produit = Produit::findOrFail($produitId);

            $query = ProduitVariante::with('valeurs.attribut')
                ->where('produit_parent_id', $produitId);

            // Filtrer par statut actif
            if ($request->has('actif')) {
                $query->where('actif', $request->boolean('actif'));
            }

            // Filtrer par disponibilité en stock
            if ($request->has('disponible') && $request->boolean('disponible')) {
                $query->where('stock', '>', 0);
            }

            $variantes = $query->get();

            return response()->json($variantes);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erreur lors de la récupération des variantes',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreProduitVarianteRequest $request, string $produit_id = null)
    {
        try {
            DB::beginTransaction();

            // Si produit_id est fourni dans l'URL, l'utiliser
            if ($produit_id) {
                $request->merge(['produit_id' => $produit_id]);
            }

            // Créer la variante
            $variante = ProduitVariante::create([
                'produit_parent_id' => $request->input('produit_id'),
                'sku' => $request->input('sku'),
                'prix_supplement' => $request->input('prix_supplement'),
                'stock' => $request->input('stock'),
                'actif' => $request->input('actif', true)
            ]);

            // Ajouter les valeurs d'attributs
            foreach ($request->input('valeurs') as $valeurData) {
                $attribut = Attribut::findOrFail($valeurData['attribut_id']);
                $colonne = 'valeur_' . $attribut->type_valeur;

                VarianteValeur::create([
                    'produit_variante_id' => $variante->id,
                    'attribut_id' => $attribut->id,
                    $colonne => $valeurData['valeur']
                ]);
            }

            DB::commit();

            return response()->json($variante->load('valeurs.attribut'), 201);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'error' => 'Erreur lors de la création de la variante',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
            $variante = ProduitVariante::with(['valeurs.attribut', 'produitParent'])->findOrFail($id);
            return response()->json($variante);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Variante non trouvée',
                'message' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateProduitVarianteRequest $request, string $id)
    {
        try {
            DB::beginTransaction();

            $variante = ProduitVariante::findOrFail($id);

            // Mettre à jour les propriétés de base
            $variante->fill($request->only([
                'sku',
                'prix_supplement',
                'stock',
                'actif'
            ]));
            $variante->save();

            // Mettre à jour les valeurs d'attributs si spécifié
            if ($request->has('valeurs')) {
                foreach ($request->input('valeurs') as $valeurData) {
                    $attribut = Attribut::findOrFail($valeurData['attribut_id']);
                    $colonne = 'valeur_' . $attribut->type_valeur;

                    VarianteValeur::updateOrCreate(
                        [
                            'produit_variante_id' => $variante->id,
                            'attribut_id' => $attribut->id
                        ],
                        [
                            $colonne => $valeurData['valeur']
                        ]
                    );
                }
            }

            DB::commit();

            return response()->json($variante->load('valeurs.attribut'));
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'error' => 'Erreur lors de la mise à jour de la variante',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            DB::beginTransaction();

            $variante = ProduitVariante::findOrFail($id);

            // Supprimer les valeurs d'attributs associées
            $variante->valeurs()->delete();

            // Supprimer la variante
            $variante->delete();

            DB::commit();

            return response()->json([
                'message' => 'Variante supprimée avec succès'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'error' => 'Erreur lors de la suppression de la variante',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mettre à jour le stock d'une variante
     */
    public function updateStock(UpdateProduitVarianteStockRequest $request, string $id)
    {
        try {
            $variante = ProduitVariante::findOrFail($id);
            $variante->stock = $request->input('stock');
            $variante->save();

            return response()->json([
                'message' => 'Stock mis à jour avec succès',
                'variante' => $variante
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erreur lors de la mise à jour du stock',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
