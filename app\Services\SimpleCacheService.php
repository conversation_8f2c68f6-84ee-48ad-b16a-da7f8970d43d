<?php

namespace App\Services;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class SimpleCacheService
{
    const CACHE_TTL = 3600; // 1 hour
    private string $cacheDirectory = 'cache';

    public function __construct()
    {
        if (!Storage::disk('local')->exists($this->cacheDirectory)) {
            Storage::disk('local')->makeDirectory($this->cacheDirectory);
        }
    }

    public function get(string $key)
    {
        try {
            $filename = $this->getCacheFilename($key);

            if (!Storage::disk('local')->exists($filename)) {
                return null;
            }

            $content = Storage::disk('local')->get($filename);
            $cacheData = unserialize($content);

            if ($cacheData['expires_at'] < Carbon::now()->timestamp) {
                Storage::disk('local')->delete($filename);
                return null;
            }

            return $cacheData['data'];
        } catch (\Exception $e) {
            Log::error('Cache get error: ' . $e->getMessage());
            return null;
        }
    }

    public function put(string $key, $data, int $ttl = self::CACHE_TTL): void
    {
        try {
            $cacheData = [
                'data' => $data,
                'expires_at' => Carbon::now()->addSeconds($ttl)->timestamp,
                'created_at' => Carbon::now()->timestamp
            ];

            $filename = $this->getCacheFilename($key);
            Storage::disk('local')->put($filename, serialize($cacheData));
        } catch (\Exception $e) {
            Log::error('Cache put error: ' . $e->getMessage());
        }
    }

    public function forget(string $key): void
    {
        try {
            $filename = $this->getCacheFilename($key);
            if (Storage::disk('local')->exists($filename)) {
                Storage::disk('local')->delete($filename);
            }
        } catch (\Exception $e) {
            Log::error('Cache forget error: ' . $e->getMessage());
        }
    }

    private function getCacheFilename(string $key): string
    {
        return $this->cacheDirectory . '/' . md5($key) . '.cache';
    }

    // Product-specific cache methods for compatibility
    public function getProductListing(string $cacheKey)
    {
        return $this->get('product_list:' . $cacheKey);
    }

    public function setProductListing(string $cacheKey, $data, int $ttl = self::CACHE_TTL): void
    {
        $this->put('product_list:' . $cacheKey, $data, $ttl);
    }

    public function getProductDetails(int $productId)
    {
        return $this->get('product_detail:' . $productId);
    }

    public function setProductDetails(int $productId, $data, int $ttl = self::CACHE_TTL): void
    {
        $this->put('product_detail:' . $productId, $data, $ttl);
    }

    public function flushProductCache(): void
    {
        try {
            $files = Storage::disk('local')->files($this->cacheDirectory);
            $deletedCount = 0;

            foreach ($files as $file) {
                // Delete all cache files that start with product prefixes
                if (str_contains($file, 'product_list') || str_contains($file, 'product_detail')) {
                    Storage::disk('local')->delete($file);
                    $deletedCount++;
                }
            }

            Log::info("Product cache flushed", ['deleted_files' => $deletedCount]);
        } catch (\Exception $e) {
            Log::error('Failed to flush product cache: ' . $e->getMessage());
        }
    }

    // Additional methods for controller compatibility
    public function invalidateProductCaches(): void
    {
        $this->flushProductCache();
    }

    public function forgetProductCache(int $productId): void
    {
        $this->forget('product_detail:' . $productId);
        // Also clear any related product list caches
        $this->flushProductCache();
    }

    /**
     * Clear cache entries matching a pattern
     */
    public function forgetPattern(string $pattern): void
    {
        try {
            // Convert pattern to regex (simple * wildcard support)
            $regex = str_replace('*', '.*', preg_quote($pattern, '/'));

            $files = Storage::disk('local')->files($this->cacheDirectory);
            $deletedCount = 0;

            foreach ($files as $file) {
                $filename = basename($file, '.cache');
                if (preg_match('/^' . $regex . '$/', $filename)) {
                    Storage::disk('local')->delete($file);
                    $deletedCount++;
                }
            }

            Log::debug('Cache pattern cleared', [
                'pattern' => $pattern,
                'deleted_count' => $deletedCount
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to clear cache pattern: ' . $e->getMessage(), [
                'pattern' => $pattern
            ]);
        }
    }
}
