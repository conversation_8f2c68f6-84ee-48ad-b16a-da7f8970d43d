<?php

namespace App\Http\Controllers;

use App\Models\Image;
use App\Models\Produit;
use App\Models\Categorie;
use App\Models\SousCategorie;
use App\Models\sous_sousCategorie;
use App\Models\Collection;
use App\Models\Marque;
use App\Models\ProduitVariante;
use App\Models\CarouselSlide;
use App\Services\ImageService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use App\Http\Requests\StoreImageRequest;
use App\Http\Requests\UploadMultipleImagesRequest;
use App\Http\Requests\GetImagesRequest;
use App\Http\Requests\UpdateImageRequest;
use App\Http\Requests\ReorderImagesRequest;

class ImageController extends Controller
{
    /**
     * The image service instance.
     *
     * @var \App\Services\ImageService
     */
    protected $imageService;

    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\ImageService  $imageService
     * @return void
     */
    public function __construct(ImageService $imageService)
    {
        $this->imageService = $imageService;
    }

    /**
     * Upload an image for a model.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function upload(StoreImageRequest $request)
    {
        $validated = $request->validated();
        // Get the model
        $model = $this->getModel($validated['model_type'], $validated['model_id']);
        if (!$model) {
            return response()->json(['error' => 'Model not found'], 404);
        }
        // Set upload options
        $options = [
            'optimize' => true,
            'thumbnails' => ['small', 'medium', 'large'],
            'is_primary' => $this->convertToBoolean($validated['is_primary'] ?? false),
            'alt_text' => $validated['alt_text'] ?? null,
            'title' => $validated['title'] ?? null,
        ];
        // Check if the image file exists in the request
        if (!$request->hasFile('image')) {
            return response()->json([
                'errors' => [
                    'image' => ['No image file was provided in the request.']
                ]
            ], 422);
        }
        // Check if the image file is valid
        $file = $request->file('image');
        if (!$file->isValid()) {
            return response()->json([
                'errors' => [
                    'image' => ['The image file is invalid or corrupted. Error code: ' . $file->getError()]
                ]
            ], 422);
        }
        // Check if the file is actually an image
        $mimeType = $file->getMimeType();
        $allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
        if (!in_array($mimeType, $allowedMimeTypes)) {
            return response()->json([
                'errors' => [
                    'image' => ['The file is not a valid image. Detected MIME type: ' . $mimeType]
                ]
            ], 422);
        }
        // Get file details for debugging
        $fileDetails = [
            'original_name' => $file->getClientOriginalName(),
            'mime_type' => $file->getMimeType(),
            'size' => $file->getSize(),
            'error' => $file->getError(),
        ];
        // Upload the image
        try {
            $directory = $this->getDirectoryForModel($validated['model_type'], $model);
            $options['model'] = $model;
            $image = $this->imageService->upload($file, $directory, $options);
            $image->direct_url = $image->getDirectUrlAttribute();
            return response()->json([
                'message' => 'Image uploaded successfully',
                'image' => $image,
                'url' => $image->url,
                'direct_url' => $image->direct_url,
            ], 201);
        } catch (\Exception $e) {
            \Log::error('Image upload failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'file_details' => $fileDetails,
                'model_type' => $validated['model_type'],
                'model_id' => $validated['model_id'],
            ]);
            $errorMessage = 'The image failed to upload. Error: ' . $e->getMessage();
            $statusCode = 500;
            if (strpos($e->getMessage(), 'SQLSTATE') !== false) {
                $errorMessage = 'Database error while saving the image. Please check if the model exists and try again.';
            }
            if (strpos($e->getMessage(), 'storage') !== false || strpos($e->getMessage(), 'disk') !== false) {
                $errorMessage = 'Error storing the image. Please check storage configuration.';
            }
            if (strpos($e->getMessage(), 'permission') !== false || strpos($e->getMessage(), 'read') !== false || strpos($e->getMessage(), 'write') !== false) {
                $errorMessage = 'File system error. Please check file permissions.';
            }
            return response()->json([
                'errors' => [
                    'image' => [$errorMessage]
                ],
                'debug_info' => [
                    'file_details' => $fileDetails,
                    'error_class' => get_class($e),
                    'error_message' => $e->getMessage(),
                    'error_code' => $e->getCode(),
                ]
            ], $statusCode);
        }
    }

    /**
     * Upload multiple images for a model.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadMultiple(UploadMultipleImagesRequest $request)
    {
        $validated = $request->validated();
        $model = $this->getModel($validated['model_type'], $validated['model_id']);
        if (!$model) {
            return response()->json(['error' => 'Model not found'], 404);
        }
        $options = [
            'optimize' => true,
            'thumbnails' => ['small', 'medium', 'large'],
            'alt_text' => $validated['alt_text'] ?? null,
            'title' => $validated['title'] ?? null,
            'model' => $model,
        ];
        try {
            $directory = $this->getDirectoryForModel($validated['model_type'], $model);
            $images = $this->imageService->uploadMultiple($request->file('images'), $directory, $options);
            foreach ($images as $image) {
                $image->url = $image->getUrlAttribute();
                $image->direct_url = $image->getDirectUrlAttribute();
                $image->thumbnail_small = $image->getThumbnailUrl('small');
                $image->thumbnail_medium = $image->getThumbnailUrl('medium');
                $image->thumbnail_large = $image->getThumbnailUrl('large');
            }
            return response()->json([
                'message' => count($images) . ' images uploaded successfully',
                'images' => $images,
            ], 201);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Get all images for a model.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getImages(GetImagesRequest $request)
    {
        $validated = $request->validated();
        $model = $this->getModel($validated['model_type'], $validated['model_id']);
        if (!$model) {
            return response()->json(['error' => 'Model not found'], 404);
        }
        $images = $model->images()->orderBy('order')->get();
        $images->each(function ($image) {
            $image->url = $image->getUrlAttribute();
            $image->direct_url = $image->getDirectUrlAttribute();
            $image->thumbnail_small = $image->getThumbnailUrl('small');
            $image->thumbnail_medium = $image->getThumbnailUrl('medium');
            $image->thumbnail_large = $image->getThumbnailUrl('large');
        });
        return response()->json([
            'images' => $images,
        ]);
    }

    /**
     * Update image details.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(UpdateImageRequest $request, $id)
    {
        $validated = $request->validated();
        $image = Image::findOrFail($id);
        $data = [
            'alt_text' => $validated['alt_text'] ?? null,
            'title' => $validated['title'] ?? null,
            'order' => $validated['order'] ?? null,
        ];
        if ($request->has('is_primary')) {
            $data['is_primary'] = $this->convertToBoolean($validated['is_primary']);
        }
        $image->fill($data);
        $image->save();
        if ($request->has('is_primary') && $this->convertToBoolean($validated['is_primary'])) {
            $image->imageable->images()
                ->where('id', '!=', $image->id)
                ->update(['is_primary' => false]);
        }
        $image->url = $image->getUrlAttribute();
        $image->direct_url = $image->getDirectUrlAttribute();
        $image->thumbnail_small = $image->getThumbnailUrl('small');
        $image->thumbnail_medium = $image->getThumbnailUrl('medium');
        $image->thumbnail_large = $image->getThumbnailUrl('large');
        return response()->json([
            'message' => 'Image updated successfully',
            'image' => $image,
        ]);
    }

    /**
     * Delete an image.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        // Find the image
        $image = Image::findOrFail($id);

        // Delete the image
        $this->imageService->delete($image);

        return response()->json([
            'message' => 'Image deleted successfully',
        ]);
    }

    /**
     * Reorder images for a model.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function reorder(ReorderImagesRequest $request)
    {
        $validated = $request->validated();
        $model = $this->getModel($validated['model_type'], $validated['model_id']);
        if (!$model) {
            return response()->json(['error' => 'Model not found'], 404);
        }
        $imageIds = $validated['image_ids'];
        foreach ($imageIds as $index => $imageId) {
            $model->images()->where('id', $imageId)->update(['order' => $index]);
        }
        return response()->json([
            'message' => 'Images reordered successfully',
        ]);
    }

    /**
     * Get the model instance based on type and ID.
     *
     * @param  string  $type
     * @param  int  $id
     * @return mixed
     */
    protected function getModel($type, $id)
    {
        return match ($type) {
            'produit' => Produit::find($id),
            'categorie' => Categorie::find($id),
            'sous_categorie' => SousCategorie::find($id),
            'sous_sous_categorie' => sous_sousCategorie::find($id),
            'collection' => Collection::find($id),
            'marque' => Marque::find($id),
            'produit_variante' => ProduitVariante::find($id),
            'carousel_slide' => CarouselSlide::find($id),
            default => null,
        };
    }

    /**
     * Get the directory for storing images based on model type.
     *
     * @param  string  $type
     * @param  mixed  $model
     * @return string
     */
    protected function getDirectoryForModel($type, $model)
    {
        return match ($type) {
            'produit' => "produits/{$model->id}",
            'categorie' => "categories/{$model->id}",
            'sous_categorie' => "sous_categories/{$model->id}",
            'sous_sous_categorie' => "sous_sous_categories/{$model->id}",
            'collection' => "collections/{$model->id}",
            'marque' => "marques/{$model->id}",
            'produit_variante' => "produits/{$model->produit_parent_id}/variantes/{$model->id}",
            'carousel_slide' => "carousels/{$model->carousel_id}/slides/{$model->id}",
            default => "uploads",
        };
    }

    /**
     * Convert various representations of boolean values to actual boolean.
     *
     * @param  mixed  $value
     * @return bool
     */
    protected function convertToBoolean($value): bool
    {
        if (is_bool($value)) {
            return $value;
        }

        if (is_string($value)) {
            return in_array(strtolower($value), ['true', '1', 'yes', 'on']);
        }

        if (is_numeric($value)) {
            return (int) $value === 1;
        }

        return false;
    }
}
