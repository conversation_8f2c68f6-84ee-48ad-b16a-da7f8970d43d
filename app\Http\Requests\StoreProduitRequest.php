<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreProduitRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'nom_produit' => 'required|string|max:255',
            'description_produit' => 'nullable|string',
            'prix_produit' => 'required|numeric|min:0',
            'image_produit' => 'nullable|url',
            'quantite_produit' => 'required|integer|min:1',
            'marque_id' => 'required|exists:marques,id',
            'sous_sous_categorie_id' => 'required|exists:sous_sous_categories,id',
        ];
    }

    public function messages()
    {
        return [
            'nom_produit.required' => 'Le nom du produit est obligatoire.',
            'prix_produit.required' => 'Le prix du produit est obligatoire.',
            'prix_produit.numeric' => 'Le prix du produit doit être un nombre.',
            'prix_produit.min' => 'Le prix du produit doit être supérieur ou égal à 0.',
            'quantite_produit.required' => 'La quantité du produit est obligatoire.',
            'quantite_produit.integer' => 'La quantité doit être un nombre entier.',
            'quantite_produit.min' => 'La quantité doit être au moins 1.',
            'marque_id.required' => 'La marque est obligatoire.',
            'marque_id.exists' => 'La marque sélectionnée est invalide.',
            'sous_sous_categorie_id.required' => 'La sous-sous-catégorie est obligatoire.',
            'sous_sous_categorie_id.exists' => 'La sous-sous-catégorie sélectionnée est invalide.',
        ];
    }
} 