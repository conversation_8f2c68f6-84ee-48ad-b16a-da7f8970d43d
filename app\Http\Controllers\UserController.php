<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;

class UserController extends Controller
{
    public function index()
    {
        $users = User::all();
        return response()->json($users);
    }
    public function show($id)

    {
        try{
        $user = User::findOrFail($id);
        return response()->json($user);
    } catch (\Exception $e) {
        return response()->json(["error"=>"probleme de récupération des données {$e->getMessage()}"]);
    }
    }
    public function update(Request $request, $id)
    {
        try{
        $user = User::findOrFail($id);
        $user->update($request->all());
        return response()->json($user);
    } catch (\Exception $e) {
        return response()->json(["error"=>"probleme de modification {$e->getMessage()}"]);
    }
    }
    public function destroy($id)
    {
        try{
        $user = User::findOrFail($id);
        $user->delete();
        return response()->json(["message" => "User supprimée avec succès"], 200);
    } catch (\Exception $e) {
        return response()->json(["error"=>"probleme de suppression de User {$e->getMessage()}"]);
    }
    }

}
