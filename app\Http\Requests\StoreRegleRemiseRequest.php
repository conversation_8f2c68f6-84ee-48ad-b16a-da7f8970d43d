<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreRegleRemiseRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'nom' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type_client' => 'required|in:standard,premium,affilie,groupe',
            'valeur' => 'required|numeric|min:0',
            'type' => 'required|in:pourcentage,montant_fixe',
            'priorité' => 'nullable|integer',
            'active' => 'nullable|boolean',
            'conditions_supplementaires' => 'nullable|array',
        ];
    }

    public function messages()
    {
        return [
            'nom.required' => 'Le nom est obligatoire.',
            'type_client.required' => 'Le type de client est obligatoire.',
            'type_client.in' => 'Le type de client est invalide.',
            'valeur.required' => 'La valeur est obligatoire.',
            'valeur.numeric' => 'La valeur doit être un nombre.',
            'valeur.min' => 'La valeur doit être au moins 0.',
            'type.required' => 'Le type est obligatoire.',
            'type.in' => 'Le type est invalide.',
        ];
    }
} 