<?php

namespace App\Console\Commands;

use App\Models\Image;
use App\Models\ProduitVariante;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class AssignImageToVariante extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'images:assign-to-variante
                            {variante_id : ID de la variante}
                            {image_path : Chemin de l\'image dans le stockage S3}
                            {--primary : Définir comme image principale}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Associe une image existante à une variante de produit';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $varianteId = $this->argument('variante_id');
        $imagePath = $this->argument('image_path');
        $isPrimary = $this->option('primary');

        // Vérifier si la variante existe
        $variante = ProduitVariante::find($varianteId);
        if (!$variante) {
            $this->error("La variante avec l'ID {$varianteId} n'existe pas.");
            return 1;
        }

        $this->info("Variante trouvée: {$variante->sku} (ID: {$variante->id})");

        // Vérifier si l'image existe dans le stockage
        if (!Storage::disk('s3')->exists($imagePath)) {
            $this->error("L'image {$imagePath} n'existe pas dans le stockage S3.");
            return 1;
        }

        // Vérifier si l'image est déjà associée à cette variante
        $existingImage = $variante->images()->where('path', $imagePath)->first();
        if ($existingImage) {
            $this->warn("Cette image est déjà associée à cette variante.");
            
            // Mettre à jour l'image comme principale si demandé
            if ($isPrimary && !$existingImage->is_primary) {
                $this->info("Mise à jour de l'image comme image principale...");
                
                // Mettre à jour toutes les autres images pour qu'elles ne soient plus principales
                $variante->images()->where('id', '!=', $existingImage->id)->update(['is_primary' => false]);
                
                // Définir cette image comme principale
                $existingImage->update(['is_primary' => true]);
                
                $this->info("Image définie comme principale avec succès.");
            }
            
            return 0;
        }

        // Récupérer les métadonnées de l'image
        $size = Storage::disk('s3')->size($imagePath);
        $mimeType = Storage::disk('s3')->mimeType($imagePath);
        $filename = basename($imagePath);

        // Créer l'enregistrement d'image
        $image = new Image([
            'path' => $imagePath,
            'filename' => $filename,
            'disk' => 's3',
            'mime_type' => $mimeType,
            'size' => $size,
            'alt_text' => "Image pour {$variante->sku}",
            'title' => "Variante {$variante->sku}",
            'is_primary' => $isPrimary || !$variante->images()->exists(), // Première image ou option --primary
            'order' => $variante->images()->count(),
            'metadata' => [
                'original_filename' => $filename,
                'extension' => pathinfo($filename, PATHINFO_EXTENSION),
            ],
        ]);

        // Si cette image est définie comme principale, mettre à jour les autres images
        if ($image->is_primary) {
            $variante->images()->update(['is_primary' => false]);
        }

        // Associer l'image à la variante
        $variante->images()->save($image);

        $this->info("Image associée avec succès à la variante.");
        $this->info("URL de l'image: " . Storage::disk('s3')->url($imagePath));

        return 0;
    }
}
