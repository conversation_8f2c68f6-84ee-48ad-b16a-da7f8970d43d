<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AddCartItemRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'produit_id' => 'required|exists:produits,id',
            'variante_id' => 'nullable|exists:produit_variantes,id',
            'quantite' => 'required|integer|min:1',
        ];
    }

    public function messages()
    {
        return [
            'produit_id.required' => 'Le champ produit_id est obligatoire.',
            'produit_id.exists' => 'Le produit sélectionné est invalide.',
            'variante_id.exists' => 'La variante sélectionnée est invalide.',
            'quantite.required' => 'Le champ quantite est obligatoire.',
            'quantite.integer' => 'La quantité doit être un nombre entier.',
            'quantite.min' => 'La quantité doit être au moins 1.',
        ];
    }
} 