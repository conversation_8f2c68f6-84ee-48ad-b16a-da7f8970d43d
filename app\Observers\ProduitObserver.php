<?php

namespace App\Observers;

use App\Models\Produit;
use App\Services\CacheService;
use Illuminate\Support\Facades\Cache;

class ProduitObserver
{
    protected $cacheService;

    public function __construct(CacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    /**
     * Handle the Produit "created" event.
     */
    public function created(Produit $produit): void
    {
        $this->invalidateRelatedCaches($produit);
    }

    /**
     * Handle the Produit "updated" event.
     */
    public function updated(Produit $produit): void
    {
        $this->invalidateRelatedCaches($produit);

        // Clear specific product cache
        $this->cacheService->forget('product_' . $produit->id);

        // Clear product attributes cache
        $this->cacheService->forget('product_attributes_' . $produit->id);
    }

    /**
     * Handle the Produit "deleted" event.
     */
    public function deleted(Produit $produit): void
    {
        $this->invalidateRelatedCaches($produit);

        // Clear specific product cache
        $this->cacheService->forget('product_' . $produit->id);

        // Clear product attributes cache
        $this->cacheService->forget('product_attributes_' . $produit->id);
    }

    /**
     * Handle the Produit "restored" event.
     */
    public function restored(Produit $produit): void
    {
        $this->invalidateRelatedCaches($produit);
    }

    /**
     * Handle the Produit "force deleted" event.
     */
    public function forceDeleted(Produit $produit): void
    {
        $this->invalidateRelatedCaches($produit);

        // Clear specific product cache
        $this->cacheService->forget('product_' . $produit->id);

        // Clear product attributes cache
        $this->cacheService->forget('product_attributes_' . $produit->id);
    }

    /**
     * Invalidate related caches when a product changes
     */
    protected function invalidateRelatedCaches(Produit $produit): void
    {
        // Clear product lists cache
        Cache::flush(); // Consider this for product list queries with varying parameters

        // Clear popular products cache
        $this->cacheService->forgetPopularProducts();

        // Clear brand-related cache if marque_id changed
        if ($produit->isDirty('marque_id')) {
            if ($produit->getOriginal('marque_id')) {
                $this->cacheService->forgetBrand($produit->getOriginal('marque_id'));
            }
            if ($produit->marque_id) {
                $this->cacheService->forgetBrand($produit->marque_id);
            }
        }

        // Clear category-related cache if category changed
        if ($produit->isDirty('sous_sous_categorie_id')) {
            if ($produit->getOriginal('sous_sous_categorie_id')) {
                $this->cacheService->forgetCategories();
            }
            if ($produit->sous_sous_categorie_id) {
                $this->cacheService->forgetCategories();
            }
        }
    }
}
