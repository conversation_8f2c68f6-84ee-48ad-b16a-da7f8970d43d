<?php

namespace Database\Seeders;

use App\Models\Attribut;
use App\Models\Produit;
use App\Models\ProduitVariante;
use App\Models\VarianteValeur;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ProductVariantsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::beginTransaction();

        try {
            // Récupérer le produit parent
            $produit = Produit::findOrFail(3); // Parure de lit Élégance
            
            // Récupérer les attributs
            $couleurAttribut = Attribut::where('nom', 'Couleur')->first();
            $tailleAttribut = Attribut::where('nom', 'Taille')->first();
            
            // Définir les variantes à créer
            $variants = [
                [
                    'sku' => 'ELEGANCE-BLANC-140',
                    'prix_supplement' => 0,
                    'stock' => 20,
                    'attributs' => [
                        ['attribut' => $couleurAttribut, 'valeur' => 'Blanc'],
                        ['attribut' => $tailleAttribut, 'valeur' => '140x200 cm']
                    ]
                ],
                [
                    'sku' => 'ELEGANCE-BLANC-160',
                    'prix_supplement' => 10,
                    'stock' => 15,
                    'attributs' => [
                        ['attribut' => $couleurAttribut, 'valeur' => 'Blanc'],
                        ['attribut' => $tailleAttribut, 'valeur' => '160x200 cm']
                    ]
                ],
                [
                    'sku' => 'ELEGANCE-BLANC-180',
                    'prix_supplement' => 20,
                    'stock' => 10,
                    'attributs' => [
                        ['attribut' => $couleurAttribut, 'valeur' => 'Blanc'],
                        ['attribut' => $tailleAttribut, 'valeur' => '180x200 cm']
                    ]
                ],
                [
                    'sku' => 'ELEGANCE-BLANC-220',
                    'prix_supplement' => 30,
                    'stock' => 5,
                    'attributs' => [
                        ['attribut' => $couleurAttribut, 'valeur' => 'Blanc'],
                        ['attribut' => $tailleAttribut, 'valeur' => '220x240 cm']
                    ]
                ],
                [
                    'sku' => 'ELEGANCE-BEIGE-140',
                    'prix_supplement' => 0,
                    'stock' => 18,
                    'attributs' => [
                        ['attribut' => $couleurAttribut, 'valeur' => 'Beige'],
                        ['attribut' => $tailleAttribut, 'valeur' => '140x200 cm']
                    ]
                ],
                [
                    'sku' => 'ELEGANCE-BEIGE-160',
                    'prix_supplement' => 10,
                    'stock' => 12,
                    'attributs' => [
                        ['attribut' => $couleurAttribut, 'valeur' => 'Beige'],
                        ['attribut' => $tailleAttribut, 'valeur' => '160x200 cm']
                    ]
                ],
                [
                    'sku' => 'ELEGANCE-BEIGE-180',
                    'prix_supplement' => 20,
                    'stock' => 8,
                    'attributs' => [
                        ['attribut' => $couleurAttribut, 'valeur' => 'Beige'],
                        ['attribut' => $tailleAttribut, 'valeur' => '180x200 cm']
                    ]
                ],
                [
                    'sku' => 'ELEGANCE-BEIGE-220',
                    'prix_supplement' => 30,
                    'stock' => 4,
                    'attributs' => [
                        ['attribut' => $couleurAttribut, 'valeur' => 'Beige'],
                        ['attribut' => $tailleAttribut, 'valeur' => '220x240 cm']
                    ]
                ],
                [
                    'sku' => 'ELEGANCE-GRIS-140',
                    'prix_supplement' => 0,
                    'stock' => 15,
                    'attributs' => [
                        ['attribut' => $couleurAttribut, 'valeur' => 'Gris clair'],
                        ['attribut' => $tailleAttribut, 'valeur' => '140x200 cm']
                    ]
                ],
                [
                    'sku' => 'ELEGANCE-GRIS-160',
                    'prix_supplement' => 10,
                    'stock' => 10,
                    'attributs' => [
                        ['attribut' => $couleurAttribut, 'valeur' => 'Gris clair'],
                        ['attribut' => $tailleAttribut, 'valeur' => '160x200 cm']
                    ]
                ],
                [
                    'sku' => 'ELEGANCE-GRIS-180',
                    'prix_supplement' => 20,
                    'stock' => 5,
                    'attributs' => [
                        ['attribut' => $couleurAttribut, 'valeur' => 'Gris clair'],
                        ['attribut' => $tailleAttribut, 'valeur' => '180x200 cm']
                    ]
                ],
                [
                    'sku' => 'ELEGANCE-GRIS-220',
                    'prix_supplement' => 30,
                    'stock' => 3,
                    'attributs' => [
                        ['attribut' => $couleurAttribut, 'valeur' => 'Gris clair'],
                        ['attribut' => $tailleAttribut, 'valeur' => '220x240 cm']
                    ]
                ]
            ];
            
            // Créer les variantes
            foreach ($variants as $variantData) {
                $variant = new ProduitVariante([
                    'produit_parent_id' => $produit->id,
                    'sku' => $variantData['sku'],
                    'prix_supplement' => $variantData['prix_supplement'],
                    'stock' => $variantData['stock'],
                    'actif' => true
                ]);
                $variant->save();
                
                // Ajouter les valeurs d'attributs pour cette variante
                foreach ($variantData['attributs'] as $attributData) {
                    if ($attributData['attribut']) {
                        $colonne = 'valeur_' . $attributData['attribut']->type_valeur;
                        VarianteValeur::create([
                            'produit_variante_id' => $variant->id,
                            'attribut_id' => $attributData['attribut']->id,
                            $colonne => $attributData['valeur']
                        ]);
                    }
                }
            }
            
            DB::commit();
            $this->command->info('Variantes créées avec succès pour le produit ID 3!');
        } catch (\Exception $e) {
            DB::rollBack();
            $this->command->error('Erreur lors de la création des variantes: ' . $e->getMessage());
        }
    }
}
