<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('images', function (Blueprint $table) {
            $table->id();
            $table->string('path')->comment('Path to the image in S3');
            $table->string('filename')->comment('Original filename');
            $table->string('disk')->default('s3')->comment('Storage disk');
            $table->string('mime_type')->nullable()->comment('MIME type of the image');
            $table->integer('size')->nullable()->comment('Size in bytes');
            $table->string('alt_text')->nullable()->comment('Alternative text for accessibility');
            $table->string('title')->nullable()->comment('Title for the image');
            $table->morphs('imageable'); // Polymorphic relationship
            $table->boolean('is_primary')->default(false)->comment('Whether this is the primary image');
            $table->integer('order')->default(0)->comment('Order for display');
            $table->json('metadata')->nullable()->comment('Additional metadata like dimensions, etc.');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('images');
    }
};
