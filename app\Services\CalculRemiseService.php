<?php

namespace App\Services;

use App\Models\Produit;
use App\Models\Promotion;
use App\Models\RegleRemise;
use App\Models\User;

class CalculRemiseService
{
    /**
     * Calculer la remise totale pour un produit et un client
     *
     * @param Produit $produit
     * @param User|null $client
     * @return array
     */
    public function calculerRemiseProduit(Produit $produit, User $client = null)
    {
        $prixOriginal = $produit->prix_produit ?? $produit->prix ?? 0;
        $remises = [];
        $remiseTotale = 0;
        $prixFinal = $prixOriginal;

        // Si pas de client, on ne calcule que les promotions produit
        if (!$client) {
            return $this->calculerPromotionsProduit($produit, $prixOriginal);
        }

        // 1. Remise basée sur le profil client
        $remiseClient = $this->calculerRemiseClient($client, $prixOriginal);
        if ($remiseClient['montant'] > 0) {
            $remises[] = $remiseClient;
            $remiseTotale += $remiseClient['montant'];
        }

        // 2. Remise personnelle du client
        if ($client->remise_personnelle > 0) {
            $montantRemisePersonnelle = $prixOriginal * ($client->remise_personnelle / 100);
            $remises[] = [
                'type' => 'remise_personnelle',
                'description' => 'Remise personnelle',
                'valeur' => $client->remise_personnelle,
                'montant' => $montantRemisePersonnelle
            ];
            $remiseTotale += $montantRemisePersonnelle;
        }

        // 3. Promotions sur le produit
        $promotionsProduit = $this->calculerPromotionsProduit($produit, $prixOriginal);
        foreach ($promotionsProduit['remises'] as $promotion) {
            // Vérifier si la promotion est applicable au profil du client
            if ($this->estPromotionApplicableAuClient($promotion['promotion_id'] ?? null, $client)) {
                $remises[] = $promotion;
                $remiseTotale += $promotion['montant'];
            }
        }

        // 4. Promotions sur les collections du produit
        $promotionsCollection = $this->calculerPromotionsCollection($produit, $prixOriginal, $client);
        foreach ($promotionsCollection['remises'] as $promotion) {
            $remises[] = $promotion;
            $remiseTotale += $promotion['montant'];
        }

        // Limiter la remise totale au prix du produit
        $remiseTotale = min($remiseTotale, $prixOriginal);
        $prixFinal = $prixOriginal - $remiseTotale;

        return [
            'prix_original' => $prixOriginal,
            'remises' => $remises,
            'remise_totale' => $remiseTotale,
            'prix_final' => $prixFinal
        ];
    }

    /**
     * Calculer la remise basée sur le profil du client
     *
     * @param User $client
     * @param float $montant
     * @return array
     */
    private function calculerRemiseClient(User $client, float $montant)
    {
        $profilRemise = $client->profil_remise;

        $regleRemise = RegleRemise::where('type_client', $profilRemise)
            ->where('active', true)
            ->orderBy('priorité', 'desc')
            ->first();

        if (!$regleRemise) {
            return [
                'type' => 'profil_client',
                'description' => 'Aucune remise pour ce profil',
                'valeur' => 0,
                'montant' => 0
            ];
        }

        $montantRemise = $regleRemise->calculerRemise($montant);

        return [
            'type' => 'profil_client',
            'description' => "Remise {$profilRemise}",
            'valeur' => $regleRemise->type === 'pourcentage' ? $regleRemise->valeur . '%' : $regleRemise->valeur . '€',
            'montant' => $montantRemise
        ];
    }

    /**
     * Calculer les promotions applicables à un produit
     *
     * @param Produit $produit
     * @param float $prixOriginal
     * @return array
     */
    private function calculerPromotionsProduit(Produit $produit, float $prixOriginal)
    {
        $remises = [];
        $remiseTotale = 0;

        $promotions = $produit->promotions()
            ->whereIn('promotions.statut', ['active', 'programmée'])
            ->where(function ($query) {
                $now = now();
                $query->whereNull('promotions.date_debut')
                    ->orWhere('promotions.date_debut', '<=', $now);
            })
            ->where(function ($query) {
                $now = now();
                $query->whereNull('promotions.date_fin')
                    ->orWhere('promotions.date_fin', '>=', $now);
            })
            ->orderBy('promotions.priorité', 'desc')
            ->get();

        foreach ($promotions as $promotion) {
            $montantRemise = $promotion->calculerRemise($prixOriginal);

            $remises[] = [
                'type' => 'promotion_produit',
                'description' => $promotion->nom,
                'valeur' => $promotion->type === 'pourcentage' ? $promotion->valeur . '%' : $promotion->valeur . '€',
                'montant' => $montantRemise,
                'promotion_id' => $promotion->id
            ];

            if (!$promotion->cumulable) {
                $remiseTotale = $montantRemise;
                break;
            }

            $remiseTotale += $montantRemise;
        }

        return [
            'remises' => $remises,
            'remise_totale' => $remiseTotale
        ];
    }

    /**
     * Calculer les promotions applicables via les collections du produit
     *
     * @param Produit $produit
     * @param float $prixOriginal
     * @param User $client
     * @return array
     */
    private function calculerPromotionsCollection(Produit $produit, float $prixOriginal, User $client)
    {
        $remises = [];
        $remiseTotale = 0;

        $collections = $produit->collections;

        foreach ($collections as $collection) {
            $promotions = $collection->promotions()
                ->whereIn('promotions.statut', ['active', 'programmée'])
                ->where(function ($query) {
                    $now = now();
                    $query->whereNull('promotions.date_debut')
                        ->orWhere('promotions.date_debut', '<=', $now);
                })
                ->where(function ($query) {
                    $now = now();
                    $query->whereNull('promotions.date_fin')
                        ->orWhere('promotions.date_fin', '>=', $now);
                })
                ->orderBy('promotions.priorité', 'desc')
                ->get();

            foreach ($promotions as $promotion) {
                // Vérifier si la promotion est applicable au profil du client
                if (!$this->estPromotionApplicableAuClient($promotion->id, $client)) {
                    continue;
                }

                $montantRemise = $promotion->calculerRemise($prixOriginal);

                $remises[] = [
                    'type' => 'promotion_collection',
                    'description' => $promotion->nom . ' (Collection: ' . $collection->nom . ')',
                    'valeur' => $promotion->type === 'pourcentage' ? $promotion->valeur . '%' : $promotion->valeur . '€',
                    'montant' => $montantRemise,
                    'promotion_id' => $promotion->id
                ];

                if (!$promotion->cumulable) {
                    $remiseTotale = max($remiseTotale, $montantRemise);
                    break;
                }

                $remiseTotale += $montantRemise;
            }
        }

        return [
            'remises' => $remises,
            'remise_totale' => $remiseTotale
        ];
    }

    /**
     * Vérifier si une promotion est applicable à un client
     *
     * @param int|null $promotionId
     * @param User $client
     * @return bool
     */
    private function estPromotionApplicableAuClient(?int $promotionId, User $client)
    {
        if (!$promotionId) {
            return false;
        }

        $promotion = Promotion::find($promotionId);

        if (!$promotion) {
            return false;
        }

        // Si la promotion n'a pas de profils de remise spécifiques, elle est applicable à tous
        $profilsRemise = $promotion->profilsRemise;
        if ($profilsRemise->isEmpty()) {
            return true;
        }

        // Vérifier si le profil du client est dans la liste des profils autorisés
        foreach ($profilsRemise as $profil) {
            if ($profil->profil_remise === $client->profil_remise) {
                return true;
            }
        }

        return false;
    }
}
