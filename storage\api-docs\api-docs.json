{"openapi": "3.0.0", "info": {"title": "Jihene-Line API", "description": "Documentation API pour Jihene-Line", "contact": {"email": "<EMAIL>"}, "version": "1.0.0"}, "servers": [{"url": "http://localhost/api/v1", "description": "Serveur API"}, {"url": "http://localhost:8000"}], "paths": {"/api/v1": {}, "/api/marques": {"get": {"tags": ["Marques"], "summary": "Liste toutes les marques", "operationId": "03787a04650457a20881200057ea409e", "responses": {"200": {"description": "Liste des marques"}}}, "post": {"tags": ["Marques"], "summary": "Crée une nouvelle marque", "operationId": "e53637c4ff9307b1212bf074b16226f2", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"nom": {"type": "string", "example": "Nike"}, "description": {"type": "string", "example": "Marque de sport"}}, "type": "object"}}}}, "responses": {"201": {"description": "<PERSON><PERSON>"}, "422": {"description": "Validation error"}}}}, "/api/marques/{id}": {"get": {"tags": ["Marques"], "summary": "Affiche une marque spécifique", "operationId": "5f11e45542cd15be491207048afb4e89", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Dé<PERSON> de la marque"}, "404": {"description": "Marque non trouvée"}}}, "put": {"tags": ["Marques"], "summary": "Met à jour une marque", "operationId": "924d4d7c9c95fb01e67c14b250edaab8", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"nom": {"type": "string", "example": "Nike"}, "description": {"type": "string", "example": "Marque de sport"}}, "type": "object"}}}}, "responses": {"200": {"description": "<PERSON><PERSON> mise à jour"}, "404": {"description": "Marque non trouvée"}, "422": {"description": "Validation error"}}}, "delete": {"tags": ["Marques"], "summary": "Supprime une marque", "operationId": "dc28b5d662995167a39c7d9ec4372b57", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"204": {"description": "<PERSON><PERSON> supprimée"}, "404": {"description": "Marque non trouvée"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "bearerFormat": "JWT", "scheme": "bearer"}}}, "tags": [{"name": "Marques", "description": "Marques"}]}