<?php

namespace App\Http\Controllers;

use App\Services\ErrorHandlingService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Stripe\Webhook;
use Stripe\Exception\SignatureVerificationException;
use App\Models\Commande;
use App\Models\Paiement;
use App\Jobs\ProcessPaymentConfirmation;
use App\Jobs\SendOrderConfirmationEmail;

class StripeWebhookController extends Controller
{
    /**
     * Handle Stripe webhook events
     */
    public function handleWebhook(Request $request): JsonResponse
    {
        $payload = $request->getContent();
        $sigHeader = $request->header('Stripe-Signature');
        $endpoint_secret = config('services.stripe.webhook_secret');

        try {
            // Verify webhook signature
            $event = Webhook::constructEvent($payload, $sigHeader, $endpoint_secret);
        } catch (SignatureVerificationException $e) {
            ErrorHandlingService::logSecurityEvent('stripe_webhook_signature_failed', [
                'error' => $e->getMessage(),
                'payload_length' => strlen($payload)
            ]);

            return response()->json(['error' => 'Invalid signature'], 400);
        } catch (\Exception $e) {
            ErrorHandlingService::logAndFormatError($e, $request);
            return response()->json(['error' => 'Webhook error'], 400);
        }

        // Log the webhook event
        Log::info('Stripe webhook received', [
            'event_type' => $event['type'],
            'event_id' => $event['id'],
            'livemode' => $event['livemode']
        ]);

        // Handle the event
        try {
            switch ($event['type']) {
                case 'payment_intent.succeeded':
                    $this->handlePaymentIntentSucceeded($event['data']['object']);
                    break;

                case 'payment_intent.payment_failed':
                    $this->handlePaymentIntentFailed($event['data']['object']);
                    break;

                case 'payment_intent.canceled':
                    $this->handlePaymentIntentCanceled($event['data']['object']);
                    break;

                case 'charge.dispute.created':
                    $this->handleChargeDisputeCreated($event['data']['object']);
                    break;

                case 'invoice.payment_succeeded':
                    $this->handleInvoicePaymentSucceeded($event['data']['object']);
                    break;

                case 'invoice.payment_failed':
                    $this->handleInvoicePaymentFailed($event['data']['object']);
                    break;

                case 'customer.subscription.created':
                    $this->handleSubscriptionCreated($event['data']['object']);
                    break;

                case 'customer.subscription.updated':
                    $this->handleSubscriptionUpdated($event['data']['object']);
                    break;

                case 'customer.subscription.deleted':
                    $this->handleSubscriptionDeleted($event['data']['object']);
                    break;

                default:
                    Log::info('Unhandled Stripe webhook event', [
                        'event_type' => $event['type']
                    ]);
            }

            return response()->json(['status' => 'success']);

        } catch (\Exception $e) {
            ErrorHandlingService::logAndFormatError($e, $request);
            return response()->json(['error' => 'Processing failed'], 500);
        }
    }

    /**
     * Handle successful payment intent
     */
    private function handlePaymentIntentSucceeded($paymentIntent): void
    {
        // Try both metadata keys for backward compatibility
        $orderId = $paymentIntent['metadata']['commande_id'] ?? $paymentIntent['metadata']['order_id'] ?? null;

        if (!$orderId) {
            Log::warning('Payment intent succeeded but no commande_id/order_id in metadata', [
                'payment_intent_id' => $paymentIntent['id'],
                'metadata' => $paymentIntent['metadata'] ?? []
            ]);
            return;
        }

        $order = Commande::find($orderId);
        if (!$order) {
            Log::error('Order not found for successful payment', [
                'order_id' => $orderId,
                'payment_intent_id' => $paymentIntent['id']
            ]);
            return;
        }

        // Check if payment is already processed
        $existingPayment = Paiement::where('transaction_id', $paymentIntent['id'])->first();

        if ($existingPayment && $existingPayment->status === 'completed') {
            Log::info('Payment already processed, skipping', [
                'payment_intent_id' => $paymentIntent['id'],
                'order_id' => $orderId
            ]);
            return;
        }

        // Update order status using proper field names
        $updateData = [
            'status' => 'confirmee', // Use 'status' instead of 'statut'
            'payment_status' => 'paid'
        ];

        // Only add fields that exist in the database
        if (\Schema::hasColumn('commandes', 'payment_intent_id')) {
            $updateData['payment_intent_id'] = $paymentIntent['id'];
        }
        if (\Schema::hasColumn('commandes', 'paid_at')) {
            $updateData['paid_at'] = now();
        }

        $order->update($updateData);

        // Update existing payment record or create new one
        if ($existingPayment) {
            $existingPayment->update([
                'status' => 'completed',
                'processed_at' => now(),
                'gateway_response' => array_merge(
                    $existingPayment->gateway_response ?? [],
                    ['webhook_data' => $paymentIntent]
                )
            ]);

            Log::info('Updated existing payment record', [
                'payment_id' => $existingPayment->id,
                'payment_intent_id' => $paymentIntent['id']
            ]);
        } else {
            // Create new payment record if none exists
            $paymentData = [
                'commande_id' => $order->id,
                'transaction_id' => $paymentIntent['id'],
                'montant' => $paymentIntent['amount'] / 100, // Convert from cents
                'methode_paiement' => 'stripe',
                'status' => 'completed',
                'processed_at' => now(),
                'gateway_response' => $paymentIntent
            ];

            // Add optional fields if they exist in the database
            if (\Schema::hasColumn('paiements', 'stripe_payment_intent_id')) {
                $paymentData['stripe_payment_intent_id'] = $paymentIntent['id'];
            }
            if (\Schema::hasColumn('paiements', 'currency')) {
                $paymentData['currency'] = $paymentIntent['currency'];
            }
            if (\Schema::hasColumn('paiements', 'stripe_currency')) {
                $paymentData['stripe_currency'] = strtoupper($paymentIntent['currency']);
            }

            Paiement::create($paymentData);

            Log::info('Created new payment record', [
                'payment_intent_id' => $paymentIntent['id']
            ]);
        }

        // Dispatch background jobs
        ProcessPaymentConfirmation::dispatch($order)->onQueue('payments');
        SendOrderConfirmationEmail::dispatch($order)->onQueue('emails');

        Log::info('Payment processed successfully', [
            'order_id' => $orderId,
            'payment_intent_id' => $paymentIntent['id'],
            'amount' => $paymentIntent['amount'] / 100,
            'order_status' => $order->status,
            'payment_status' => $order->payment_status
        ]);
    }

    /**
     * Handle failed payment intent
     */
    private function handlePaymentIntentFailed($paymentIntent): void
    {
        // Try both metadata keys for backward compatibility
        $orderId = $paymentIntent['metadata']['commande_id'] ?? $paymentIntent['metadata']['order_id'] ?? null;

        if (!$orderId) {
            Log::warning('Payment intent failed but no commande_id/order_id in metadata', [
                'payment_intent_id' => $paymentIntent['id']
            ]);
            return;
        }

        $order = Commande::find($orderId);
        if (!$order) {
            Log::error('Order not found for failed payment', [
                'order_id' => $orderId,
                'payment_intent_id' => $paymentIntent['id']
            ]);
            return;
        }

        // Update order status using proper field names
        $updateData = [
            'status' => 'echec_paiement',
            'payment_status' => 'failed'
        ];

        // Only add fields that exist in the database
        if (\Schema::hasColumn('commandes', 'payment_intent_id')) {
            $updateData['payment_intent_id'] = $paymentIntent['id'];
        }

        $order->update($updateData);

        // Update payment record if it exists
        $payment = Paiement::where('transaction_id', $paymentIntent['id'])->first();
        if ($payment) {
            $payment->update([
                'status' => 'failed',
                'gateway_response' => array_merge(
                    $payment->gateway_response ?? [],
                    ['webhook_data' => $paymentIntent]
                )
            ]);
        }

        // Log the failure
        Log::warning('Payment failed', [
            'order_id' => $orderId,
            'payment_intent_id' => $paymentIntent['id'],
            'failure_code' => $paymentIntent['last_payment_error']['code'] ?? null,
            'failure_message' => $paymentIntent['last_payment_error']['message'] ?? null
        ]);
    }

    /**
     * Handle canceled payment intent
     */
    private function handlePaymentIntentCanceled($paymentIntent): void
    {
        // Try both metadata keys for backward compatibility
        $orderId = $paymentIntent['metadata']['commande_id'] ?? $paymentIntent['metadata']['order_id'] ?? null;

        if (!$orderId) {
            Log::warning('Payment intent canceled but no commande_id/order_id in metadata', [
                'payment_intent_id' => $paymentIntent['id']
            ]);
            return;
        }

        $order = Commande::find($orderId);
        if (!$order) {
            Log::error('Order not found for canceled payment', [
                'order_id' => $orderId,
                'payment_intent_id' => $paymentIntent['id']
            ]);
            return;
        }

        // Update order status using proper field names
        $updateData = [
            'status' => 'annulee',
            'payment_status' => 'canceled'
        ];

        // Only add fields that exist in the database
        if (\Schema::hasColumn('commandes', 'payment_intent_id')) {
            $updateData['payment_intent_id'] = $paymentIntent['id'];
        }

        $order->update($updateData);

        // Update payment record if it exists
        $payment = Paiement::where('transaction_id', $paymentIntent['id'])->first();
        if ($payment) {
            $payment->update([
                'status' => 'canceled',
                'gateway_response' => array_merge(
                    $payment->gateway_response ?? [],
                    ['webhook_data' => $paymentIntent]
                )
            ]);
        }

        Log::info('Payment canceled', [
            'order_id' => $orderId,
            'payment_intent_id' => $paymentIntent['id']
        ]);
    }

    /**
     * Handle charge dispute created
     */
    private function handleChargeDisputeCreated($dispute): void
    {
        // Find the payment record
        $chargeId = $dispute['charge'];
        $payment = Paiement::where('stripe_charge_id', $chargeId)->first();

        if ($payment) {
            $payment->update([
                'status' => 'disputed',
                'dispute_id' => $dispute['id']
            ]);

            // Notify administrators
            Log::alert('Charge dispute created', [
                'dispute_id' => $dispute['id'],
                'charge_id' => $chargeId,
                'amount' => $dispute['amount'] / 100,
                'reason' => $dispute['reason']
            ]);
        }
    }

    /**
     * Handle successful invoice payment (for subscriptions)
     */
    private function handleInvoicePaymentSucceeded($invoice): void
    {
        Log::info('Invoice payment succeeded', [
            'invoice_id' => $invoice['id'],
            'customer_id' => $invoice['customer'],
            'amount_paid' => $invoice['amount_paid'] / 100
        ]);
    }

    /**
     * Handle failed invoice payment
     */
    private function handleInvoicePaymentFailed($invoice): void
    {
        Log::warning('Invoice payment failed', [
            'invoice_id' => $invoice['id'],
            'customer_id' => $invoice['customer'],
            'amount_due' => $invoice['amount_due'] / 100
        ]);
    }

    /**
     * Handle subscription created
     */
    private function handleSubscriptionCreated($subscription): void
    {
        Log::info('Subscription created', [
            'subscription_id' => $subscription['id'],
            'customer_id' => $subscription['customer'],
            'status' => $subscription['status']
        ]);
    }

    /**
     * Handle subscription updated
     */
    private function handleSubscriptionUpdated($subscription): void
    {
        Log::info('Subscription updated', [
            'subscription_id' => $subscription['id'],
            'customer_id' => $subscription['customer'],
            'status' => $subscription['status']
        ]);
    }

    /**
     * Handle subscription deleted
     */
    private function handleSubscriptionDeleted($subscription): void
    {
        Log::info('Subscription deleted', [
            'subscription_id' => $subscription['id'],
            'customer_id' => $subscription['customer']
        ]);
    }
}
