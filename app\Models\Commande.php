<?php

namespace App\Models;

use App\Enums\CommandeStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Commande extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'client_id',
        'numero_commande',
        'status',
        'payment_status',
        'total_commande',
        'shipping_address',
        'billing_address',
        'shipping_street',
        'shipping_city',
        'shipping_postal_code',
        'telephone_commande',
        'email_commande',
        'notes',
        'methode_paiement',
        'date_commande',
    ];

    protected $casts = [
        'status' => CommandeStatus::class,
        'total_commande' => 'decimal:2',
        'date_commande' => 'datetime',
        'shipping_address' => 'json',
        'billing_address' => 'json',
    ];

    public function updateStatus(CommandeStatus $newStatus): void
    {
        $this->status = $newStatus;
        $this->save();
    }

    public function canChangeStatusTo(CommandeStatus $newStatus): bool
    {
        return match ($this->status) {
            CommandeStatus::EN_ATTENTE => in_array($newStatus, [
                CommandeStatus::CONFIRMEE,
                CommandeStatus::ANNULEE
            ]),
            CommandeStatus::CONFIRMEE => in_array($newStatus, [
                CommandeStatus::EN_PREPARATION,
                CommandeStatus::ANNULEE
            ]),
            CommandeStatus::EN_PREPARATION => in_array($newStatus, [
                CommandeStatus::EXPEDIEE,
                CommandeStatus::ANNULEE
            ]),
            CommandeStatus::EXPEDIEE => in_array($newStatus, [
                CommandeStatus::LIVREE,
                CommandeStatus::ANNULEE
            ]),
            CommandeStatus::LIVREE => in_array($newStatus, [
                CommandeStatus::REMBOURSEE
            ]),
            CommandeStatus::ANNULEE, CommandeStatus::REMBOURSEE => false,
        };
    }

    public function produits(): BelongsToMany
    {
        return $this->belongsToMany(Produit::class, 'commande_produit', 'commande_id', 'produit_id')
            ->withPivot('quantite', 'prix_unitaire')
            ->withTimestamps();
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class, 'client_id');
    }

    public function paiement(): HasOne
    {
        return $this->hasOne(Paiement::class);
    }

    /**
     * Calculate the total price of the order
     *
     * @return float
     */
    public function calculateTotal(): float
    {
        // Calculate the subtotal from all products
        return $this->produits->sum(function ($produit) {
            return $produit->pivot->quantite * $produit->pivot->prix_unitaire;
        });
    }

    /**
     * Recalculate and update the order total
     */
    public function recalculateTotal(): void
    {
        $this->total_commande = $this->calculateTotal();
        $this->save();
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        // Set default status for new orders
        static::creating(function ($commande) {
            if (!$commande->status) {
                $commande->status = CommandeStatus::EN_ATTENTE;
            }
            // Set date_commande if not set
            if (!$commande->date_commande) {
                $commande->date_commande = now();
            }
        });

        // After saving products, recalculate total if products were attached
        static::saved(function ($commande) {
            // Only recalculate if we have products and total is 0 or not set
            if ($commande->produits()->exists() && (!$commande->total_commande || $commande->total_commande == 0)) {
                $newTotal = $commande->calculateTotal();
                if ($newTotal != $commande->total_commande) {
                    // Use updateQuietly to avoid infinite loop
                    $commande->updateQuietly(['total_commande' => $newTotal]);
                }
            }
        });
    }
}
