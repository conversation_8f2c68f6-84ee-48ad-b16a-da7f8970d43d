<?php

namespace App\Http\Controllers;

use App\Models\GroupeAttribut;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class GroupeAttributController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        try {
            $groupes = GroupeAttribut::with('attributs')->get();
            return response()->json($groupes);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erreur lors de la récupération des groupes d\'attributs',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'nom' => 'required|string|max:255',
                'description' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'error' => 'Données invalides',
                    'message' => $validator->errors()
                ], 422);
            }

            $groupe = GroupeAttribut::create($request->all());

            return response()->json($groupe, 201);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erreur lors de la création du groupe d\'attributs',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
            $groupe = GroupeAttribut::with('attributs')->findOrFail($id);
            return response()->json($groupe);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Groupe d\'attributs non trouvé',
                'message' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'nom' => 'sometimes|required|string|max:255',
                'description' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'error' => 'Données invalides',
                    'message' => $validator->errors()
                ], 422);
            }

            $groupe = GroupeAttribut::findOrFail($id);
            $groupe->update($request->all());

            return response()->json($groupe);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erreur lors de la mise à jour du groupe d\'attributs',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            DB::beginTransaction();

            $groupe = GroupeAttribut::findOrFail($id);

            // Vérifier si le groupe a des attributs associés
            if ($groupe->attributs()->count() > 0) {
                return response()->json([
                    'error' => 'Impossible de supprimer ce groupe',
                    'message' => 'Ce groupe contient des attributs. Veuillez d\'abord supprimer ou réaffecter ces attributs.'
                ], 422);
            }

            $groupe->delete();

            DB::commit();

            return response()->json([
                'message' => 'Groupe d\'attributs supprimé avec succès'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'error' => 'Erreur lors de la suppression du groupe d\'attributs',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
