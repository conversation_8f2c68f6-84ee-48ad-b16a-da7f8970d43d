<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class sous_sousCategorie extends Model
{
    protected $fillable = ['nom_sous_sous_categorie', 'description_sous_sous_categorie', 'sous_categorie_id'];

    public function sousCategorie()
    {
        return $this->belongsTo(SousCategorie::class);
    }

    public function produits()
    {
        return $this->hasMany(Produit::class);
    }

    /**
     * Get all images for this sub-subcategory
     */
    public function images(): MorphMany
    {
        return $this->morphMany(Image::class, 'imageable')->orderBy('order');
    }

    /**
     * Get the primary image for this sub-subcategory
     */
    public function getPrimaryImageAttribute()
    {
        return $this->images()->where('is_primary', true)->first() ?? $this->images()->first();
    }

    /**
     * Get the primary image URL for this sub-subcategory
     */
    public function getPrimaryImageUrlAttribute()
    {
        if ($this->primaryImage) {
            return $this->primaryImage->url;
        }

        return null;
    }
}
