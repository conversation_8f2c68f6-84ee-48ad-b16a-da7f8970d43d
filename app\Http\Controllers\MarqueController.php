<?php

namespace App\Http\Controllers;

use App\Models\Marque;
use Illuminate\Http\Request;
use OpenApi\Annotations as OA;


class MarqueController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/marques",
     *     tags={"Marques"},
     *     summary="Liste toutes les marques",
     *     @OA\Response(
     *         response=200,
     *         description="Liste des marques"
     *
     *     )
     * )
     */
    public function index()
    {
        $marques = Marque::all();
        return response()->json($marques);
    }

    /**
     * @OA\Post(
     *     path="/api/marques",
     *     tags={"Marques"},
     *     summary="Crée une nouvelle marque",
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="nom", type="string", example="Nike"),
     *             @OA\Property(property="description", type="string", example="Marque de sport")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Marque créée"
     *
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error"
     *     )
     * )
     */
    public function store(Request $request)
    {
        try {
            $marque = new Marque([
                'nom_marque' => $request->input('nom_marque'),
                'description_marque' => $request->input('description_marque'),
                'logo_marque' => $request->input('logo_marque')
            ]);
            $marque->save();
            return response()->json($marque, 201);
        } catch (\Exception $e) {
            return response()->json(["error" => "probleme d'insertion {$e->getMessage()}"]);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/marques/{id}",
     *     tags={"Marques"},
     *     summary="Affiche une marque spécifique",
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Détails de la marque"
     *
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Marque non trouvée"
     *     )
     * )
     */
    public function show($id)
    {
        try {
            $marque = Marque::findOrFail($id);
            return response()->json($marque);
        } catch (\Exception $e) {
            return response()->json(["error" => "probleme de récupération des données {$e->getMessage()}"]);
        }
    }

    /**
     * @OA\Put(
     *     path="/api/marques/{id}",
     *     tags={"Marques"},
     *     summary="Met à jour une marque",
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="nom", type="string", example="Nike"),
     *             @OA\Property(property="description", type="string", example="Marque de sport")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Marque mise à jour"
     *
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Marque non trouvée"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error"
     *     )
     * )
     */
    public function update(Request $request, $id)
    {
        try {
            $marque = Marque::findorFail($id);
            $marque->update($request->all());
            return response()->json($marque);
        } catch (\Exception $e) {
            return response()->json(["error" => "probleme de modification {$e->getMessage()}"]);
        }
    }

    /**
     * @OA\Delete(
     *     path="/api/marques/{id}",
     *     tags={"Marques"},
     *     summary="Supprime une marque",
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=204,
     *         description="Marque supprimée"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Marque non trouvée"
     *     )
     * )
     */
    public function destroy($id)
    {
        try {
            $marque = Marque::findOrFail($id);
            $marque->delete();
            return response()->json(["message" => "Marque supprimée avec succès"], 200);
        } catch (\Exception $e) {
            return response()->json(["error" => "probleme de suppression de Marque {$e->getMessage()}"]);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/marques/{id}/produits",
     *     tags={"Marques"},
     *     summary="Récupère tous les produits d'une marque spécifique",
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Liste des produits de la marque"
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Marque non trouvée"
     *     )
     * )
     */
    public function getProduits($id)
    {
        try {
            $marque = Marque::findOrFail($id);
            $produits = $marque->produits;

            if ($produits->isEmpty()) {
                return response()->json([
                    "message" => "Aucun produit trouvé pour cette marque"
                ], 404);
            }

            return response()->json($produits);
        } catch (\Exception $e) {
            return response()->json([
                "error" => "Problème de récupération des produits",
                "message" => $e->getMessage()
            ], 500);
        }
    }
}
