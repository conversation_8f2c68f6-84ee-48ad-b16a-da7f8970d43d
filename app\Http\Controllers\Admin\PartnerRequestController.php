<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Partenaire;
use App\Models\PartnerRequest;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class PartnerRequestController extends Controller
{
    /**
     * Display a listing of partner requests
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        if (!$user || !$user->hasAnyRole(['admin', 'staff'])) {
            return response()->json([
                'status' => 'error',
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $status = $request->query('status', 'pending');
        $perPage = $request->query('per_page', 15);

        $query = PartnerRequest::with('user');

        if ($status !== 'all') {
            $query->where('status', $status);
        }

        $requests = $query->latest()->paginate($perPage);

        return response()->json([
            'status' => 'success',
            'data' => $requests
        ]);
    }

    /**
     * Display the specified partner request
     */
    public function show(string $id)
    {
        $user = Auth::user();

        if (!$user || !$user->hasAnyRole(['admin', 'staff'])) {
            return response()->json([
                'status' => 'error',
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $partnerRequest = PartnerRequest::with('user')->find($id);

        if (!$partnerRequest) {
            return response()->json([
                'status' => 'error',
                'message' => 'Demande de partenariat non trouvée'
            ], 404);
        }

        return response()->json([
            'status' => 'success',
            'data' => $partnerRequest
        ]);
    }

    /**
     * Approve a partner request
     */
    public function approve(Request $request, string $id)
    {
        $user = Auth::user();

        if (!$user || !$user->hasAnyRole(['admin'])) {
            return response()->json([
                'status' => 'error',
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'remise' => 'required|numeric|min:0|max:100',
            'admin_notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $partnerRequest = PartnerRequest::with('user')->find($id);

        if (!$partnerRequest) {
            return response()->json([
                'status' => 'error',
                'message' => 'Demande de partenariat non trouvée'
            ], 404);
        }

        if ($partnerRequest->status !== 'pending') {
            return response()->json([
                'status' => 'error',
                'message' => 'Cette demande a déjà été traitée'
            ], 400);
        }

        DB::beginTransaction();

        try {
            // Update the request status
            $partnerRequest->status = 'approved';
            $partnerRequest->admin_notes = $request->admin_notes;
            $partnerRequest->processed_at = now();
            $partnerRequest->processed_by = $user->id;
            $partnerRequest->save();

            // Check if the request is associated with a user
            if ($partnerRequest->user_id) {
                // Create or update partner record
                $partner = Partenaire::firstOrNew(['user_id' => $partnerRequest->user_id]);
                $partner->remise = $request->remise;
                $partner->description = $partnerRequest->motivation;
                $partner->statut = 'actif';
                $partner->save();

                // Update user roles
                $requestUser = $partnerRequest->user;
                $roles = $requestUser->roles ?? [];

                if (!in_array('partenaire', $roles)) {
                    $roles[] = 'partenaire';
                    $requestUser->roles = $roles;
                    $requestUser->save();

                    // Ensure role consistency
                    User::ensureRoleTypeConsistency($requestUser);
                }
            } else {
                // For requests without a user, we need to create a user first
                // Check if a user with this email already exists
                $existingUser = User::where('email', $partnerRequest->email)->first();

                if ($existingUser) {
                    // Associate the request with the existing user
                    $partnerRequest->user_id = $existingUser->id;
                    $partnerRequest->save();

                    // Create partner record
                    $partner = Partenaire::firstOrNew(['user_id' => $existingUser->id]);
                    $partner->remise = $request->remise;
                    $partner->description = $partnerRequest->motivation;
                    $partner->statut = 'actif';
                    $partner->save();

                    // Update user roles
                    $roles = $existingUser->roles ?? [];

                    if (!in_array('partenaire', $roles)) {
                        $roles[] = 'partenaire';
                        $existingUser->roles = $roles;
                        $existingUser->save();

                        // Ensure role consistency
                        User::ensureRoleTypeConsistency($existingUser);
                    }
                } else {
                    // Create a new user
                    $newUser = new User();
                    $newUser->name = $partnerRequest->name;
                    $newUser->email = $partnerRequest->email;
                    $newUser->roles = ['partenaire'];
                    $newUser->save();

                    // Associate the request with the new user
                    $partnerRequest->user_id = $newUser->id;
                    $partnerRequest->save();

                    // Create partner record
                    $partner = new Partenaire();
                    $partner->user_id = $newUser->id;
                    $partner->remise = $request->remise;
                    $partner->description = $partnerRequest->motivation;
                    $partner->statut = 'actif';
                    $partner->save();
                }
            }

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Demande de partenariat approuvée avec succès',
                'data' => $partnerRequest
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'status' => 'error',
                'message' => 'Une erreur est survenue lors de l\'approbation de la demande',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reject a partner request
     */
    public function reject(Request $request, string $id)
    {
        $user = Auth::user();

        if (!$user || !$user->hasAnyRole(['admin', 'staff'])) {
            return response()->json([
                'status' => 'error',
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'admin_notes' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $partnerRequest = PartnerRequest::find($id);

        if (!$partnerRequest) {
            return response()->json([
                'status' => 'error',
                'message' => 'Demande de partenariat non trouvée'
            ], 404);
        }

        if ($partnerRequest->status !== 'pending') {
            return response()->json([
                'status' => 'error',
                'message' => 'Cette demande a déjà été traitée'
            ], 400);
        }

        // Update the request status
        $partnerRequest->status = 'rejected';
        $partnerRequest->admin_notes = $request->admin_notes;
        $partnerRequest->processed_at = now();
        $partnerRequest->processed_by = $user->id;
        $partnerRequest->save();

        return response()->json([
            'status' => 'success',
            'message' => 'Demande de partenariat rejetée',
            'data' => $partnerRequest
        ]);
    }
}
