<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('partenaires', function (Blueprint $table) {
            // Drop existing columns if they exist
            if (Schema::hasColumn('partenaires', 'nom_partenaire')) {
                $table->dropColumn('nom_partenaire');
            }
            
            if (Schema::hasColumn('partenaires', 'remise_partenaire')) {
                $table->dropColumn('remise_partenaire');
            }
            
            // Add new columns
            if (!Schema::hasColumn('partenaires', 'user_id')) {
                $table->foreignId('user_id')->constrained()->onDelete('cascade');
            }
            
            if (!Schema::hasColumn('partenaires', 'remise')) {
                $table->decimal('remise', 5, 2)->default(0); // Percentage discount (e.g., 10.50 for 10.5%)
            }
            
            if (!Schema::hasColumn('partenaires', 'description')) {
                $table->string('description')->nullable();
            }
            
            if (!Schema::hasColumn('partenaires', 'statut')) {
                $table->enum('statut', ['actif', 'inactif'])->default('actif');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('partenaires', function (Blueprint $table) {
            // Restore original columns
            $table->string('nom_partenaire')->nullable();
            $table->string('remise_partenaire')->nullable();
            
            // Drop new columns
            $table->dropColumn(['remise', 'description', 'statut']);
            
            // Don't drop user_id as it might be used by other parts of the application
        });
    }
};
