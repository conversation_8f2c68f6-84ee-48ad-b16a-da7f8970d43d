<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\File;


Route::get('/', function () {
    return view('welcome');
});

// Route for Scribe documentation with external_laravel type
Route::view('/docs', 'scribe.index')->name('scribe.docs');

Route::get('/docs.postman', function () {
    $path = storage_path('app/scribe/collection.json');
    if (File::exists($path)) {
        return Response::make(
            File::get($path),
            200,
            ['Content-Type' => 'application/json']
        );
    }

    // If the file doesn't exist, return an empty Postman collection
    $emptyCollection = [
        'info' => [
            'name' => config('app.name') . ' API',
            'description' => 'API Documentation',
            'schema' => 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json'
        ],
        'item' => []
    ];

    return response()->json($emptyCollection);
})->name('scribe.postman');

Route::get('/docs.openapi', function () {
    $path = storage_path('app/scribe/openapi.yaml');
    if (File::exists($path)) {
        return Response::make(
            File::get($path),
            200,
            ['Content-Type' => 'application/yaml']
        );
    }

    // If the file doesn't exist, return a minimal OpenAPI spec
    $minimalOpenAPI = <<<YAML
openapi: 3.0.1
info:
  title: Laravel API
  description: API Documentation
  version: 1.0.0
paths: {}
components:
  schemas: {}
YAML;

    return response($minimalOpenAPI, 200, [
        'Content-Type' => 'application/yaml'
    ]);
})->name('scribe.openapi');
