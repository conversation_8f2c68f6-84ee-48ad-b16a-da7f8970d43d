<?php

namespace App\Http\Controllers;

use App\Models\Attribut;
use Illuminate\Http\Request;
use App\Models\Produit;
use App\Models\Promotion;
use App\Models\ProduitValeur;
use App\Services\SimpleCacheService;
use App\Services\ProductService;
use App\Http\Requests\ProductFilterRequest;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use App\Services\ValidationService;

class ProduitController extends Controller
{
    public function __construct(
        private SimpleCacheService $cacheService,
        private ProductService $productService,
        private ValidationService $validationService
    ) {
    }

    /**
     * Afficher la liste des produits avec filtres et pagination
     */
    public function index(ProductFilterRequest $request)
    {
        try {
            $filters = $request->getValidatedInput();

            Log::info('Product listing requested', [
                'filters' => $filters,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            $products = $this->productService->getProducts($filters);

            return response()->json([
                'success' => true,
                'data' => $products->items(),
                'pagination' => [
                    'current_page' => $products->currentPage(),
                    'last_page' => $products->lastPage(),
                    'per_page' => $products->perPage(),
                    'total' => $products->total(),
                    'has_more_pages' => $products->hasMorePages(),
                ],
                'filters_applied' => $filters,
                'cache_info' => [
                    'served_from_cache' => false, // Will be set by service if cached
                    'ttl' => 3600
                ]
            ], 200);

        } catch (\Exception $e) {
            Log::error('Error in product listing', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error retrieving products',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }
    /**
     * Get a single product with complete details
     */
    public function show(string $id)
    {
        try {
            $product = $this->productService->getProductById((int) $id);

            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => 'Product not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $product
            ], 200);

        } catch (\Exception $e) {
            Log::error('Error retrieving product details', [
                'product_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error retrieving product details'
            ], 500);
        }
    }

    /**
     * Get related products for a specific product
     */
    public function getRelated(int $id, Request $request)
    {
        try {
            $product = Produit::find($id);

            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => 'Product not found'
                ], 404);
            }

            $limit = min($request->input('limit', 8), 20);

            // Ensure we have a proper Produit instance
            /** @var Produit $product */
            $relatedProducts = $this->productService->getRelatedProducts($product, $limit);

            return response()->json([
                'success' => true,
                'data' => $relatedProducts->toArray(),
                'count' => $relatedProducts->count()
            ], 200);

        } catch (\Exception $e) {
            Log::error('Error retrieving related products', [
                'product_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error retrieving related products'
            ], 500);
        }
    }

    /**
     * Get featured products
     */
    public function getFeatured(Request $request)
    {
        try {
            $limit = min($request->input('limit', 12), 50);
            $featured = $this->productService->getFeaturedProducts($limit);

            return response()->json([
                'success' => true,
                'data' => $featured,
                'count' => $featured->count()
            ], 200);

        } catch (\Exception $e) {
            Log::error('Error retrieving featured products', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error retrieving featured products'
            ], 500);
        }
    }

    /**
     * Search products with advanced filtering
     */
    public function search(Request $request)
    {
        try {
            $query = $request->input('q', '');

            if (strlen($query) < 2) {
                return response()->json([
                    'success' => false,
                    'message' => 'Search query must be at least 2 characters long'
                ], 400);
            }

            $filters = $request->only(['marque_id', 'prix_min', 'prix_max', 'categorie_id']);
            $limit = min($request->input('limit', 50), 100);

            $results = $this->productService->searchProducts($query, $filters, $limit);

            return response()->json([
                'success' => true,
                'data' => $results,
                'query' => $query,
                'filters' => $filters,
                'count' => $results->count()
            ], 200);

        } catch (\Exception $e) {
            Log::error('Error in product search', [
                'query' => $request->input('q'),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error searching products'
            ], 500);
        }
    }

    /**
     * Filter products by category (modernized)
     */
    public function filterByCategory(Request $request)
    {
        try {
            // Validate input
            $validated = $this->validationService->validateProductFilter($request->all());

            // Use ProductService for filtering
            $products = $this->productService->getFilteredProducts($validated);

            return response()->json([
                'status' => 'success',
                'data' => $products,
                'message' => 'Products filtered successfully'
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            \Log::error('Error filtering products by category: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Show the form for creating a new resource.
     */

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            // Validate input using ValidationService
            $rules = [
                "nom_produit" => "required|string|max:255",
                "description_produit" => "nullable|string|max:5000",
                "prix_produit" => "required|numeric|min:0|max:999999",
                "image_produit" => "nullable|url|max:2000",
                "quantite_produit" => "required|integer|min:0|max:99999",
                "marque_id" => "required|exists:marques,id",
                "sous_sous_categorie_id" => "required|exists:sous_sous_categories,id"
            ];

            $validatedData = $this->validationService->validateWithRules($request->all(), $rules);

            // Sanitize input data
            $sanitizedData = $this->validationService->sanitizeInput($validatedData);

            // Create product using ProductService
            $produit = $this->productService->createProduct($sanitizedData);

            // ProductService already handles cache invalidation, but ensure it's comprehensive
            $this->productService->invalidateProductCache($produit->id);

            return response()->json([
                'status' => 'success',
                'data' => $produit,
                'message' => 'Product created successfully'
            ], 201);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            \Log::error('Error creating product: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to create product'
            ], 500);
        }
    }


    /**
     * Display the specified resource.
     */
    public function showLegacy(string $id)
    {
        try {
            $produit = Produit::findOrFail($id);
            return response()->json($produit);
        } catch (\Exception $e) {
            return response()->json(["error" => "probleme de récupération des données {$e->getMessage()}"]);
        }
    }



    /**
     * Récupérer les attributs d'un produit (nouveau système)
     *
     * @param string $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAttributs(string $id)
    {
        try {
            $produit = Produit::findOrFail($id);
            $valeurs = ProduitValeur::with('attribut.groupe')
                ->where('produit_id', $id)
                ->get()
                ->map(function ($valeur) {
                    // Get the correct value based on attribute type
                    $attributeValue = null;
                    switch ($valeur->attribut->type_valeur) {
                        case 'texte':
                            $attributeValue = $valeur->valeur_texte;
                            break;
                        case 'nombre':
                            $attributeValue = $valeur->valeur_nombre;
                            break;
                        case 'booleen':
                            $attributeValue = $valeur->valeur_booleen;
                            break;
                        case 'date':
                            $attributeValue = $valeur->valeur_date;
                            break;
                        default:
                            $attributeValue = $valeur->valeur; // fallback to generic accessor
                    }

                    return [
                        'id' => $valeur->id,
                        'attribut_id' => $valeur->attribut_id,
                        'attribut' => [
                            'id' => $valeur->attribut->id,
                            'nom' => $valeur->attribut->nom,
                            'description' => $valeur->attribut->description,
                            'type_valeur' => $valeur->attribut->type_valeur,
                            'groupe' => $valeur->attribut->groupe ? [
                                'id' => $valeur->attribut->groupe->id,
                                'nom' => $valeur->attribut->groupe->nom
                            ] : null
                        ],
                        'valeur' => $attributeValue,
                        // Include specific value fields for frontend compatibility
                        'valeur_texte' => $valeur->valeur_texte,
                        'valeur_nombre' => $valeur->valeur_nombre,
                        'valeur_booleen' => $valeur->valeur_booleen,
                        'valeur_date' => $valeur->valeur_date
                    ];
                });

            Log::info('Product attributes retrieved', [
                'product_id' => $id,
                'attributes_count' => $valeurs->count()
            ]);

            return response()->json([
                'success' => true,
                'data' => $valeurs
            ]);
        } catch (\Exception $e) {
            Log::error('Error retrieving product attributes', [
                'product_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Erreur lors de la récupération des attributs du produit',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Définir les attributs d'un produit
     *
     * @param Request $request
     * @param string $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function setAttributs(Request $request, string $id)
    {
        try {
            DB::beginTransaction();

            $produit = Produit::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'attributs' => 'required|array',
                'attributs.*.attribut_id' => 'required|exists:attributs,id',
                'attributs.*.valeur' => 'required'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'error' => 'Données invalides',
                    'message' => $validator->errors()
                ], 422);
            }

            foreach ($request->input('attributs') as $attributData) {
                $attributId = $attributData['attribut_id'];
                $valeur = $attributData['valeur'];

                $attribut = Attribut::findOrFail($attributId);
                $colonne = 'valeur_' . $attribut->type_valeur;

                ProduitValeur::updateOrCreate(
                    [
                        'produit_id' => $produit->id,
                        'attribut_id' => $attributId
                    ],
                    [
                        $colonne => $valeur
                    ]
                );
            }

            DB::commit();

            return response()->json([
                'message' => 'Attributs du produit mis à jour avec succès',
                'produit' => $produit->load('valeurs.attribut')
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'error' => 'Erreur lors de la mise à jour des attributs du produit',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mettre à jour un attribut spécifique d'un produit
     *
     * @param Request $request
     * @param string $id ID du produit
     * @param string $attributId ID de l'attribut
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateAttribut(Request $request, string $id, string $attributId)
    {
        try {
            DB::beginTransaction();

            $produit = Produit::findOrFail($id);
            $attribut = Attribut::findOrFail($attributId);

            $validator = Validator::make($request->all(), [
                'valeur' => 'required'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'error' => 'Données invalides',
                    'message' => $validator->errors()
                ], 422);
            }

            $valeur = $request->input('valeur');
            $colonne = 'valeur_' . $attribut->type_valeur;

            // Vérifier si l'attribut existe déjà pour ce produit
            $produitValeur = ProduitValeur::where('produit_id', $produit->id)
                ->where('attribut_id', $attributId)
                ->first();

            if (!$produitValeur) {
                return response()->json([
                    'error' => 'Attribut non trouvé',
                    'message' => "Cet attribut n'est pas associé à ce produit"
                ], 404);
            }

            // Mettre à jour la valeur
            $produitValeur->$colonne = $valeur;
            $produitValeur->save();

            DB::commit();

            return response()->json([
                'message' => 'Attribut du produit mis à jour avec succès',
                'attribut' => [
                    'id' => $produitValeur->id,
                    'attribut_id' => $attribut->id,
                    'attribut' => [
                        'id' => $attribut->id,
                        'nom' => $attribut->nom,
                        'description' => $attribut->description,
                        'type_valeur' => $attribut->type_valeur
                    ],
                    'valeur' => $produitValeur->valeur
                ]
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'error' => 'Erreur lors de la mise à jour de l\'attribut du produit',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Supprimer un attribut d'un produit
     *
     * @param string $id ID du produit
     * @param string $attributId ID de l'attribut
     * @return \Illuminate\Http\JsonResponse
     */
    public function removeAttribut(string $id, string $attributId)
    {
        try {
            DB::beginTransaction();

            $produit = Produit::findOrFail($id);
            $attribut = Attribut::findOrFail($attributId);

            // Vérifier si l'attribut existe pour ce produit
            $produitValeur = ProduitValeur::where('produit_id', $produit->id)
                ->where('attribut_id', $attributId)
                ->first();

            if (!$produitValeur) {
                return response()->json([
                    'error' => 'Attribut non trouvé',
                    'message' => "Cet attribut n'est pas associé à ce produit"
                ], 404);
            }

            // Supprimer la valeur
            $produitValeur->delete();

            DB::commit();

            return response()->json([
                'message' => 'Attribut du produit supprimé avec succès'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'error' => 'Erreur lors de la suppression de l\'attribut du produit',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Filtrer les produits par attributs
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function filterByAttributes(Request $request)
    {
        try {
            // Initialiser la requête
            $query = Produit::with(['marque', 'sousSousCategorie.sousCategorie', 'valeurs.attribut']);

            // Filtrage par marque
            if ($request->has('marque_id')) {
                $query->where('marque_id', $request->input('marque_id'));
            }

            // Filtrage par catégorie
            if ($request->has('sous_categorie_id')) {
                $query->whereHas('sousSousCategorie', function ($q) use ($request) {
                    $q->where('sous_categorie_id', $request->input('sous_categorie_id'));
                });
            }

            // Filtrage par sous-catégorie
            if ($request->has('sous_sous_categorie_id')) {
                $query->where('sous_sous_categorie_id', $request->input('sous_sous_categorie_id'));
            }

            // Filtrage par attributs
            if ($request->has('attributs') && is_array($request->input('attributs'))) {
                foreach ($request->input('attributs') as $attributId => $valeur) {
                    if (empty($valeur))
                        continue;

                    // Récupérer l'attribut pour connaître son type
                    $attribut = Attribut::find($attributId);
                    if (!$attribut)
                        continue;

                    $colonne = 'valeur_' . $attribut->type_valeur;

                    $query->whereHas('valeurs', function ($q) use ($attributId, $valeur, $colonne, $attribut) {
                        $q->where('attribut_id', $attributId);

                        // Traitement différent selon le type d'attribut et la valeur
                        if ($attribut->type_valeur === 'texte') {
                            // Si c'est un tableau de valeurs textuelles
                            if (is_array($valeur)) {
                                $q->whereIn($colonne, $valeur);
                            } else {
                                $q->where($colonne, $valeur);
                            }
                        } elseif ($attribut->type_valeur === 'nombre') {
                            // Si c'est une plage de valeurs numériques
                            if (is_array($valeur) && isset($valeur['min']) && isset($valeur['max'])) {
                                $q->where($colonne, '>=', $valeur['min'])
                                    ->where($colonne, '<=', $valeur['max']);
                            } elseif (is_numeric($valeur)) {
                                $q->where($colonne, $valeur);
                            }
                        } elseif ($attribut->type_valeur === 'booleen') {
                            $q->where($colonne, (bool) $valeur);
                        }
                    });
                }
            }

            // Filtrage par prix
            if ($request->has('prix_min')) {
                $query->where('prix_produit', '>=', $request->input('prix_min'));
            }

            if ($request->has('prix_max')) {
                $query->where('prix_produit', '<=', $request->input('prix_max'));
            }

            // Filtrage par promotion
            if ($request->has('en_promotion') && $request->boolean('en_promotion')) {
                $now = now();
                $query->whereHas('promotions', function ($q) use ($now) {
                    $q->where('promotions.statut', 'active')
                        ->where(function ($q) use ($now) {
                            $q->whereNull('promotions.date_debut')
                                ->orWhere('promotions.date_debut', '<=', $now);
                        })
                        ->where(function ($q) use ($now) {
                            $q->whereNull('promotions.date_fin')
                                ->orWhere('promotions.date_fin', '>=', $now);
                        })
                        ->where(function ($q) use ($now) {
                            $q->whereNull('produit_promotion.date_debut')
                                ->orWhere('produit_promotion.date_debut', '<=', $now);
                        })
                        ->where(function ($q) use ($now) {
                            $q->whereNull('produit_promotion.date_fin')
                                ->orWhere('produit_promotion.date_fin', '>=', $now);
                        });
                });
            }

            // Tri
            $sortField = $request->input('sort_by', 'nom_produit');
            $sortDirection = $request->input('sort_direction', 'asc');
            $query->orderBy($sortField, $sortDirection);

            // Pagination
            $perPage = $request->input('per_page', 12);
            $produits = $query->paginate($perPage);

            return response()->json($produits);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erreur lors du filtrage des produits',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function produitsPaginate()
    {
        try {
            $perPage = request()->input('pageSize', 10); // Valeur par défaut : 10 produits par page

            // Récupération des produits avec pagination
            $produits = Produit::paginate($perPage);

            // Retourne le résultat sous format JSON
            return response()->json([
                'products' => $produits->items(), // Liste des produits pour la page actuelle
                'totalPages' => $produits->lastPage(), // Nombre total de pages
                'currentPage' => $produits->currentPage(), // Page actuelle
                'totalItems' => $produits->total(), // Nombre total d'articles
            ]);

        } catch (\Exception $e) {
            return response()->json(["error" => "Sélection impossible : {$e->getMessage()}"], 500);
        }
    }


    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            DB::beginTransaction();

            // Log incoming request for debugging
            Log::info('Product update request', [
                'product_id' => $id,
                'request_data' => $request->all()
            ]);

            // Validate input
            $rules = [
                "nom_produit" => "sometimes|required|string|max:255",
                "description_produit" => "nullable|string|max:5000",
                "prix_produit" => "sometimes|required|numeric|min:0|max:999999",
                "image_produit" => "nullable|url|max:2000",
                "quantite_produit" => "sometimes|required|integer|min:0|max:99999",
                "marque_id" => "sometimes|required|exists:marques,id",
                "sous_sous_categorie_id" => "sometimes|required|exists:sous_sous_categories,id",
                // Add validation for attributes
                "attributs" => "sometimes|array",
                "attributs.*.attribut_id" => "required_with:attributs|exists:attributs,id",
                "attributs.*.valeur" => "required_with:attributs"
            ];

            $validatedData = $this->validationService->validateWithRules($request->all(), $rules);

            // Sanitize input data (excluding attributs for separate processing)
            $productData = array_diff_key($validatedData, ['attributs' => '']);
            $sanitizedData = $this->validationService->sanitizeInput($productData);

            // Find and update product
            $produit = Produit::findOrFail($id);
            $produit->update($sanitizedData);

            // Process attributes if provided
            if (isset($validatedData['attributs']) && is_array($validatedData['attributs'])) {
                Log::info('Processing attributes for product', [
                    'product_id' => $id,
                    'attributes_count' => count($validatedData['attributs'])
                ]);

                foreach ($validatedData['attributs'] as $attributData) {
                    $attributId = $attributData['attribut_id'];
                    $valeur = $attributData['valeur'];

                    // Get attribute to determine the correct column
                    $attribut = Attribut::findOrFail($attributId);
                    $colonne = 'valeur_' . $attribut->type_valeur;

                    // Update or create the attribute value
                    ProduitValeur::updateOrCreate(
                        [
                            'produit_id' => $produit->id,
                            'attribut_id' => $attributId
                        ],
                        [
                            $colonne => $valeur
                        ]
                    );

                    Log::info('Attribute updated', [
                        'product_id' => $id,
                        'attribute_id' => $attributId,
                        'column' => $colonne,
                        'value' => $valeur
                    ]);
                }
            }

            // Load fresh relationships including attributes
            $produit->load(['marque', 'sousSousCategorie', 'valeurs.attribut']);

            DB::commit();

            // Comprehensive cache invalidation
            $this->productService->invalidateProductCache($produit->id);

            return response()->json([
                'status' => 'success',
                'data' => $produit,
                'message' => 'Product updated successfully'
            ]);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            DB::rollBack();
            return response()->json([
                'status' => 'error',
                'message' => 'Product not found'
            ], 404);
        } catch (\Illuminate\Validation\ValidationException $e) {
            DB::rollBack();
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating product', [
                'product_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update product'
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            DB::beginTransaction();

            $produit = Produit::findOrFail($id);

            // Store product name for logging
            $productName = $produit->nom_produit;

            Log::info('Deleting product', [
                'product_id' => $id,
                'product_name' => $productName
            ]);

            // Delete related data first (if needed)
            $produit->valeurs()->delete(); // Delete attribute values
            $produit->images()->delete(); // Delete images
            $produit->variantes()->delete(); // Delete variants

            // Delete the product
            $produit->delete();

            DB::commit();

            // Comprehensive cache invalidation using ProductService
            $this->productService->invalidateProductCache((int) $id);

            Log::info('Product deleted successfully', [
                'product_id' => $id,
                'product_name' => $productName
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Product deleted successfully',
                'deleted_id' => $id
            ]);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            DB::rollBack();
            return response()->json([
                'status' => 'error',
                'message' => 'Product not found'
            ], 404);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error deleting product', [
                'product_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to delete product'
            ], 500);
        }
    }

    /**
     * Associer une promotion à un produit
     *
     * @param Request $request
     * @param int $produit
     * @return \Illuminate\Http\JsonResponse
     */
    public function attachPromotion(Request $request, $produit)
    {
        try {
            $validator = Validator::make($request->all(), [
                'promotion_id' => 'required|exists:promotions,id',
                'date_debut' => 'nullable|date',
                'date_fin' => 'nullable|date|after_or_equal:date_debut',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'error' => 'Données invalides',
                    'message' => $validator->errors()
                ], 422);
            }

            $produit = Produit::findOrFail($produit);
            $promotion = Promotion::findOrFail($request->input('promotion_id'));

            // Vérifier si la promotion est déjà associée au produit
            if ($produit->promotions()->where('promotion_id', $promotion->id)->exists()) {
                return response()->json([
                    'error' => 'Promotion déjà associée',
                    'message' => 'Cette promotion est déjà associée à ce produit'
                ], 422);
            }

            // Associer la promotion au produit avec les dates spécifiques
            $produit->promotions()->attach($promotion->id, [
                'date_debut' => $request->input('date_debut'),
                'date_fin' => $request->input('date_fin'),
            ]);

            return response()->json([
                'message' => 'Promotion associée au produit avec succès',
                'produit' => $produit->load('promotions')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erreur lors de l\'association de la promotion',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Détacher une promotion d'un produit
     *
     * @param int $produit
     * @param int $promotion
     * @return \Illuminate\Http\JsonResponse
     */
    public function detachPromotion($produit, $promotion)
    {
        try {
            $produit = Produit::findOrFail($produit);
            $promotion = Promotion::findOrFail($promotion);

            // Vérifier si la promotion est associée au produit
            if (!$produit->promotions()->where('promotion_id', $promotion->id)->exists()) {
                return response()->json([
                    'error' => 'Promotion non associée',
                    'message' => 'Cette promotion n\'est pas associée à ce produit'
                ], 422);
            }

            // Détacher la promotion du produit
            $produit->promotions()->detach($promotion->id);

            return response()->json([
                'message' => 'Promotion détachée du produit avec succès',
                'produit' => $produit->load('promotions')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erreur lors du détachement de la promotion',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
