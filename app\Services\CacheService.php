<?php

namespace App\Services;

use App\Models\Categorie;
use App\Models\Promotion;
use App\Models\ProduitValeur;
use App\Models\Produit;
use App\Models\Marque;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Pagination\LengthAwarePaginator;
use Carbon\Carbon;

class CacheService
{
    const CACHE_TTL = 3600; // 1 hour
    const LONG_CACHE_TTL = 86400; // 24 hours
    const SHORT_CACHE_TTL = 900; // 15 minutes

    // Cache key prefixes
    const PRODUCT_LIST_PREFIX = 'product_list:';
    const PRODUCT_DETAIL_PREFIX = 'product_detail:';

    private string $cacheDirectory = 'cache';

    public function __construct()
    {
        // Ensure cache directory exists
        if (!Storage::disk('local')->exists($this->cacheDirectory)) {
            Storage::disk('local')->makeDirectory($this->cacheDirectory);
        }
    }

    /**
     * Store data in file-based cache
     */
    private function setCache(string $key, $data, int $ttl = self::CACHE_TTL): void
    {
        try {
            $cacheData = [
                'data' => $data,
                'expires_at' => Carbon::now()->addSeconds($ttl)->timestamp,
                'created_at' => Carbon::now()->timestamp
            ];

            $filename = $this->getCacheFilename($key);
            Storage::disk('local')->put($filename, serialize($cacheData));

            Log::debug("Cache set: {$key}", ['expires_at' => $cacheData['expires_at']]);
        } catch (\Exception $e) {
            Log::error("Failed to set cache: {$key}", ['error' => $e->getMessage()]);
        }
    }

    /**
     * Get data from file-based cache
     */
    private function getCache(string $key)
    {
        try {
            $filename = $this->getCacheFilename($key);

            if (!Storage::disk('local')->exists($filename)) {
                return null;
            }

            $cacheData = unserialize(Storage::disk('local')->get($filename));

            // Check if cache has expired
            if ($cacheData['expires_at'] < Carbon::now()->timestamp) {
                Storage::disk('local')->delete($filename);
                Log::debug("Cache expired and deleted: {$key}");
                return null;
            }

            Log::debug("Cache hit: {$key}");
            return $cacheData['data'];
        } catch (\Exception $e) {
            Log::error("Failed to get cache: {$key}", ['error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * Delete cache by key
     */
    private function forgetCache(string $key): void
    {
        try {
            $filename = $this->getCacheFilename($key);
            if (Storage::disk('local')->exists($filename)) {
                Storage::disk('local')->delete($filename);
                Log::debug("Cache deleted: {$key}");
            }
        } catch (\Exception $e) {
            Log::error("Failed to delete cache: {$key}", ['error' => $e->getMessage()]);
        }
    }

    /**
     * Clean up expired cache files
     */
    public function clearExpiredCache(): void
    {
        try {
            $files = Storage::disk('local')->files($this->cacheDirectory);
            $currentTime = Carbon::now()->timestamp;
            $deletedCount = 0;

            foreach ($files as $file) {
                try {
                    $cacheData = unserialize(Storage::disk('local')->get($file));
                    if (isset($cacheData['expires_at']) && $cacheData['expires_at'] < $currentTime) {
                        Storage::disk('local')->delete($file);
                        $deletedCount++;
                    }
                } catch (\Exception $e) {
                    // If file is corrupted, delete it
                    Storage::disk('local')->delete($file);
                    $deletedCount++;
                }
            }

            Log::info("Expired cache cleanup completed", ['deleted_files' => $deletedCount]);
        } catch (\Exception $e) {
            Log::error("Failed to clear expired cache", ['error' => $e->getMessage()]);
        }
    }

    /**
     * Generate cache filename
     */
    private function getCacheFilename(string $key): string
    {
        return $this->cacheDirectory . '/' . md5($key) . '.cache';
    }

    /**
     * Get product listing cache
     */
    public function getProductListing(string $cacheKey)
    {
        return $this->getCache(self::PRODUCT_LIST_PREFIX . $cacheKey);
    }

    /**
     * Set product listing cache
     */
    public function setProductListing(string $cacheKey, $data, int $ttl = self::CACHE_TTL): void
    {
        $this->setCache(self::PRODUCT_LIST_PREFIX . $cacheKey, $data, $ttl);
    }

    /**
     * Get product details cache
     */
    public function getProductDetails(int $productId)
    {
        return $this->getCache(self::PRODUCT_DETAIL_PREFIX . $productId);
    }

    /**
     * Set product details cache
     */
    public function setProductDetails(int $productId, $data, int $ttl = self::LONG_CACHE_TTL): void
    {
        $this->setCache(self::PRODUCT_DETAIL_PREFIX . $productId, $data, $ttl);
    }

    /**
     * Flush all product cache
     */
    public function flushProductCache(): void
    {
        try {
            $files = Storage::disk('local')->files($this->cacheDirectory);
            $deletedCount = 0;

            foreach ($files as $file) {
                $filename = basename($file);
                $originalKey = $this->getOriginalKeyFromFilename($filename);

                if (
                    str_starts_with($originalKey, self::PRODUCT_LIST_PREFIX) ||
                    str_starts_with($originalKey, self::PRODUCT_DETAIL_PREFIX)
                ) {
                    Storage::disk('local')->delete($file);
                    $deletedCount++;
                }
            }

            Log::info("Product cache flushed", ['deleted_files' => $deletedCount]);
        } catch (\Exception $e) {
            Log::error("Failed to flush product cache", ['error' => $e->getMessage()]);
        }
    }

    /**
     * Get original key from filename (for cache flushing)
     */
    private function getOriginalKeyFromFilename(string $filename): ?string
    {
        // This is a placeholder. In a real scenario, you might store a mapping
        // or embed the key in the filename if it's not just an md5 hash.
        // For now, we can't reliably get the original key from just the md5 hash.
        // This method would need to be implemented based on how keys are stored/mapped.
        return null; // Or implement a reverse lookup if possible
    }

    public function forgetPopularProducts(): void
    {
        $this->forgetCache('popular_products');
    }

    public function forget(string $key): void
    {
        $this->forgetCache($key);
    }

    public function forgetBrand(int $brandId): void
    {
        $this->forgetCache('brand_' . $brandId);
        // Also forget product lists that might be filtered by this brand
        // This is a simplified example; a more robust solution might involve tagging
        $this->flushProductListingsByPattern('brand_id=' . $brandId);
    }

    public function forgetCategories(): void
    {
        $this->forgetCache('all_categories');
        // Also forget product lists that might be filtered by categories
        $this->flushProductListingsByPattern('category_id='); // Broad flush
    }

    private function flushProductListingsByPattern(string $pattern): void
    {
        try {
            $files = Storage::disk('local')->files($this->cacheDirectory);
            $deletedCount = 0;

            foreach ($files as $file) {
                // This is a simplified approach. Ideally, you would have a way to
                // map filenames back to keys or store keys in a way that allows pattern matching.
                // For now, we assume keys might contain the pattern.
                // This will not work if keys are only md5 hashes without a reverse map.
                if (str_contains(basename($file), md5($pattern))) { // This is a guess
                    // A more robust way would be to iterate and check original keys if stored
                    // or use a tagging system if your cache supports it.
                    // For file-based, you might need to store metadata or key hints.
                    // Storage::disk('local')->delete($file);
                    // $deletedCount++;
                    // Log::debug("Cache possibly related to pattern '{$pattern}' deleted: " . basename($file));
                }
            }
            // if ($deletedCount > 0) {
            //     Log::info("Product listings cache flushed by pattern '{$pattern}'", ['deleted_files' => $deletedCount]);
            // }
        } catch (\Exception $e) {
            Log::error("Failed to flush product listings by pattern '{$pattern}'", ['error' => $e->getMessage()]);
        }
    }

    /**
     * Set cache with alias method for backward compatibility
     */
    public function setToCache(string $key, $data, int $ttl = self::CACHE_TTL): void
    {
        $this->setCache($key, $data, $ttl);
    }

    /**
     * Get cache statistics
     */
    public function getCacheStats(): array
    {
        try {
            $files = Storage::disk('local')->files($this->cacheDirectory);
            $totalFiles = count($files);
            $totalSize = 0;
            $expiredFiles = 0;
            $currentTime = Carbon::now()->timestamp;

            foreach ($files as $file) {
                $size = Storage::disk('local')->size($file);
                $totalSize += $size;

                try {
                    $cacheData = unserialize(Storage::disk('local')->get($file));
                    if (isset($cacheData['expires_at']) && $cacheData['expires_at'] < $currentTime) {
                        $expiredFiles++;
                    }
                } catch (\Exception $e) {
                    $expiredFiles++;
                }
            }

            return [
                'total_files' => $totalFiles,
                'total_size_bytes' => $totalSize,
                'total_size_mb' => round($totalSize / (1024 * 1024), 2),
                'expired_files' => $expiredFiles,
                'active_files' => $totalFiles - $expiredFiles
            ];
        } catch (\Exception $e) {
            Log::error("Failed to get cache stats", ['error' => $e->getMessage()]);
            return [];
        }
    }
}
