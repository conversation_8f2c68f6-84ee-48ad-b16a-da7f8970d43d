<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('attribut_categorie', function (Blueprint $table) {
            $table->id();
            $table->foreignId('attribut_id')->constrained()->onDelete('cascade');
            $table->foreignId('sous_categorie_id')->constrained('sous_categories')->onDelete('cascade');
            $table->boolean('obligatoire')->default(false);
            $table->timestamps();

            $table->unique(['attribut_id', 'sous_categorie_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attribut_categorie');
    }
};
