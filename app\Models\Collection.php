<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use App\Models\Promotion;

class Collection extends Model
{
    protected $fillable = [
        'nom',
        'description',
        'image', // Deprecated - will be removed in favor of images relation
        'active',
        'date_debut',
        'date_fin'
    ];

    protected $casts = [
        'active' => 'boolean',
        'date_debut' => 'date',
        'date_fin' => 'date',
    ];

    /**
     * Les produits appartenant à cette collection
     */
    public function produits(): BelongsToMany
    {
        return $this->belongsToMany(Produit::class, 'collection_produit')
            ->withPivot('ordre', 'featured')
            ->withTimestamps()
            ->orderBy('collection_produit.ordre');
    }

    /**
     * Récupère uniquement les collections actives
     */
    public function scopeActive($query)
    {
        return $query->where('active', true)
            ->where(function ($query) {
                $query->whereNull('date_debut')
                    ->orWhere('date_debut', '<=', now());
            })
            ->where(function ($query) {
                $query->whereNull('date_fin')
                    ->orWhere('date_fin', '>=', now());
            });
    }

    /**
     * Récupère les produits mis en avant dans cette collection
     */
    public function produitsFeatured()
    {
        return $this->produits()->wherePivot('featured', true);
    }

    /**
     * Les promotions associées à cette collection
     */
    public function promotions(): BelongsToMany
    {
        return $this->belongsToMany(Promotion::class, 'collection_promotion')
            ->withPivot('date_debut', 'date_fin')
            ->withTimestamps();
    }

    /**
     * Get all images for this collection
     */
    public function images(): MorphMany
    {
        return $this->morphMany(Image::class, 'imageable')->orderBy('order');
    }

    /**
     * Get the primary image for this collection
     */
    public function getPrimaryImageAttribute()
    {
        return $this->images()->where('is_primary', true)->first() ?? $this->images()->first();
    }

    /**
     * Get the primary image URL for this collection
     */
    public function getPrimaryImageUrlAttribute()
    {
        if ($this->primaryImage) {
            return $this->primaryImage->url;
        }

        // Fallback to the old image field if no images are associated
        if ($this->image) {
            return $this->image;
        }

        return null;
    }
}
