<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ListeSouhait extends Model
{
    use HasFactory;

    protected $table = 'liste_souhaits';

    protected $fillable = [
        'client_id',
        'nom',
        'description',
        'par_defaut'
    ];

    protected $casts = [
        'par_defaut' => 'boolean',
    ];

    /**
     * Relation avec le client (utilisateur)
     */
    public function client()
    {
        return $this->belongsTo(User::class, 'client_id');
    }

    /**
     * Relation avec les items de la liste de souhaits
     */
    public function items()
    {
        return $this->hasMany(ListeSouhaitItem::class);
    }

    /**
     * Vérifie si un produit est dans la liste de souhaits
     */
    public function contientProduit($produitId, $varianteId = null)
    {
        $query = $this->items()->where('produit_id', $produitId);

        if ($varianteId) {
            $query->where('variante_id', $varianteId);
        } else {
            $query->whereNull('variante_id');
        }

        return $query->exists();
    }

    /**
     * Ajoute un produit à la liste de souhaits
     */
    public function ajouterProduit($produitId, $varianteId = null, $note = null)
    {
        // Vérifier si le produit existe
        $produit = Produit::findOrFail($produitId);

        // Vérifier si la variante existe si spécifiée
        if ($varianteId) {
            $variante = ProduitVariante::findOrFail($varianteId);
            $prix = $produit->prix_produit + $variante->prix_supplement;
        } else {
            $prix = $produit->prix_produit;
        }

        // Vérifier si le produit est déjà dans la liste
        if ($this->contientProduit($produitId, $varianteId)) {
            // Mettre à jour la note si nécessaire
            if ($note !== null) {
                $item = $this->items()
                    ->where('produit_id', $produitId)
                    ->where(function ($query) use ($varianteId) {
                        if ($varianteId) {
                            $query->where('variante_id', $varianteId);
                        } else {
                            $query->whereNull('variante_id');
                        }
                    })
                    ->first();

                $item->note = $note;
                $item->save();
            }

            return $this;
        }

        // Ajouter le produit à la liste
        $this->items()->create([
            'produit_id' => $produitId,
            'variante_id' => $varianteId,
            'note' => $note,
            'prix_reference' => $prix
        ]);

        return $this;
    }

    /**
     * Supprime un item de la liste de souhaits
     */
    public function supprimerItem($itemId)
    {
        $item = $this->items()->findOrFail($itemId);
        $item->delete();

        return $this;
    }

    /**
     * Déplace un item vers le panier
     */
    public function deplacerVersLePanier($itemId, $panier, $quantite = 1)
    {
        $item = $this->items()->findOrFail($itemId);

        // Ajouter au panier
        $panier->addItem($item->produit_id, $quantite, $item->variante_id);

        // Supprimer de la liste de souhaits
        $item->delete();

        return $this;
    }
}
