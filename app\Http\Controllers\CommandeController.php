<?php

namespace App\Http\Controllers;

use App\Enums\CommandeStatus;
use App\Models\Commande;
use App\Models\User;
use App\Models\Client;
use App\Services\OrderService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\JsonResponse;

class CommandeController extends Controller
{
    protected OrderService $orderService;

    public function __construct(OrderService $orderService)
    {
        $this->orderService = $orderService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            // Optimize with eager loading
            $query = Commande::with(['produits', 'paiement', 'user']);

            if ($request->has('user_id')) {
                $query->where('user_id', $request->input('user_id'));
            } elseif (Auth::check() && !$request->user()->hasRole('admin')) {
                $query->where('user_id', Auth::id());
            }

            if ($request->has('status')) {
                $query->where('status', $request->input('status'));
            }
            if ($request->has('payment_status')) {
                $query->where('payment_status', $request->input('payment_status'));
            }

            if ($request->has('date_from')) {
                $query->whereDate('created_at', '>=', $request->input('date_from'));
            }
            if ($request->has('date_to')) {
                $query->whereDate('created_at', '<=', $request->input('date_to'));
            }

            if ($request->has('with')) {
                $relations = explode(',', $request->input('with'));
                $allowedRelations = ['user', 'produits', 'paiement', 'client'];
                $validRelations = array_intersect($relations, $allowedRelations);
                if (!empty($validRelations)) {
                    $query->with($validRelations);
                }
            }

            $sortField = $request->input('sort_by', 'created_at');
            $sortDirection = $request->input('sort_direction', 'desc');
            $allowedSortFields = ['id', 'created_at', 'total', 'status', 'payment_status'];
            if (in_array($sortField, $allowedSortFields)) {
                $query->orderBy($sortField, $sortDirection === 'desc' ? 'desc' : 'asc');
            }

            $perPage = min(max(1, $request->input('per_page', 15)), 100);
            $commandes = $query->paginate($perPage);

            // Transform the data for standardized response structure
            $commandes->getCollection()->transform(function ($commande) {
                $data = $commande->toArray();

                // Add standardized field names - using new column structure
                $data['adresse'] = $data['shipping_street'] ?? null;
                $data['ville'] = $data['shipping_city'] ?? null;
                $data['code_postal'] = $data['shipping_postal_code'] ?? null;
                $data['telephone'] = $data['telephone_commande'] ?? null;
                $data['email'] = $data['email_commande'] ?? null;
                $data['total'] = $data['total_commande'] ?? null;
                $data['remise'] = $data['remise_commande'] ?? null;

                // Add status label
                if (isset($data['status'])) {
                    $data['status_label'] = \App\Enums\CommandeStatus::from($data['status'])->label();
                }

                // Add client remise if available
                if (isset($data['user']) && is_array($data['user'])) {
                    $data['client_remise'] = $data['user']['remise_personnelle'] ?? 0;
                }

                // Transform products data to add standardized fields
                if (isset($data['produits']) && is_array($data['produits'])) {
                    foreach ($data['produits'] as &$produit) {
                        if (isset($produit['pivot'])) {
                            // Add calculated fields
                            $produit['quantite'] = $produit['pivot']['quantite'];
                            $produit['prix_unitaire'] = $produit['pivot']['prix_unitaire'];
                            $produit['sous_total'] = $produit['pivot']['quantite'] * $produit['pivot']['prix_unitaire'];

                            // Add standardized field names
                            $produit['nom'] = $produit['nom_produit'];
                            $produit['prix'] = $produit['prix_produit'];
                            $produit['description'] = $produit['description_produit'];
                        }
                    }
                }

                return $data;
            });

            return $this->successResponse($commandes, 'Commandes récupérées avec succès');
        } catch (\Exception $e) {
            return $this->errorResponse('Erreur lors de la récupération des commandes', 500, [], ['message' => $e->getMessage()]);
        }
    }

    /**
     * Store a newly created resource in storage.
     * This method creates an order from a cart.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'cart_id' => 'required|string|exists:paniers,id',
            'user_id' => 'nullable|exists:users,id',
            'client_id' => 'nullable|exists:clients,id',
            'shipping_address' => 'required|array',
            'shipping_address.street' => 'required|string',
            'shipping_address.city' => 'required|string',
            'shipping_address.postal_code' => 'required|string',
            'shipping_address.country' => 'required|string',
            'billing_address' => 'required|array',
            'billing_address.street' => 'required|string',
            'billing_address.city' => 'required|string',
            'billing_address.postal_code' => 'required|string',
            'billing_address.country' => 'required|string',
            'notes' => 'nullable|string|max:1000',
            'code_promo' => 'nullable|string',
            'methode_paiement' => 'required|string',
            'payment_details' => 'nullable|array',
            'guest_details' => 'nullable|array',
            'guest_details.email' => 'required_without_all:user_id,client_id|email',
            'guest_details.nom' => 'required_without_all:user_id,client_id|string|max:255',
            'guest_details.telephone' => 'nullable|string|max:20',
        ]);

        if ($validator->fails()) {
            return $this->validationErrorResponse($validator);
        }

        try {
            $user = Auth::user();
            $client = null;
            $orderData = $request->only([
                'shipping_address',
                'billing_address',
                'notes',
                'code_promo',
                'methode_paiement',
                'payment_details'
            ]);

            $userId = $request->input('user_id');
            $clientId = $request->input('client_id');

            if ($userId) {
                $user = User::find($userId);
                if (!$user)
                    return $this->errorResponse('Utilisateur non trouvé.', 404);
            } elseif ($clientId) {
                $client = Client::find($clientId);
                if (!$client)
                    return $this->errorResponse('Client non trouvé.', 404);
            } elseif ($request->has('guest_details')) {
                $orderData = array_merge($orderData, $request->input('guest_details'));
                $orderData['is_guest_checkout'] = true;
            } else if ($user) {
            } else {
                return $this->errorResponse('Informations utilisateur ou invité manquantes.', 400);
            }

            $commande = $this->orderService->createOrderFromCart(
                $request->cart_id,
                $user,
                $client,
                $orderData
            );
            return $this->successResponse($commande->load(['produits', 'paiement', 'user', 'client']), 'Commande créée avec succès', 201);
        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 400);
        } catch (\Exception $e) {
            return $this->errorResponse('Erreur lors de la création de la commande: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id, Request $request): JsonResponse
    {
        try {
            // Get authenticated user outside of cache closure
            $user = $request->user();
            $isAdmin = $user && $user->hasRole('admin');
            $userId = Auth::id();
            $clientId = optional($user)->client_id;

            // Determine relations to load
            $relations = ['user', 'produits', 'paiement', 'client'];
            if ($request->has('with')) {
                $requestedRelations = explode(',', $request->input('with'));
                $validRelations = array_intersect($requestedRelations, $relations);
                if (!empty($validRelations)) {
                    $relations = $validRelations;
                }
            }

            // Create cache key that includes user context for security
            $cacheKey = 'commande_'.$id.'_'.($userId ?? 'guest').'_'.implode(',', $relations);
            
            return cache()->remember($cacheKey, 300, function () use ($id, $relations, $isAdmin, $userId, $clientId) {
                $commande = Commande::with($relations)->findOrFail($id);

                // Check authorization
                if (!$isAdmin && $commande->user_id !== $userId && $commande->client_id !== $clientId) {
                    return $this->errorResponse('Accès non autorisé à cette commande.', 403);
                }

                // Transform the data for standardized response structure
                $data = $commande->toArray();

                // Add standardized field names - using new column structure
                $data['adresse'] = $data['shipping_street'] ?? null;
                $data['ville'] = $data['shipping_city'] ?? null;
                $data['code_postal'] = $data['shipping_postal_code'] ?? null;
                $data['telephone'] = $data['telephone_commande'] ?? null;
                $data['email'] = $data['email_commande'] ?? null;
                $data['total'] = $data['total_commande'] ?? null;
                $data['remise'] = $data['remise_commande'] ?? null;

                // Add status label
                if (isset($data['status'])) {
                    $data['status_label'] = \App\Enums\CommandeStatus::from($data['status'])->label();
                }

                // Add client remise if available
                if (isset($data['user']) && is_array($data['user'])) {
                    $data['client_remise'] = $data['user']['remise_personnelle'] ?? 0;
                }

                // Transform products data to add standardized fields
                if (isset($data['produits']) && is_array($data['produits'])) {
                    foreach ($data['produits'] as &$produit) {
                        if (isset($produit['pivot'])) {
                            // Add calculated fields
                            $produit['quantite'] = $produit['pivot']['quantite'];
                            $produit['prix_unitaire'] = $produit['pivot']['prix_unitaire'];
                            $produit['sous_total'] = $produit['pivot']['quantite'] * $produit['pivot']['prix_unitaire'];

                            // Add standardized field names
                            $produit['nom'] = $produit['nom_produit'];
                            $produit['prix'] = $produit['prix_produit'];
                            $produit['description'] = $produit['description_produit'];
                        }
                    }
                }

                return $this->successResponse($data, 'Commande récupérée avec succès');
            });
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->errorResponse('Commande non trouvée', 404);
        } catch (\Exception $e) {
            return $this->errorResponse('Problème de récupération de la commande', 500, [], ['message' => $e->getMessage()]);
        }
    }

    /**
     * Update the status of an order.
     */
    public function updateStatus(Request $request, string $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'status' => ['required', new Enum(CommandeStatus::class)],
            'notes' => 'nullable|string|max:1000',
            'send_notification' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return $this->validationErrorResponse($validator);
        }

        try {
            $commande = Commande::findOrFail($id);
            $newStatus = CommandeStatus::from($request->status);
            $notes = $request->input('notes');
            $sendNotification = $request->input('send_notification', true);

            $updatedCommande = $this->orderService->updateOrderStatus($commande, $newStatus, $notes, $sendNotification);

            return $this->successResponse($updatedCommande->load(['produits', 'paiement', 'user', 'client']), 'Statut de la commande mis à jour avec succès');
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->errorResponse('Commande non trouvée', 404);
        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);
        } catch (\Exception $e) {
            return $this->errorResponse('Erreur lors de la mise à jour du statut: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Process payment for an order.
     */
    public function processPayment(Request $request, string $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'payment_details' => 'required|array',
        ]);

        if ($validator->fails()) {
            return $this->validationErrorResponse($validator);
        }

        try {
            $commande = Commande::findOrFail($id);

            if ($commande->status !== CommandeStatus::EN_ATTENTE || $commande->payment_status === 'paid') {
                return $this->errorResponse('Le paiement pour cette commande ne peut pas être traité ou a déjà été traité.', 422);
            }

            $paymentDetails = $request->input('payment_details');
            $paiement = $this->orderService->processPayment($commande, $paymentDetails);

            $commande->refresh();

            return $this->successResponse([
                'commande' => $commande->load(['produits', 'paiement', 'user', 'client']),
                'paiement' => $paiement
            ], 'Paiement traité avec succès');
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->errorResponse('Commande non trouvée pour le paiement', 404);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->validator);
        } catch (\Exception $e) {
            return $this->errorResponse('Erreur lors du traitement du paiement: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Cancel an order.
     */
    public function cancelOrder(Request $request, string $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'reason' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return $this->validationErrorResponse($validator);
        }

        try {
            $commande = Commande::findOrFail($id);
            $reason = $request->input('reason');

            $cancelledCommande = $this->orderService->cancelOrder($commande, $reason);

            return $this->successResponse($cancelledCommande->load(['produits', 'paiement', 'user', 'client']), 'Commande annulée avec succès');
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->errorResponse('Commande non trouvée pour annulation', 404);
        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);
        } catch (\Exception $e) {
            return $this->errorResponse('Erreur lors de l\'annulation de la commande: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Remove the specified resource from storage. (Admin only)
     */
    public function destroy(string $id): JsonResponse
    {
        if (!Auth::check() || !Auth::user()->hasRole('admin')) {
            return $this->errorResponse('Non autorisé à supprimer cette commande.', 403);
        }

        try {
            $commande = Commande::findOrFail($id);

            $commande->produits()->detach();
            if ($commande->paiement) {
                $commande->paiement->delete();
            }
            $commande->delete();

            return $this->successResponse(null, 'Commande supprimée définitivement avec succès', 204);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->errorResponse('Commande non trouvée', 404);
        } catch (\Exception $e) {
            return $this->errorResponse('Problème de suppression de la commande: ' . $e->getMessage(), 500);
        }
    }

    /**
     * @param mixed $result
     * @param string $message
     * @param int $code
     * @return JsonResponse
     */
    protected function successResponse($result, string $message = 'Opération réussie', int $code = 200): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $result
        ], $code);
    }

    protected function errorResponse(string $message, int $code = 400, array $data = [], array $debug = []): JsonResponse
    {
        $response = [
            'success' => false,
            'message' => $message,
        ];
        if (!empty($data)) {
            $response['data'] = $data;
        }
        if (!empty($debug) && config('app.debug')) {
            $response['debug'] = $debug;
        }
        return response()->json($response, $code);
    }

    protected function validationErrorResponse(\Illuminate\Contracts\Validation\Validator $validator): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => 'Erreur de validation.',
            'errors' => $validator->errors()
        ], 422);
    }
}

