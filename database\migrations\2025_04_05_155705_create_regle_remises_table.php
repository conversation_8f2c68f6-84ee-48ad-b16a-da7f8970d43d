<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('regle_remises', function (Blueprint $table) {
            $table->id();
            $table->string('nom');
            $table->text('description')->nullable();
            $table->enum('type_client', ['standard', 'premium', 'affilie', 'groupe']);
            $table->decimal('valeur', 10, 2);
            $table->enum('type', ['pourcentage', 'montant_fixe']);
            $table->integer('priorité')->default(0);
            $table->boolean('active')->default(true);
            $table->json('conditions_supplementaires')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('regle_remises');
    }
};
