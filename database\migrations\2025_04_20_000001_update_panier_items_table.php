<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('panier_items', function (Blueprint $table) {
            if (!Schema::hasColumn('panier_items', 'panier_id')) {
                $table->foreignId('panier_id')->constrained()->onDelete('cascade');
            }
            
            if (!Schema::hasColumn('panier_items', 'produit_id')) {
                $table->foreignId('produit_id')->constrained()->onDelete('cascade');
            }
            
            if (!Schema::hasColumn('panier_items', 'variante_id')) {
                $table->foreignId('variante_id')->nullable()->constrained('produit_variantes')->nullOnDelete();
            }
            
            if (!Schema::hasColumn('panier_items', 'quantite')) {
                $table->integer('quantite')->default(1);
            }
            
            if (!Schema::hasColumn('panier_items', 'prix_unitaire')) {
                $table->decimal('prix_unitaire', 10, 2);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('panier_items', function (Blueprint $table) {
            if (Schema::hasColumn('panier_items', 'panier_id')) {
                $table->dropForeign(['panier_id']);
                $table->dropColumn('panier_id');
            }
            
            if (Schema::hasColumn('panier_items', 'produit_id')) {
                $table->dropForeign(['produit_id']);
                $table->dropColumn('produit_id');
            }
            
            if (Schema::hasColumn('panier_items', 'variante_id')) {
                $table->dropForeign(['variante_id']);
                $table->dropColumn('variante_id');
            }
            
            if (Schema::hasColumn('panier_items', 'quantite')) {
                $table->dropColumn('quantite');
            }
            
            if (Schema::hasColumn('panier_items', 'prix_unitaire')) {
                $table->dropColumn('prix_unitaire');
            }
        });
    }
};
