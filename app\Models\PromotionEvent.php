<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PromotionEvent extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'nom',
        'code',
        'description',
        'couleur',
        'icone',
        'actif',
        'date_debut',
        'date_fin',
    ];

    protected $casts = [
        'actif' => 'boolean',
        'date_debut' => 'date',
        'date_fin' => 'date',
    ];

    /**
     * Get the promotions associated with this event
     */
    public function promotions()
    {
        return $this->hasMany(Promotion::class, 'event_id');
    }

    /**
     * Check if the event is currently active
     */
    public function estActif()
    {
        if (!$this->actif) {
            return false;
        }

        $maintenant = now()->startOfDay();

        if ($this->date_debut && $maintenant->lt($this->date_debut)) {
            return false;
        }

        if ($this->date_fin && $maintenant->gt($this->date_fin)) {
            return false;
        }

        return true;
    }
}
