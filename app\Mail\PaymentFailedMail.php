<?php

namespace App\Mail;

use App\Models\Commande;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class PaymentFailedMail extends Mailable
{
    use Queueable, SerializesModels;

    public Commande $commande;
    public string $failureReason;

    /**
     * Create a new message instance.
     */
    public function __construct(Commande $commande, string $failureReason = 'Erreur de paiement')
    {
        $this->commande = $commande;
        $this->failureReason = $failureReason;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            from: config('mail.from.address'),
            subject: 'Problème de paiement pour votre commande #' . $this->commande->numero_commande,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.orders.payment-failed',
            with: [
                'commande' => $this->commande,
                'orderNumber' => $this->commande->numero_commande,
                'orderTotal' => $this->commande->total_commande,
                'failureReason' => $this->failureReason,
                'attemptedAt' => now()->format('d/m/Y à H:i'),
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
