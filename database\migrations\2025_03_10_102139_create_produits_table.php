<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('produits', function (Blueprint $table) {
            $table->id();
            $table->string('nom_produit');
            $table->string('description_produit');
            $table->string('image_produit');
            $table->float('prix_produit');
            $table->integer('quantite_produit');
            $table->foreignId('marque_id')->constrained('marques');
            $table->foreignId('sous_sous_categorie_id')->constrained('sous_sous_categories');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('produits');
    }
};


