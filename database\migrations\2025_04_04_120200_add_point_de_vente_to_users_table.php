<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->foreignId('point_de_vente_id')->nullable()->after('roles')
                ->constrained('points_de_vente')->nullOnDelete();
            $table->decimal('remise_personnelle', 5, 2)->default(0)->after('point_de_vente_id');
            $table->enum('type_client', ['normal', 'partenaire', 'point_de_vente'])->default('normal')->after('remise_personnelle');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['point_de_vente_id']);
            $table->dropColumn(['point_de_vente_id', 'remise_personnelle', 'type_client']);
        });
    }
};
