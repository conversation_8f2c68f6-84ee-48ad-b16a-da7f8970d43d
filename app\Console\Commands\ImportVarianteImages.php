<?php

namespace App\Console\Commands;

use App\Models\Image;
use App\Models\ProduitVariante;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ImportVarianteImages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'images:import-variantes
                            {--path= : Chemin dans le stockage où se trouvent les images (ex: produits/variantes)}
                            {--pattern= : Motif pour filtrer les images (ex: *.jpg)}
                            {--dry-run : Exécuter sans effectuer de modifications}
                            {--root : Chercher dans le répertoire racine du stockage}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Importe les images existantes et les associe aux variantes de produits';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $path = $this->option('path') ?: 'produits';
        $pattern = $this->option('pattern') ?: '*';
        $dryRun = $this->option('dry-run');
        $useRoot = $this->option('root');

        // Si l'option --root est utilisée, utiliser le répertoire racine
        if ($useRoot) {
            $path = '';
            $this->info("Recherche dans le répertoire racine du stockage");
        }

        $this->info("Recherche des images dans le chemin: {$path}");
        $this->info("Motif de recherche: {$pattern}");

        if ($dryRun) {
            $this->warn("Mode simulation activé - aucune modification ne sera effectuée");
        }

        // Récupérer toutes les images dans le chemin spécifié
        $files = Storage::disk('s3')->files($path);

        // Filtrer les fichiers selon le motif
        if ($pattern !== '*') {
            $files = array_filter($files, function ($file) use ($pattern) {
                return Str::is($pattern, basename($file));
            });
        }

        $this->info("Nombre d'images trouvées: " . count($files));

        if (count($files) === 0) {
            $this->error("Aucune image trouvée. Vérifiez le chemin et le motif.");
            return 1;
        }

        // Demander à l'utilisateur comment il souhaite associer les images
        $associationMethod = $this->choice(
            'Comment souhaitez-vous associer les images aux variantes?',
            [
                'sku' => 'Par SKU (le nom du fichier doit contenir le SKU de la variante)',
                'id' => 'Par ID (le nom du fichier doit contenir l\'ID de la variante)',
                'manual' => 'Manuellement (vous associerez chaque image à une variante)',
            ],
            'sku'
        );

        $totalProcessed = 0;
        $totalAssociated = 0;
        $totalSkipped = 0;
        $totalErrors = 0;

        foreach ($files as $file) {
            $this->info("Traitement de l'image: {$file}");
            $totalProcessed++;

            try {
                // Récupérer les métadonnées du fichier
                $size = Storage::disk('s3')->size($file);
                $mimeType = Storage::disk('s3')->mimeType($file);
                $filename = basename($file);

                // Trouver la variante associée
                $variante = null;

                switch ($associationMethod) {
                    case 'sku':
                        // Extraire le SKU du nom de fichier
                        $sku = $this->extractSkuFromFilename($filename);
                        if ($sku) {
                            $variante = ProduitVariante::where('sku', 'like', "%{$sku}%")->first();
                            if ($variante) {
                                $this->info("Variante trouvée avec SKU: {$variante->sku} (ID: {$variante->id})");
                            } else {
                                $this->warn("Aucune variante trouvée avec SKU contenant: {$sku}");
                            }
                        } else {
                            $this->warn("Impossible d'extraire un SKU du nom de fichier: {$filename}");
                        }
                        break;

                    case 'id':
                        // Extraire l'ID du nom de fichier
                        $id = $this->extractIdFromFilename($filename);
                        if ($id) {
                            $variante = ProduitVariante::find($id);
                            if ($variante) {
                                $this->info("Variante trouvée avec ID: {$variante->id}");
                            } else {
                                $this->warn("Aucune variante trouvée avec ID: {$id}");
                            }
                        } else {
                            $this->warn("Impossible d'extraire un ID du nom de fichier: {$filename}");
                        }
                        break;

                    case 'manual':
                        // Afficher toutes les variantes et demander à l'utilisateur de choisir
                        $this->info("Sélection manuelle pour l'image: {$filename}");

                        // Demander s'il faut associer cette image
                        if (!$this->confirm("Voulez-vous associer cette image à une variante?", true)) {
                            $this->warn("Image ignorée: {$filename}");
                            $totalSkipped++;
                            continue 2; // Continuer avec la prochaine itération de la boucle externe
                        }

                        // Demander le SKU ou l'ID de la variante
                        $search = $this->ask("Entrez le SKU ou l'ID de la variante");

                        // Rechercher par SKU ou ID
                        if (is_numeric($search)) {
                            $variante = ProduitVariante::find($search);
                        } else {
                            $variante = ProduitVariante::where('sku', 'like', "%{$search}%")->first();
                        }

                        if ($variante) {
                            $this->info("Variante trouvée: {$variante->sku} (ID: {$variante->id})");
                        } else {
                            $this->error("Aucune variante trouvée avec SKU ou ID: {$search}");
                            $totalSkipped++;
                            continue 2; // Continuer avec la prochaine itération de la boucle externe
                        }
                        break;
                }

                if (!$variante) {
                    $this->warn("Aucune variante associée pour l'image: {$filename}");
                    $totalSkipped++;
                    continue;
                }

                // Vérifier si l'image existe déjà pour cette variante
                $existingImage = $variante->images()
                    ->where('path', $file)
                    ->first();

                if ($existingImage) {
                    $this->warn("Une image avec ce chemin existe déjà pour cette variante. Image ignorée.");
                    $totalSkipped++;
                    continue;
                }

                // Créer l'enregistrement d'image
                if (!$dryRun) {
                    $image = new Image([
                        'path' => $file,
                        'filename' => $filename,
                        'disk' => 's3',
                        'mime_type' => $mimeType,
                        'size' => $size,
                        'alt_text' => "Image pour {$variante->sku}",
                        'title' => "Variante {$variante->sku}",
                        'is_primary' => !$variante->images()->exists(), // Première image = primaire
                        'order' => $variante->images()->count(),
                        'metadata' => [
                            'original_filename' => $filename,
                            'extension' => pathinfo($filename, PATHINFO_EXTENSION),
                        ],
                    ]);

                    $variante->images()->save($image);
                    $this->info("Image associée avec succès à la variante: {$variante->sku}");
                    $totalAssociated++;
                } else {
                    $this->info("[SIMULATION] L'image serait associée à la variante: {$variante->sku}");
                    $totalAssociated++;
                }
            } catch (\Exception $e) {
                $this->error("Erreur lors du traitement de l'image {$file}: " . $e->getMessage());
                $totalErrors++;
            }
        }

        $this->newLine();
        $this->info("Résumé de l'importation:");
        $this->info("- Images traitées: {$totalProcessed}");
        $this->info("- Images associées: {$totalAssociated}");
        $this->info("- Images ignorées: {$totalSkipped}");
        $this->info("- Erreurs: {$totalErrors}");

        if ($dryRun) {
            $this->warn("Mode simulation - Aucune modification n'a été effectuée");
        }

        return 0;
    }

    /**
     * Extraire le SKU du nom de fichier
     */
    protected function extractSkuFromFilename($filename)
    {
        // Supprimer l'extension
        $name = pathinfo($filename, PATHINFO_FILENAME);

        // Méthode 1: Rechercher un motif de SKU (ex: ABC-123)
        if (preg_match('/([A-Za-z0-9]+-[A-Za-z0-9]+)/', $name, $matches)) {
            return $matches[1];
        }

        // Méthode 2: Utiliser le nom de fichier sans extension comme SKU
        return $name;
    }

    /**
     * Extraire l'ID du nom de fichier
     */
    protected function extractIdFromFilename($filename)
    {
        // Rechercher un nombre dans le nom de fichier
        if (preg_match('/(\d+)/', $filename, $matches)) {
            return $matches[1];
        }

        return null;
    }
}
