<?php

namespace App\Http\Controllers;

use App\Models\Commande;
use App\Models\GroupeClient;
use App\Models\Partenaire;
use App\Models\PointDeVente;
use App\Models\User;
use App\Services\KeycloakService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use App\Http\Requests\UpdateClientDiscountRequest;
use App\Http\Requests\UpdateClientTypeRequest;

class ClientController extends Controller
{
    protected KeycloakService $keycloakService;

    public function __construct(KeycloakService $keycloakService)
    {
        $this->keycloakService = $keycloakService;
    }
    /**
     * Map legacy type names to profil_remise
     *
     * @param string $type
     * @return string
     */
    protected function getLegacyTypeMapping(string $type): string
    {
        return match ($type) {
            'normal' => 'standard',
            'partenaire' => 'premium',
            'point_de_vente' => 'affilie',
            'groupe' => 'groupe',
            default => $type
        };
    }
    /**
     * Display a listing of the clients.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            // Base query: get all users with the 'client' role
            // This ensures we only get users who can access client features
            $query = User::whereJsonContains('roles', 'client');

            // Filter by client type if specified
            if ($request->has('type')) {
                $type = $request->input('type');

                // Map legacy types to profil_remise if needed
                $profilRemise = $this->getLegacyTypeMapping($type);

                // Filter by profil_remise
                $query->where('profil_remise', $profilRemise);
            }

            // Filter by point de vente if specified
            if ($request->has('point_de_vente_id')) {
                $query->where('point_de_vente_id', $request->input('point_de_vente_id'));
            }

            // Filter by groupe client if specified
            if ($request->has('groupe_client_id')) {
                $query->where('groupe_client_id', $request->input('groupe_client_id'));
            }

            // Include relationships based on client type
            $query->with(['partenaire', 'pointDeVente', 'groupeClient']);

            $clients = $query->get();

            // Add effective discount to each client
            $clients->each(function ($client) {
                $client->remise_effective = $client->getRemiseEffective();
            });

            return response()->json($clients);
        } catch (\Exception $e) {
            return response()->json([
                "error" => "Problème de récupération des clients",
                "message" => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified client.
     *
     * @param string $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(string $id)
    {
        try {
            $client = User::with(['partenaire', 'pointDeVente', 'groupeClient'])
                ->findOrFail($id);

            // Check if the user has the client role
            if (!$client->hasRole('client')) {
                return response()->json([
                    "error" => "L'utilisateur n'est pas un client"
                ], 400);
            }

            // Add effective discount
            $client->remise_effective = $client->getRemiseEffective();

            return response()->json($client);
        } catch (\Exception $e) {
            return response()->json([
                "error" => "Problème de récupération du client",
                "message" => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update a client's discount.
     *
     * @param UpdateClientDiscountRequest $request
     * @param string $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateDiscount(UpdateClientDiscountRequest $request, string $id)
    {
        try {
            $validatedData = $request->validated();

            $client = User::findOrFail($id);

            // Check if the user has the client role
            if (!$client->hasRole('client')) {
                return response()->json([
                    "error" => "L'utilisateur n'est pas un client"
                ], 400);
            }

            // Update the personal discount
            $client->remise_personnelle = $validatedData['remise_personnelle'];
            $client->save();

            // Add effective discount
            $client->remise_effective = $client->getRemiseEffective();

            return response()->json($client);
        } catch (\Exception $e) {
            return response()->json([
                "error" => "Problème lors de la mise à jour de la remise",
                "message" => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update a client's profile.
     *
     * @param UpdateClientTypeRequest $request
     * @param string $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateType(UpdateClientTypeRequest $request, string $id)
    {
        try {
            $validatedData = $request->validated();

            // Start a transaction
            DB::beginTransaction();

            $client = User::findOrFail($id);

            // Check if the user has the client role
            if (!$client->hasRole('client')) {
                return response()->json([
                    "error" => "L'utilisateur n'est pas un client"
                ], 400);
            }

            // Update client profile
            $typeOrProfile = $validatedData['type_client'];
            $client->profil_remise = $this->getLegacyTypeMapping($typeOrProfile);

            // Reset both point_de_vente_id and groupe_client_id
            $client->point_de_vente_id = null;
            $client->groupe_client_id = null;

            // Handle point_de_vente_id based on profile
            if ($client->profil_remise === 'affilie') {
                if (empty($validatedData['point_de_vente_id'])) {
                    return response()->json([
                        "error" => "L'ID du point de vente est requis pour ce type de client"
                    ], 400);
                }
                $client->point_de_vente_id = $validatedData['point_de_vente_id'];
            }

            // Handle groupe_client_id based on profile
            if ($client->profil_remise === 'groupe') {
                if (empty($validatedData['groupe_client_id'])) {
                    return response()->json([
                        "error" => "L'ID du groupe de clients est requis pour ce type de client"
                    ], 400);
                }
                $client->groupe_client_id = $validatedData['groupe_client_id'];
            }

            // Handle partenaire relationship
            if ($client->profil_remise === 'premium') {
                // Add partenaire role if not already present
                $roles = $client->roles ?? [];
                if (!in_array('partenaire', $roles)) {
                    $roles[] = 'partenaire';
                    $client->roles = $roles;

                    // Sync with Keycloak if keycloak_id is available
                    if ($client->keycloak_id) {
                        try {
                            $this->keycloakService->addRoleToUser($client->keycloak_id, 'partenaire');
                        } catch (\Exception $e) {
                            Log::error("Failed to add partenaire role to user in Keycloak: " . $e->getMessage());
                        }
                    }
                }
            } else {
                // Remove partenaire role if present
                $roles = $client->roles ?? [];
                if (in_array('partenaire', $roles)) {
                    $roles = array_diff($roles, ['partenaire']);
                    $client->roles = $roles;

                    // Sync with Keycloak if keycloak_id is available
                    if ($client->keycloak_id) {
                        try {
                            $this->keycloakService->removeRoleFromUser($client->keycloak_id, 'partenaire');
                        } catch (\Exception $e) {
                            Log::error("Failed to remove partenaire role from user in Keycloak: " . $e->getMessage());
                        }
                    }
                }
            }

            // Create partenaire record if needed
            if ($client->profil_remise === 'premium' && !$client->partenaire) {
                Partenaire::create([
                    'user_id' => $client->id,
                    'remise' => 0, // Default discount
                    'statut' => 'actif'
                ]);
            }

            $client->save();

            // Commit the transaction
            DB::commit();

            // Reload the client with relationships
            $client = User::with(['partenaire', 'pointDeVente', 'groupeClient'])->find($id);

            // Add effective discount
            $client->remise_effective = $client->getRemiseEffective();

            return response()->json($client);
        } catch (\Exception $e) {
            // Rollback the transaction
            DB::rollBack();

            return response()->json([
                "error" => "Problème lors de la mise à jour du type de client",
                "message" => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get the latest order for a specific client.
     *
     * @param string $id The ID of the client
     * @return \Illuminate\Http\JsonResponse
     */
    public function getLatestOrder(string $id) // getDerniereCommande
    {
        try {
            // First check if the user exists
            $user = User::findOrFail($id); // Verify user exists, will throw exception if not found

            // For admin routes, we don't need to check the role
            // Get the latest order for this client/user
            $latestOrder = Commande::where('user_id', $id)
                ->orderBy('created_at', 'desc')
                ->first();

            if (!$latestOrder) {
                return $this->errorResponse('Aucune commande trouvée pour cet utilisateur', 404);
            }

            // Load the products relationship to get complete order details
            $latestOrder->load(['produits', 'user', 'paiement']);

            // Transform the data for standardized response
            $data = $latestOrder->toArray();

            // Add standardized field names - using new column structure
            $data['adresse'] = $data['shipping_street'] ?? null;
            $data['ville'] = $data['shipping_city'] ?? null;
            $data['code_postal'] = $data['shipping_postal_code'] ?? null;
            $data['telephone'] = $data['telephone_commande'] ?? null;
            $data['email'] = $data['email_commande'] ?? null;
            $data['total'] = $data['total_commande'] ?? null;
            $data['remise'] = $data['remise_commande'] ?? null;

            // Add status label
            if (isset($data['status'])) {
                $data['status_label'] = \App\Enums\CommandeStatus::from($data['status'])->label();
            }

            // Add the effective discount information
            $data['client_remise'] = $user->getRemiseEffective();

            // Transform product data if loaded
            if (isset($data['produits']) && is_array($data['produits'])) {
                foreach ($data['produits'] as &$produit) {
                    if (isset($produit['pivot'])) {
                        // Add calculated fields
                        $produit['quantite'] = $produit['pivot']['quantite'];
                        $produit['prix_unitaire'] = $produit['pivot']['prix_unitaire'];
                        $produit['sous_total'] = $produit['pivot']['quantite'] * $produit['pivot']['prix_unitaire'];

                        // Add standardized field names
                        $produit['nom'] = $produit['nom_produit'];
                        $produit['prix'] = $produit['prix_produit'];
                        $produit['description'] = $produit['description_produit'];
                    }
                }
            }

            return $this->successResponse($data, 'Dernière commande récupérée avec succès');
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->errorResponse('Utilisateur non trouvé', 404);
        } catch (\Exception $e) {
            return $this->errorResponse(
                'Problème de récupération de la dernière commande',
                500,
                [],
                ['message' => $e->getMessage(), 'trace' => $e->getTraceAsString()]
            );
        }
    }

    /**
     * Get all orders for a specific client.
     *
     * @param string $id The ID of the client
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getOrders(string $id, Request $request) // getCommandes
    {
        try {
            // First check if the user exists
            $user = User::findOrFail($id); // Verify user exists, will throw exception if not found

            // Initialize the query
            $query = Commande::where('user_id', $id);

            // Filter by status if provided
            if ($request->has('status')) {
                $query->where('status', $request->input('status'));
            }

            // Filter by date range if provided
            if ($request->has('date_from')) {
                $query->whereDate('created_at', '>=', $request->input('date_from'));
            }

            if ($request->has('date_to')) {
                $query->whereDate('created_at', '<=', $request->input('date_to'));
            }

            // Load relationships if requested
            if ($request->has('with')) {
                $relations = explode(',', $request->input('with'));
                $allowedRelations = ['produits', 'paiement'];
                $validRelations = array_intersect($relations, $allowedRelations);

                if (!empty($validRelations)) {
                    $query->with($validRelations);
                }
            }

            // Sort by created_at by default, newest first
            $sortField = $request->input('sort_by', 'created_at');
            $sortDirection = $request->input('sort_direction', 'desc');

            // List of allowed sort fields
            $allowedSortFields = ['id', 'created_at', 'total_commande', 'status'];

            if (in_array($sortField, $allowedSortFields)) {
                $query->orderBy($sortField, $sortDirection === 'desc' ? 'desc' : 'asc');
            }

            // Pagination
            $perPage = $request->input('per_page', 15);
            $page = $request->input('page', 1);

            // Limit the number of items per page to a reasonable value
            $perPage = min(max(1, $perPage), 100);

            $commandes = $query->paginate($perPage, ['*'], 'page', $page);

            if ($commandes->isEmpty()) {
                return $this->errorResponse('Aucune commande trouvée pour cet utilisateur', 404);
            }

            // Transform the data for standardized response
            $commandes->getCollection()->transform(function ($commande) {
                $data = $commande->toArray();

                // Add standardized field names - using new column structure
                $data['adresse'] = $data['shipping_street'] ?? null;
                $data['ville'] = $data['shipping_city'] ?? null;
                $data['code_postal'] = $data['shipping_postal_code'] ?? null;
                $data['telephone'] = $data['telephone_commande'] ?? null;
                $data['email'] = $data['email_commande'] ?? null;
                $data['total'] = $data['total_commande'] ?? null;
                $data['remise'] = $data['remise_commande'] ?? null;

                // Add status label
                if (isset($data['status'])) {
                    $data['status_label'] = \App\Enums\CommandeStatus::from($data['status'])->label();
                }

                // Transform product data if loaded
                if (isset($data['produits']) && is_array($data['produits'])) {
                    foreach ($data['produits'] as &$produit) {
                        if (isset($produit['pivot'])) {
                            // Add calculated fields
                            $produit['quantite'] = $produit['pivot']['quantite'];
                            $produit['prix_unitaire'] = $produit['pivot']['prix_unitaire'];
                            $produit['sous_total'] = $produit['pivot']['quantite'] * $produit['pivot']['prix_unitaire'];

                            // Add standardized field names
                            $produit['nom'] = $produit['nom_produit'];
                            $produit['prix'] = $produit['prix_produit'];
                            $produit['description'] = $produit['description_produit'];
                        }
                    }
                }

                return $data;
            });

            return $this->paginationResponse($commandes, 'Commandes récupérées avec succès');
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->errorResponse('Utilisateur non trouvé', 404);
        } catch (\Exception $e) {
            return $this->errorResponse(
                'Problème de récupération des commandes',
                500,
                [],
                ['message' => $e->getMessage(), 'trace' => $e->getTraceAsString()]
            );
        }
    }
}

