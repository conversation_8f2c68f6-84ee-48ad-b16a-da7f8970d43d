<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Problème de paiement - Lack parisien</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600&family=Inter:wght@300;400;500&display=swap');

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            line-height: 1.7;
            color: #5a4a3a;
            max-width: 600px;
            margin: 0 auto;
            padding: 0;
            background: linear-gradient(135deg, #f8f6f0 0%, #f1ede4 100%);
            font-weight: 300;
        }

        .container {
            background-color: #ffffff;
            margin: 20px;
            border-radius: 0;
            box-shadow: 0 8px 32px rgba(139, 117, 95, 0.12);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #c8a882 0%, #b8956f 100%);
            color: #5a4a3a;
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="0.5" fill="%23ffffff" opacity="0.1"/><circle cx="75" cy="75" r="0.3" fill="%23ffffff" opacity="0.08"/><circle cx="50" cy="10" r="0.4" fill="%23ffffff" opacity="0.06"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
            opacity: 0.3;
        }

        .header h1 {
            margin: 0;
            font-family: 'Playfair Display', serif;
            font-size: 28px;
            font-weight: 600;
            letter-spacing: -0.5px;
            position: relative;
            z-index: 1;
        }

        .header p {
            margin: 8px 0 0 0;
            font-size: 14px;
            opacity: 0.8;
            font-weight: 400;
            position: relative;
            z-index: 1;
        }

        .content {
            padding: 40px 30px;
        }

        .error-info {
            background: linear-gradient(135deg, #fdf6f0 0%, #faf0e6 100%);
            padding: 30px;
            border-radius: 2px;
            margin-bottom: 30px;
            border-left: 3px solid #c8a882;
            position: relative;
        }

        .error-info h2 {
            margin-top: 0;
            color: #8b7560;
            font-family: 'Playfair Display', serif;
            font-size: 22px;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .error-info p {
            margin: 0;
            color: #6b5b4b;
            line-height: 1.6;
        }

        .order-details {
            background-color: #fbfaf8;
            padding: 30px;
            border-radius: 2px;
            margin-bottom: 30px;
            border: 1px solid #f0ede6;
        }

        .order-details h3 {
            margin-top: 0;
            color: #5a4a3a;
            font-family: 'Playfair Display', serif;
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 25px;
        }

        .field {
            margin-bottom: 20px;
            padding: 18px 20px;
            background-color: #ffffff;
            border-radius: 1px;
            border: 1px solid #f0ede6;
            transition: all 0.2s ease;
        }

        .field:hover {
            border-color: #d4c4a8;
            box-shadow: 0 2px 8px rgba(139, 117, 95, 0.08);
        }

        .field-label {
            font-weight: 500;
            color: #8b7560;
            margin-bottom: 8px;
            text-transform: uppercase;
            font-size: 11px;
            letter-spacing: 1.2px;
        }

        .field-value {
            font-size: 16px;
            color: #5a4a3a;
            font-weight: 400;
        }

        .amount {
            background: linear-gradient(135deg, #c8a882 0%, #b8956f 100%);
            color: #ffffff;
            padding: 25px;
            border-radius: 2px;
            text-align: center;
            font-size: 20px;
            font-weight: 500;
            margin: 30px 0;
            letter-spacing: 0.5px;
        }

        .error-reason {
            background: linear-gradient(135deg, #fdf6f0 0%, #faf0e6 100%);
            padding: 18px;
            border-radius: 2px;
            border: 1px solid #f0ede6;
            margin: 15px 0;
            color: #8b7560;
            font-style: italic;
        }

        .footer {
            margin-top: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #faf9f6 0%, #f5f3ee 100%);
            text-align: center;
            color: #8b7560;
            font-size: 13px;
            line-height: 1.6;
            border-top: 1px solid #f0ede6;
        }

        .footer strong {
            color: #5a4a3a;
            font-weight: 500;
        }

        .retry-steps {
            background: linear-gradient(135deg, #faf9f6 0%, #f5f3ee 100%);
            padding: 30px;
            border-radius: 2px;
            margin: 30px 0;
            border-left: 3px solid #d4c4a8;
        }

        .retry-steps h3 {
            color: #5a4a3a;
            margin-top: 0;
            font-family: 'Playfair Display', serif;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .retry-steps ol {
            margin: 0;
            padding-left: 20px;
            color: #6b5b4b;
        }

        .retry-steps li {
            margin-bottom: 12px;
            line-height: 1.6;
        }

        .retry-steps strong {
            color: #8b7560;
            font-weight: 500;
        }

        .contact-info {
            background: linear-gradient(135deg, #fdf8e8 0%, #faf5e0 100%);
            padding: 25px;
            border-radius: 2px;
            margin: 30px 0;
            border-left: 3px solid #d4c4a8;
        }

        .contact-info h4 {
            color: #8b7560;
            margin-top: 0;
            font-family: 'Playfair Display', serif;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .contact-info p {
            color: #6b5b4b;
            line-height: 1.6;
        }

        .retry-button {
            background: linear-gradient(135deg, #8b7560 0%, #7a6550 100%);
            color: #ffffff;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 2px;
            display: inline-block;
            margin: 10px 0;
            font-weight: 500;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        .retry-button:hover {
            background: linear-gradient(135deg, #7a6550 0%, #6b5b4b 100%);
            color: #ffffff;
            text-decoration: none;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(139, 117, 95, 0.3);
        }

        /* Mobile responsiveness */
        @media only screen and (max-width: 600px) {
            .container {
                margin: 10px;
            }

            .header,
            .content {
                padding: 30px 20px;
            }

            .error-info,
            .order-details,
            .retry-steps,
            .contact-info {
                padding: 20px;
            }

            .field {
                padding: 15px;
            }

            .header h1 {
                font-size: 24px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>Problème de Paiement</h1>
            <p>Lack parisien - Votre paiement n'a pas pu être traité</p>
        </div>

        <div class="content">
            <div class="error-info">
                <h2>Paiement non traité</h2>
                <p>Nous n'avons pas pu traiter votre paiement pour votre commande. Votre commande est toujours en
                    attente et aucun montant n'a été débité de votre compte.</p>
            </div>

            <div class="order-details">
                <h3>Détails de la commande</h3>

                <div class="field">
                    <div class="field-label">Numéro de commande</div>
                    <div class="field-value">#{{ $orderNumber }}</div>
                </div>

                <div class="field">
                    <div class="field-label">Tentative de paiement</div>
                    <div class="field-value">{{ $attemptedAt }}</div>
                </div>

                <div class="field">
                    <div class="field-label">Raison de l'échec</div>
                    <div class="field-value">
                        <div class="error-reason">{{ $failureReason }}</div>
                    </div>
                </div>
            </div>

            <div class="amount">
                Montant à payer: {{ number_format($orderTotal, 2) }}€
            </div>

            <div class="retry-steps">
                <h3>Comment procéder</h3>
                <ol>
                    <li><strong>Vérifiez vos informations :</strong> Assurez-vous que les détails de votre carte sont
                        corrects</li>
                    <li><strong>Vérifiez votre solde :</strong> Assurez-vous d'avoir suffisamment de fonds disponibles
                    </li>
                    <li><strong>Contactez votre banque :</strong> Votre banque peut avoir bloqué la transaction</li>
                    <li><strong>Réessayez le paiement :</strong> Vous pouvez tenter un nouveau paiement</li>
                    <li><strong>Utilisez une autre méthode :</strong> Essayez avec une autre carte ou méthode de
                        paiement</li>
                </ol>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <a href="#" class="retry-button">Réessayer le paiement</a>
            </div>

            <div class="contact-info">
                <h4>Besoin d'aide ?</h4>
                <p>Si le problème persiste, n'hésitez pas à contacter notre service client. Nous sommes là pour vous
                    aider à finaliser votre commande.</p>
            </div>
        </div>

        <div class="footer">
            <p><strong>Lack parisien</strong> - L'art de vivre à la française</p>
            <p>Tentative de paiement du {{ $attemptedAt }}</p>
            <p>Commande #{{ $orderNumber }} - En attente de paiement</p>
        </div>
    </div>
</body>

</html>