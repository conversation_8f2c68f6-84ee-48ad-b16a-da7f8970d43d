<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class StorePartnerRequestRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $user = Auth::user();
        $rules = [
            'company_name' => 'required|string|max:255',
            'business_type' => 'nullable|string|max:255',
            'motivation' => 'required|string',
            'website' => 'nullable|url|max:255',
            'phone' => 'required|string|max:20',
            'address' => 'required|string|max:255',
        ];
        if (!$user) {
            $rules['email'] = 'required|email|max:255';
            $rules['name'] = 'required|string|max:255';
        }
        return $rules;
    }

    public function messages()
    {
        return [
            'company_name.required' => 'Le nom de l\'entreprise est obligatoire.',
            'motivation.required' => 'La motivation est obligatoire.',
            'phone.required' => 'Le téléphone est obligatoire.',
            'address.required' => 'L\'adresse est obligatoire.',
            'email.required' => 'L\'email est obligatoire pour les utilisateurs non authentifiés.',
            'email.email' => 'L\'email doit être une adresse email valide.',
            'name.required' => 'Le nom est obligatoire pour les utilisateurs non authentifiés.',
        ];
    }
} 