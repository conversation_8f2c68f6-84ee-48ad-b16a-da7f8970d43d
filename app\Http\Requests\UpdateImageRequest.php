<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateImageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'is_primary' => 'nullable|in:true,false,0,1',
            'alt_text' => 'nullable|string|max:255',
            'title' => 'nullable|string|max:255',
            'order' => 'nullable|integer|min:0',
        ];
    }

    public function messages(): array
    {
        return [
            'is_primary.in' => 'La valeur de is_primary est invalide.',
            'alt_text.max' => 'Le texte alternatif ne doit pas dépasser 255 caractères.',
            'title.max' => 'Le titre ne doit pas dépasser 255 caractères.',
            'order.integer' => 'L\'ordre doit être un entier.',
            'order.min' => 'L\'ordre doit être au moins 0.',
        ];
    }
}
