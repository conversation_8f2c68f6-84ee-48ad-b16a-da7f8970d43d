<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class TestR2Connection extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:r2-connection';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the connection to Cloudflare R2';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info("R2 Connection Test");
        $this->info("=================");
        $this->newLine();

        try {
            // Afficher la configuration actuelle
            $this->info("Configuration S3 actuelle:");
            $this->line("Driver: " . config('filesystems.disks.s3.driver'));
            $this->line("Key: " . substr(config('filesystems.disks.s3.key'), 0, 5) . "...");
            $this->line("Secret: " . substr(config('filesystems.disks.s3.secret'), 0, 5) . "...");
            $this->line("Region: " . config('filesystems.disks.s3.region'));
            $this->line("Bucket: " . config('filesystems.disks.s3.bucket'));
            $this->line("URL: " . config('filesystems.disks.s3.url'));
            $this->line("Endpoint: " . config('filesystems.disks.s3.endpoint'));
            $this->line("Use Path Style Endpoint: " . (config('filesystems.disks.s3.use_path_style_endpoint') ? 'true' : 'false'));
            $this->line("Visibility: " . (config('filesystems.disks.s3.visibility') ?? 'non défini'));
            $this->newLine();

            // Test 1: Lister les fichiers
            $this->info("Test 1: Lister les fichiers");
            $files = Storage::disk('s3')->files();
            $this->line("Nombre de fichiers trouvés: " . count($files));
            
            if (count($files) > 0) {
                $this->line("Premiers fichiers:");
                foreach (array_slice($files, 0, 5) as $file) {
                    $this->line("- {$file}");
                }
            }
            $this->newLine();

            // Test 2: Vérifier si un fichier existe
            if (count($files) > 0) {
                $testFile = $files[0];
                $this->info("Test 2: Vérifier si le fichier existe: {$testFile}");
                $exists = Storage::disk('s3')->exists($testFile);
                $this->line("Le fichier existe: " . ($exists ? 'Oui' : 'Non'));
                $this->newLine();
            }

            // Test 3: Obtenir l'URL d'un fichier
            if (count($files) > 0) {
                $testFile = $files[0];
                $this->info("Test 3: Obtenir l'URL du fichier: {$testFile}");
                $url = Storage::disk('s3')->url($testFile);
                $this->line("URL: {$url}");
                $this->newLine();
            }

            // Test 4: Créer un fichier temporaire
            $this->info("Test 4: Créer un fichier temporaire");
            $tempFile = 'test_' . time() . '.txt';
            $content = 'Ceci est un test de connexion à Cloudflare R2 - ' . date('Y-m-d H:i:s');
            
            try {
                Storage::disk('s3')->put($tempFile, $content, 'public');
                $this->line("Fichier créé avec succès: {$tempFile}");
                
                // Vérifier si le fichier existe
                $exists = Storage::disk('s3')->exists($tempFile);
                $this->line("Le fichier existe: " . ($exists ? 'Oui' : 'Non'));
                
                // Obtenir l'URL du fichier
                $url = Storage::disk('s3')->url($tempFile);
                $this->line("URL: {$url}");
                
                // Supprimer le fichier
                Storage::disk('s3')->delete($tempFile);
                $this->line("Fichier supprimé avec succès");
                $this->newLine();
            } catch (\Exception $e) {
                $this->error("Erreur lors de la création du fichier: " . $e->getMessage());
                $this->newLine();
            }

            $this->info("Tous les tests sont terminés!");
            return 0;
        } catch (\Exception $e) {
            $this->error("Erreur: " . $e->getMessage());
            $this->line("Trace: " . $e->getTraceAsString());
            return 1;
        }
    }
}
