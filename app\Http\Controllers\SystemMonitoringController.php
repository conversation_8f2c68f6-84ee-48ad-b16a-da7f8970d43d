<?php

namespace App\Http\Controllers;

use App\Services\MonitoringService;
use App\Services\ApiResponseService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Cache;

class SystemMonitoringController extends Controller
{
    protected MonitoringService $monitoringService;
    protected ApiResponseService $apiResponse;

    public function __construct(MonitoringService $monitoringService, ApiResponseService $apiResponse)
    {
        $this->monitoringService = $monitoringService;
        $this->apiResponse = $apiResponse;

        // Apply rate limiting for monitoring endpoints
        $this->middleware('rate.limit:monitoring')->except(['ping']);
    }

    /**
     * Simple ping endpoint for basic health check
     */
    public function ping(): JsonResponse
    {
        return $this->apiResponse->success([
            'status' => 'ok',
            'timestamp' => now()->toISOString(),
            'version' => config('app.version', '1.0.0')
        ]);
    }

    /**
     * Get comprehensive system health
     */
    public function health(): JsonResponse
    {
        try {
            $health = $this->monitoringService->generateHealthReport();

            return $this->apiResponse->success($health);

        } catch (\Exception $e) {
            return $this->apiResponse->error(
                'Failed to generate health report',
                500,
                ['error' => $e->getMessage()]
            );
        }
    }

    /**
     * Get system metrics
     */
    public function metrics(): JsonResponse
    {
        try {
            $metrics = $this->monitoringService->getSystemHealth();

            return $this->apiResponse->success([
                'timestamp' => now()->toISOString(),
                'metrics' => $metrics
            ]);

        } catch (\Exception $e) {
            return $this->apiResponse->error(
                'Failed to collect metrics',
                500,
                ['error' => $e->getMessage()]
            );
        }
    }

    /**
     * Get performance metrics
     */
    public function performance(): JsonResponse
    {
        try {
            $metrics = [
                'memory' => $this->monitoringService->getSystemHealth()['memory'],
                'performance' => $this->monitoringService->getSystemHealth()['performance'],
                'cache_stats' => $this->getCacheStatistics(),
                'queue_stats' => $this->getQueueStatistics(),
            ];

            return $this->apiResponse->success($metrics);

        } catch (\Exception $e) {
            return $this->apiResponse->error(
                'Failed to collect performance metrics',
                500,
                ['error' => $e->getMessage()]
            );
        }
    }

    /**
     * Get security metrics
     */
    public function security(): JsonResponse
    {
        try {
            $securityMetrics = $this->monitoringService->getSystemHealth()['security'];

            $additionalMetrics = [
                'recent_blocked_ips' => $this->getRecentBlockedIps(),
                'security_events_summary' => $this->getSecurityEventsSummary(),
                'threat_levels' => $this->getThreatLevels(),
            ];

            return $this->apiResponse->success(array_merge($securityMetrics, $additionalMetrics));

        } catch (\Exception $e) {
            return $this->apiResponse->error(
                'Failed to collect security metrics',
                500,
                ['error' => $e->getMessage()]
            );
        }
    }

    /**
     * Get application status summary
     */
    public function status(): JsonResponse
    {
        try {
            $health = $this->monitoringService->generateHealthReport();

            $summary = [
                'overall_status' => $health['overall_status'],
                'timestamp' => $health['timestamp'],
                'uptime' => $this->getApplicationUptime(),
                'version' => config('app.version', '1.0.0'),
                'environment' => config('app.env'),
                'components_summary' => $this->getComponentsSummary($health['components']),
                'issues_count' => count($health['issues']),
                'critical_issues' => $this->getCriticalIssues($health['components'])
            ];

            return $this->apiResponse->success($summary);

        } catch (\Exception $e) {
            return $this->apiResponse->error(
                'Failed to get application status',
                500,
                ['error' => $e->getMessage()]
            );
        }
    }

    /**
     * Get cache statistics
     */
    private function getCacheStatistics(): array
    {
        return [
            'driver' => config('cache.default'),
            'hit_rate' => Cache::get('cache:hit_rate', 0),
            'total_operations' => Cache::get('cache:total_operations', 0),
            'memory_usage' => Cache::get('cache:memory_usage', 'unknown')
        ];
    }

    /**
     * Get queue statistics
     */
    private function getQueueStatistics(): array
    {
        $queues = ['default', 'emails', 'images', 'search', 'payments'];
        $stats = [];

        foreach ($queues as $queue) {
            $stats[$queue] = [
                'pending' => Cache::get("queue:{$queue}:pending", 0),
                'processed_last_hour' => Cache::get("queue:{$queue}:processed_hour", 0),
                'failed_last_hour' => Cache::get("queue:{$queue}:failed_hour", 0)
            ];
        }

        return $stats;
    }

    /**
     * Get recent blocked IPs
     */
    private function getRecentBlockedIps(): array
    {
        $blockedIps = Cache::get('security:recent_blocked_ips', []);

        return array_map(function ($ip) {
            return [
                'ip' => $ip['ip'],
                'reason' => $ip['reason'],
                'blocked_at' => $ip['blocked_at'],
                'expires_at' => $ip['expires_at']
            ];
        }, array_slice($blockedIps, 0, 10)); // Last 10 blocked IPs
    }

    /**
     * Get security events summary
     */
    private function getSecurityEventsSummary(): array
    {
        return [
            'sql_injection_attempts' => Cache::get('security:sql_injection_count', 0),
            'xss_attempts' => Cache::get('security:xss_attempts_count', 0),
            'brute_force_attempts' => Cache::get('security:brute_force_count', 0),
            'suspicious_user_agents' => Cache::get('security:suspicious_ua_count', 0),
            'rate_limit_violations' => Cache::get('security:rate_limit_violations', 0)
        ];
    }

    /**
     * Get threat levels
     */
    private function getThreatLevels(): array
    {
        $securityEvents = $this->getSecurityEventsSummary();
        $totalEvents = array_sum($securityEvents);

        if ($totalEvents > 100) {
            $level = 'high';
        } elseif ($totalEvents > 50) {
            $level = 'medium';
        } elseif ($totalEvents > 10) {
            $level = 'low';
        } else {
            $level = 'minimal';
        }

        return [
            'current_level' => $level,
            'total_events_last_hour' => $totalEvents,
            'risk_score' => min(100, $totalEvents * 2), // Simple risk scoring
            'recommendations' => $this->getSecurityRecommendations($level)
        ];
    }

    /**
     * Get security recommendations based on threat level
     */
    private function getSecurityRecommendations(string $level): array
    {
        return match ($level) {
            'high' => [
                'Consider implementing additional rate limiting',
                'Review and block suspicious IP addresses',
                'Enable additional security monitoring',
                'Consider maintenance mode if attacks persist'
            ],
            'medium' => [
                'Monitor security logs closely',
                'Review rate limiting configurations',
                'Check for patterns in attacks'
            ],
            'low' => [
                'Continue regular monitoring',
                'Review security logs periodically'
            ],
            default => [
                'Maintain current security posture',
                'Regular security reviews'
            ]
        };
    }

    /**
     * Get application uptime
     */
    private function getApplicationUptime(): array
    {
        $startTime = Cache::get('app:start_time');
        if (!$startTime) {
            $startTime = now();
            Cache::put('app:start_time', $startTime, 86400 * 30); // 30 days
        }

        $uptime = now()->diffInSeconds($startTime);

        return [
            'seconds' => $uptime,
            'human_readable' => $this->formatUptime($uptime),
            'started_at' => $startTime->toISOString()
        ];
    }

    /**
     * Format uptime in human readable format
     */
    private function formatUptime(int $seconds): string
    {
        $days = floor($seconds / 86400);
        $hours = floor(($seconds % 86400) / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $secs = $seconds % 60;

        return sprintf('%dd %dh %dm %ds', $days, $hours, $minutes, $secs);
    }

    /**
     * Get components summary
     */
    private function getComponentsSummary(array $components): array
    {
        $summary = [
            'healthy' => 0,
            'warning' => 0,
            'unhealthy' => 0
        ];

        foreach ($components as $component) {
            if (is_array($component) && isset($component['status'])) {
                $status = $component['status'];
                if (isset($summary[$status])) {
                    $summary[$status]++;
                }
            }
        }

        return $summary;
    }

    /**
     * Get critical issues
     */
    private function getCriticalIssues(array $components): array
    {
        $critical = [];

        foreach ($components as $name => $component) {
            if (is_array($component) && isset($component['status'])) {
                if ($component['status'] === 'unhealthy') {
                    $critical[] = [
                        'component' => $name,
                        'error' => $component['error'] ?? 'Unknown error',
                        'details' => $component
                    ];
                }
            }
        }

        return $critical;
    }
}
