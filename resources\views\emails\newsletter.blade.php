<html>

<head>
    <meta charset="UTF-8">
    <title>Newsletter - Nouveautés & Promotions - Lack parisien</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600&family=Inter:wght@300;400;500&display=swap');

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            color: #5a4a3a;
            background: linear-gradient(135deg, #f8f6f0 0%, #f1ede4 100%);
            margin: 0;
            padding: 0;
            font-weight: 300;
            line-height: 1.7;
        }

        .container {
            max-width: 600px;
            margin: 20px auto;
            background: #ffffff;
            border-radius: 0;
            box-shadow: 0 8px 32px rgba(139, 117, 95, 0.12);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #d4c4a8 0%, #c8b896 100%);
            color: #5a4a3a;
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="0.5" fill="%23ffffff" opacity="0.1"/><circle cx="75" cy="75" r="0.3" fill="%23ffffff" opacity="0.08"/><circle cx="50" cy="10" r="0.4" fill="%23ffffff" opacity="0.06"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
            opacity: 0.3;
        }

        .content {
            padding: 40px 30px;
        }

        h1 {
            color: #5a4a3a;
            font-family: 'Playfair Display', serif;
            font-size: 32px;
            font-weight: 600;
            margin: 0;
            letter-spacing: -0.5px;
            position: relative;
            z-index: 1;
        }

        .header p {
            margin: 8px 0 0 0;
            font-size: 14px;
            opacity: 0.8;
            font-weight: 400;
            position: relative;
            z-index: 1;
        }

        h2 {
            color: #8b7560;
            margin-top: 40px;
            margin-bottom: 25px;
            font-family: 'Playfair Display', serif;
            font-size: 24px;
            font-weight: 600;
            border-bottom: 1px solid #f0ede6;
            padding-bottom: 15px;
        }

        .button {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #8b7560 0%, #7a6550 100%);
            color: #ffffff !important;
            text-decoration: none;
            border-radius: 2px;
            font-weight: 500;
            font-size: 16px;
            margin-top: 30px;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        .button:hover {
            background: linear-gradient(135deg, #7a6550 0%, #6b5b4b 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(139, 117, 95, 0.3);
        }

        .item-block {
            margin-bottom: 30px;
            background: linear-gradient(135deg, #faf9f6 0%, #f5f3ee 100%);
            border-radius: 2px;
            padding: 25px;
            border-left: 3px solid #d4c4a8;
            display: flex;
            align-items: flex-start;
            gap: 20px;
        }

        .item-img {
            max-width: 120px;
            max-height: 90px;
            border-radius: 2px;
            border: 1px solid #f0ede6;
            background: #ffffff;
            object-fit: cover;
        }

        .item-content {
            flex: 1;
        }

        .item-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 8px;
            color: #5a4a3a;
            font-family: 'Playfair Display', serif;
        }

        .item-desc {
            margin: 8px 0 0 0;
            color: #6b5b4b;
            line-height: 1.6;
        }

        .item-meta {
            color: #8b7560;
            font-size: 13px;
            margin-top: 8px;
            font-weight: 400;
        }

        .footer {
            margin-top: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #faf9f6 0%, #f5f3ee 100%);
            text-align: center;
            color: #8b7560;
            font-size: 13px;
            line-height: 1.6;
            border-top: 1px solid #f0ede6;
        }

        .footer strong {
            color: #5a4a3a;
            font-weight: 500;
        }

        /* Mobile responsiveness */
        @media only screen and (max-width: 600px) {
            .container {
                margin: 10px;
            }

            .header,
            .content {
                padding: 30px 20px;
            }

            .item-block {
                flex-direction: column;
                align-items: stretch;
                padding: 20px;
            }

            .item-img {
                max-width: 100%;
                max-height: 120px;
                margin-bottom: 15px;
            }

            h1 {
                font-size: 28px;
            }

            h2 {
                font-size: 20px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>Newsletter</h1>
            <p>Lack parisien - Nouveautés & Promotions</p>
        </div>

        <div class="content">
            <p>Bonjour,</p>
            <p>Découvrez les dernières nouveautés de notre collection et nos offres exclusives :</p>

            <h2>Nouvelles Promotions</h2>
            @if(isset($promotions) && count($promotions))
                @foreach($promotions as $promo)
                    <div class="item-block">
                        @if($promo->image_url)
                            <img src="{{ $promo->image_url }}" alt="Image promotion" class="item-img">
                        @endif
                        <div class="item-content">
                            <div class="item-title">{{ $promo->titre ?? $promo->nom ?? 'Promotion' }}</div>
                            <div class="item-desc">{{ $promo->description ?? '' }}</div>
                            <div class="item-meta">
                                @if(isset($promo->date_debut) && isset($promo->date_fin))
                                    <span>Du {{ \Carbon\Carbon::parse($promo->date_debut)->format('d/m/Y') }} au
                                        {{ \Carbon\Carbon::parse($promo->date_fin)->format('d/m/Y') }}</span>
                                @endif
                                @if(isset($promo->type))<span> • Type: {{ $promo->type }}</span>@endif
                                @if(isset($promo->valeur))<span> • Valeur: {{ $promo->valeur }}</span>@endif
                            </div>
                        </div>
                    </div>
                @endforeach
            @else
                <p>Aucune nouvelle promotion pour le moment.</p>
            @endif

            <h2>Nouvelles Collections</h2>
            @if(isset($collections) && count($collections))
                @foreach($collections as $collection)
                    <div class="item-block">
                        @if($collection->primary_image_url)
                            <img src="{{ $collection->primary_image_url }}" alt="Image collection" class="item-img">
                        @endif
                        <div class="item-content">
                            <div class="item-title">{{ $collection->nom ?? $collection->titre ?? 'Collection' }}</div>
                            <div class="item-desc">{{ $collection->description ?? '' }}</div>
                            <div class="item-meta">
                                @if(isset($collection->date_debut) && isset($collection->date_fin))
                                    <span>Du {{ \Carbon\Carbon::parse($collection->date_debut)->format('d/m/Y') }} au
                                        {{ \Carbon\Carbon::parse($collection->date_fin)->format('d/m/Y') }}</span>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            @else
                <p>Aucune nouvelle collection pour le moment.</p>
            @endif

            <div style="text-align: center; margin: 40px 0;">
                <a href="{{ config('app.url') }}" class="button">Découvrir la boutique</a>
            </div>
        </div>

        <div class="footer">
            <p><strong>Lack parisien</strong> - L'art de vivre à la française</p>
            <p>Merci de votre fidélité et de votre confiance</p>
        </div>
    </div>
</body>

</html>