<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\SousCategorie;

class SousCategorieController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = SousCategorie::query();
        if ($request->has('categorie_id')) {
            $query->where('categorie_id', $request->input('categorie_id'));
        }
        $sous_categories = $query->get();
        return response()->json($sous_categories);
    }

    /**
     * Show the form for creating a new resource.
     */


    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $sous_categorie = new SousCategorie([
                "nom_sous_categorie" => $request->input("nom_sous_categorie"),
                "description_sous_categorie" => $request->input("description_sous_categorie"),
                "categorie_id" => $request->input("categorie_id")
            ]);
            $sous_categorie->save();
            return response()->json($sous_categorie, 201);

        } catch (\Exception $e) {
            return response()->json(["error" => "probleme d'insertion {$e->getMessage()}"]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $sous_categorie = SousCategorie::find($id);
        if ($sous_categorie) {
            return response()->json($sous_categorie);
        } else {
            return response()->json(["error" => "sous_categorie non trouvée"], 404);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */


    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $sous_categorie = SousCategorie::findOrFail($id);
            $sous_categorie->update($request->all());
            return response()->json($sous_categorie);
        } catch (\Exception $e) {
            return response()->json(["error" => "probleme de modification {$e->getMessage()}"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $sous_categorie = SousCategorie::findOrFail($id);
            $sous_categorie->delete();
            return response()->json(["message" => "sous_categorie supprimée avec succès"], 200);
        } catch (\Exception $e) {
            return response()->json(["error" => "probleme de suppression de sous_categorie {$e->getMessage()}"]);
        }
    }

    /**
     * Get all subcategories for a specific category
     *
     * @param string $categorieId The ID of the category
     * @return \Illuminate\Http\JsonResponse
     */
    public function getByCategorieId(string $categorieId)
    {
        try {
            $sousCategories = SousCategorie::where('categorie_id', $categorieId)->get();

            if ($sousCategories->isEmpty()) {
                return response()->json([
                    "message" => "Aucune sous-catégorie trouvée pour cette catégorie"
                ], 404);
            }

            return response()->json($sousCategories);
        } catch (\Exception $e) {
            return response()->json([
                "error" => "Problème de récupération des sous-catégories",
                "message" => $e->getMessage()
            ], 500);
        }
    }
}
