<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('commandes', function (Blueprint $table) {
            // Fix data types for monetary fields
            if (Schema::hasColumn('commandes', 'total_commande')) {
                $table->decimal('total_commande', 10, 2)->change();
            }
            if (Schema::hasColumn('commandes', 'remise_commande')) {
                $table->decimal('remise_commande', 5, 2)->default(0)->change();
            }

            // Add missing fields that the model expects
            if (!Schema::hasColumn('commandes', 'numero_commande')) {
                $table->string('numero_commande')->unique()->nullable()->after('user_id');
            }
            if (!Schema::hasColumn('commandes', 'status')) {
                $table->enum('status', ['en_attente', 'confirmee', 'en_preparation', 'expediee', 'livree', 'annulee', 'remboursee'])
                    ->default('en_attente')->after('numero_commande');
            }
            if (!Schema::hasColumn('commandes', 'payment_status')) {
                $table->enum('payment_status', ['pending', 'paid', 'failed', 'refunded'])
                    ->default('pending')->after('status');
            }
            if (!Schema::hasColumn('commandes', 'subtotal')) {
                $table->decimal('subtotal', 10, 2)->default(0)->after('payment_status');
            }
            if (!Schema::hasColumn('commandes', 'tax_amount')) {
                $table->decimal('tax_amount', 10, 2)->default(0)->after('subtotal');
            }
            if (!Schema::hasColumn('commandes', 'shipping_cost')) {
                $table->decimal('shipping_cost', 10, 2)->default(0)->after('tax_amount');
            }
            if (!Schema::hasColumn('commandes', 'discount_amount')) {
                $table->decimal('discount_amount', 10, 2)->default(0)->after('shipping_cost');
            }
            if (!Schema::hasColumn('commandes', 'shipping_address')) {
                $table->json('shipping_address')->nullable()->after('discount_amount');
            }
            if (!Schema::hasColumn('commandes', 'billing_address')) {
                $table->json('billing_address')->nullable()->after('shipping_address');
            }
            if (!Schema::hasColumn('commandes', 'notes')) {
                $table->text('notes')->nullable()->after('billing_address');
            }
            if (!Schema::hasColumn('commandes', 'code_promo')) {
                $table->string('code_promo')->nullable()->after('notes');
            }
            if (!Schema::hasColumn('commandes', 'methode_paiement')) {
                $table->string('methode_paiement')->nullable()->after('code_promo');
            }
            if (!Schema::hasColumn('commandes', 'date_commande')) {
                $table->timestamp('date_commande')->nullable()->after('methode_paiement');
            }
            if (!Schema::hasColumn('commandes', 'confirmed_at')) {
                $table->timestamp('confirmed_at')->nullable()->after('date_commande');
            }
            if (!Schema::hasColumn('commandes', 'preparation_started_at')) {
                $table->timestamp('preparation_started_at')->nullable()->after('confirmed_at');
            }
            if (!Schema::hasColumn('commandes', 'shipped_at')) {
                $table->timestamp('shipped_at')->nullable()->after('preparation_started_at');
            }
            if (!Schema::hasColumn('commandes', 'delivered_at')) {
                $table->timestamp('delivered_at')->nullable()->after('shipped_at');
            }
            if (!Schema::hasColumn('commandes', 'cancelled_at')) {
                $table->timestamp('cancelled_at')->nullable()->after('delivered_at');
            }
            if (!Schema::hasColumn('commandes', 'refunded_at')) {
                $table->timestamp('refunded_at')->nullable()->after('cancelled_at');
            }

            // Rename existing address fields to be more specific
            if (Schema::hasColumn('commandes', 'adresse_commande') && !Schema::hasColumn('commandes', 'shipping_street')) {
                $table->renameColumn('adresse_commande', 'shipping_street');
            }
            if (Schema::hasColumn('commandes', 'ville_commande') && !Schema::hasColumn('commandes', 'shipping_city')) {
                $table->renameColumn('ville_commande', 'shipping_city');
            }
            if (Schema::hasColumn('commandes', 'code_postal_commande') && !Schema::hasColumn('commandes', 'shipping_postal_code')) {
                $table->renameColumn('code_postal_commande', 'shipping_postal_code');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('commandes', function (Blueprint $table) {
            // Revert data type changes
            $table->string('total_commande')->change();
            $table->string('remise_commande')->change();

            // Remove added columns
            $table->dropColumn([
                'numero_commande',
                'status',
                'payment_status',
                'subtotal',
                'tax_amount',
                'shipping_cost',
                'discount_amount',
                'shipping_address',
                'billing_address',
                'notes',
                'code_promo',
                'methode_paiement',
                'date_commande',
                'confirmed_at',
                'preparation_started_at',
                'shipped_at',
                'delivered_at',
                'cancelled_at',
                'refunded_at'
            ]);

            // Revert column renames
            $table->renameColumn('shipping_street', 'adresse_commande');
            $table->renameColumn('shipping_city', 'ville_commande');
            $table->renameColumn('shipping_postal_code', 'code_postal_commande');
        });
    }
};
