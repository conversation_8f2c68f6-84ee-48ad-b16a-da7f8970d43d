<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SetProduitAttributsRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'attributs' => 'required|array',
            'attributs.*.attribut_id' => 'required|exists:attributs,id',
            'attributs.*.valeur' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'attributs.required' => 'Le champ attributs est obligatoire.',
            'attributs.array' => 'Le champ attributs doit être un tableau.',
            'attributs.*.attribut_id.required' => 'L\'ID de l\'attribut est obligatoire.',
            'attributs.*.attribut_id.exists' => 'L\'attribut sélectionné est invalide.',
            'attributs.*.valeur.required' => 'La valeur de l\'attribut est obligatoire.',
        ];
    }
} 