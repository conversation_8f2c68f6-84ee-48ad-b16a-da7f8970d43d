<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\KeycloakService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;

class KeycloakVerificationController extends Controller
{
    protected KeycloakService $keycloakService;

    public function __construct(KeycloakService $keycloakService)
    {
        $this->keycloakService = $keycloakService;
    }

    /**
     * Verify tokens from frontend and set cookies
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function verify(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'access_token' => 'required|string',
            'refresh_token' => 'required|string',
            'id_token' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            // Validate the token
            $token = $request->input('access_token');
            $decoded = $this->keycloakService->validateToken($token);

            // Convert decoded token object to array
            $keycloakUser = [
                'sub' => $decoded->sub,
                'name' => $decoded->name,
                'email' => $decoded->email
            ];

            // Extract roles from token
            $realmRoles = $decoded->realm_access->roles ?? [];

            // Extract client-specific roles if available
            $clientRoles = [];
            $clientId = config('services.keycloak.client_id');
            if (isset($decoded->resource_access->$clientId)) {
                $clientRoles = $decoded->resource_access->$clientId->roles ?? [];
            }

            // Combine all roles
            $roles = array_unique(array_merge($realmRoles, $clientRoles));

            // Find or create user
            $user = User::syncWithKeycloak($keycloakUser, $roles);

            // Create response
            $response = response()->json([
                'message' => 'Authentication successful',
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                ],
                'roles' => $roles,
            ]);

            // Add cookies to response
            $response->cookie('access_token', $request->input('access_token'), 60, '/', null, true, true, false, 'strict');
            $response->cookie('refresh_token', $request->input('refresh_token'), 60, '/', null, true, true, false, 'strict');

            if ($request->input('id_token')) {
                $response->cookie('id_token', $request->input('id_token'), 60, '/', null, true, true, false, 'strict');
            }

            return $response;
        } catch (Exception $e) {
            return response()->json(['error' => $e->getMessage()], 401);
        }
    }

    /**
     * Refresh token endpoint
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function refresh(Request $request): JsonResponse
    {
        $refreshToken = $request->cookie('refresh_token');

        if (!$refreshToken) {
            return response()->json(['error' => 'No refresh token available'], 401);
        }

        try {
            $tokens = $this->keycloakService->refreshToken($refreshToken);

            if (!$tokens) {
                $response = response()->json(['error' => 'Failed to refresh token'], 401);
                $response->cookie('access_token', '', -1);
                $response->cookie('refresh_token', '', -1);
                $response->cookie('id_token', '', -1);
                return $response;
            }

            $response = response()->json(['message' => 'Token refreshed successfully']);

            $response->cookie('access_token', $tokens['access_token'], 60, '/', null, true, true, false, 'strict');
            $response->cookie('refresh_token', $tokens['refresh_token'], 60, '/', null, true, true, false, 'strict');

            if (isset($tokens['id_token'])) {
                $response->cookie('id_token', $tokens['id_token'], 60, '/', null, true, true, false, 'strict');
            }

            return $response;
        } catch (Exception $e) {
            $response = response()->json(['error' => $e->getMessage()], 401);
            $response->cookie('access_token', '', -1);
            $response->cookie('refresh_token', '', -1);
            $response->cookie('id_token', '', -1);
            return $response;
        }
    }

    /**
     * Log the user out
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function logout(Request $request): JsonResponse
    {
        $refreshToken = $request->cookie('refresh_token');

        if ($refreshToken) {
            try {
                Http::asForm()->post(
                    sprintf(
                        '%s/realms/%s/protocol/openid-connect/logout',
                        config('services.keycloak.base_url'),
                        config('services.keycloak.realms')
                    ),
                    [
                        'client_id' => config('services.keycloak.client_id'),
                        'client_secret' => config('services.keycloak.client_secret'),
                        'refresh_token' => $refreshToken,
                    ]
                );
            } catch (Exception $e) {
                report($e);
            }
        }

        $response = response()->json(['message' => 'Logged out successfully']);
        $response->cookie('access_token', '', -1);
        $response->cookie('refresh_token', '', -1);
        $response->cookie('id_token', '', -1);

        return $response;
    }

    /**
     * Get the current authenticated user
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function user(Request $request): JsonResponse
    {
        // Try to get token from cookie first
        $token = $request->cookie('access_token');

        // If not in cookie, try to get from Authorization header
        if (!$token && $request->hasHeader('Authorization')) {
            $authHeader = $request->header('Authorization');
            if (strpos($authHeader, 'Bearer ') === 0) {
                $token = substr($authHeader, 7);
            }
        }

        if (!$token) {
            return response()->json(['error' => 'Unauthenticated'], 401);
        }

        try {
            $decoded = $this->keycloakService->validateToken($token);

            // Find the user
            $user = User::where('keycloak_id', $decoded->sub)->first();

            if (!$user) {
                return response()->json(['error' => 'User not found'], 404);
            }

            return response()->json([
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                ],
                'roles' => $this->extractRolesFromToken($decoded),
            ]);
        } catch (Exception $e) {
            return response()->json(['error' => $e->getMessage()], 401);
        }
    }

    /**
     * Extract all roles from a Keycloak token
     *
     * @param object $decoded
     * @return array
     */
    protected function extractRolesFromToken(object $decoded): array
    {
        // Extract realm roles
        $realmRoles = $decoded->realm_access->roles ?? [];

        // Extract client-specific roles if available
        $clientRoles = [];
        $clientId = config('services.keycloak.client_id');
        if (isset($decoded->resource_access->$clientId)) {
            $clientRoles = $decoded->resource_access->$clientId->roles ?? [];
        }

        // Combine all roles
        return array_unique(array_merge($realmRoles, $clientRoles));
    }


}
