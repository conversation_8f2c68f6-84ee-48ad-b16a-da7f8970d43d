<?php

namespace App\Services\Keycloak;

use Exception;
use Firebase\JWT\JWT;
use Firebase\JWT\JWK;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\Services\EnhancedJwtSecurityService;

class KeycloakTokenService
{
    private const KEYS_CACHE_KEY = 'keycloak_public_keys';
    private const KEYS_CACHE_TTL = 300; // 5 minutes

    private EnhancedJwtSecurityService $securityService;

    public function __construct(EnhancedJwtSecurityService $securityService)
    {
        $this->securityService = $securityService;
    }

    public function validateToken(string $token): object
    {
        return $this->securityService->enhancedValidateToken($token, function ($token) {
            return $this->performBasicValidation($token);
        });
    }

    private function performBasicValidation(string $token): object
    {
        try {
            $keys = $this->getKeycloakPublicKeys();

            // First decode the token without verification to get the header
            $tokenParts = explode('.', $token);
            if (count($tokenParts) !== 3) {
                throw new Exception('Invalid token format');
            }

            // Decode header to get the key ID (kid)
            $header = json_decode(base64_decode($tokenParts[0]), true);
            if (!isset($header['kid'])) {
                throw new Exception('No "kid" found in token header');
            }

            // Find the matching key
            $matchingKey = null;
            $tokenKid = $header['kid'];

            // Log available keys and the kid we're looking for
            \Log::debug('Matching token kid with available keys', [
                'token_kid' => $tokenKid,
                'available_kids' => array_column($keys, 'kid')
            ]);

            foreach ($keys as $key) {
                if ($key['kid'] === $tokenKid) {
                    $matchingKey = $key;
                    break;
                }
            }

            if (!$matchingKey) {
                throw new Exception("No matching key found for token (kid: {$tokenKid})");
            }

            // Create a keyset with only the matching key
            $keySet = ['keys' => [$matchingKey]];

            // Now decode and verify the token
            $decodedToken = JWT::decode($token, JWK::parseKeySet($keySet));

            // Validate issuer and expiration
            $this->validateIssuer($decodedToken);
            $this->validateExpiration($decodedToken);

            return $decodedToken;
        } catch (Exception $e) {
            // Get token details for better error reporting
            $tokenDetails = $this->getTokenDetails($token);

            throw new Exception('Token validation error: ' . $e->getMessage() .
                ' (Expected issuer: ' . $this->getKeycloakIssuerUrl() .
                ', Actual issuer: ' . ($tokenDetails['iss'] ?? 'none') . ')');
        }
    }

    private function getTokenDetails(string $token): array
    {
        try {
            $parts = explode('.', $token);
            if (count($parts) === 3) {
                return json_decode(base64_decode($parts[1]), true) ?? [];
            }
        } catch (Exception $e) {
            // Silently fail and return empty array
        }
        return [];
    }

    public function refreshPublicKeys(): array
    {
        Cache::forget(self::KEYS_CACHE_KEY);
        return $this->getKeycloakPublicKeys();
    }

    private function getKeycloakPublicKeys(): array
    {
        return Cache::remember(self::KEYS_CACHE_KEY, self::KEYS_CACHE_TTL, function () {
            $response = Http::get($this->getKeycloakCertsUrl());

            if (!$response->successful()) {
                throw new Exception('Failed to fetch Keycloak public keys: ' . $response->body());
            }

            $keys = $response->json();

            // Add logging to debug the keys
            \Log::debug('Fetched Keycloak public keys', [
                'url' => $this->getKeycloakCertsUrl(),
                'keys' => $keys
            ]);

            return $keys['keys'] ?? throw new Exception('Invalid public keys format from Keycloak');
        });
    }

    private function validateIssuer(object $token): void
    {
        $expectedIssuer = $this->getKeycloakIssuerUrl();
        if (!isset($token->iss)) {
            throw new Exception("Token missing issuer claim");
        }
        if ($token->iss !== $expectedIssuer) {
            throw new Exception("Invalid token issuer. Expected: {$expectedIssuer}, Got: {$token->iss}");
        }
    }

    private function validateExpiration(object $token): void
    {
        if (!isset($token->exp)) {
            throw new Exception('Token missing expiration claim');
        }
        if (time() >= $token->exp) {
            throw new Exception('Expired token');
        }
    }

    private function getKeycloakCertsUrl(): string
    {
        $baseUrl = rtrim(config('services.keycloak.base_url'), '/');
        $realm = config('services.keycloak.realm');

        return "{$baseUrl}/realms/{$realm}/protocol/openid-connect/certs";
    }

    private function getKeycloakIssuerUrl(): string
    {
        $baseUrl = rtrim(config('services.keycloak.base_url'), '/');
        $realm = config('services.keycloak.realm');

        return "{$baseUrl}/realms/{$realm}";
    }
}




