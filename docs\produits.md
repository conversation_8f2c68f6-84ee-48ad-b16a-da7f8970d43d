# Gestion des Produits

## Introduction

Le système permet de gérer un catalogue de produits avec leurs attributs, catégories, sous-catégories et marques associées.

## Endpoints API

### Récupérer tous les produits

```http
GET /api/produits
```

Retourne la liste des produits avec pagination et filtres.

#### Paramètres de filtrage (optionnels)

| Paramètre              | Type    | Description                                       |
|------------------------|---------|-------------------------------------------------|
| marque_id              | integer | Filtrer par ID de marque                         |
| marque                 | string  | Filtrer par nom de marque (recherche partielle)  |
| sous_sous_categorie_id | integer | Filtrer par ID de sous-sous-catégorie           |
| sous_categorie_id      | integer | Filtrer par ID de sous-catégorie                |
| categorie_id           | integer | Filtrer par ID de catégorie                     |
| nom                    | string  | Filtrer par nom de produit (recherche partielle) |
| prix_min               | decimal | Prix minimum                                     |
| prix_max               | decimal | Prix maximum                                     |
| disponible             | boolean | Filtrer par disponibilité en stock (quantité > 0) |
| collection_id          | integer | Filtrer par ID de collection                     |
| en_promotion           | boolean | Filtrer les produits en promotion active         |

> **Note sur le filtrage par promotion** : Le paramètre `en_promotion=true` filtre les produits qui ont au moins une promotion active dont les dates de validité incluent la date actuelle. Le système vérifie à la fois les dates de la promotion elle-même et les dates spécifiques à l'association produit-promotion.

#### Paramètres de pagination et tri

| Paramètre      | Type    | Description                                                 | Valeur par défaut |
|-----------------|---------|-------------------------------------------------------------|-------------------|
| page            | integer | Numéro de page                                             | 1                 |
| per_page        | integer | Nombre d'éléments par page (max 100)                      | 15                |
| sort_by         | string  | Champ de tri (id, nom_produit, prix_produit, etc.)          | id                |
| sort_direction  | string  | Direction du tri (asc, desc)                                | asc               |
| with            | string  | Relations à charger (marque,sousSousCategorie,collections) | -                 |

#### Exemple de réponse (paginée)

```json
{
  "current_page": 1,
  "data": [
    {
      "id": 1,
      "nom_produit": "Laptop Pro X",
      "description_produit": "Ordinateur portable haute performance",
      "prix_produit": 1299.99,
      "quantite_produit": 15,
      "marque_id": 1,
      "sous_sous_categorie_id": 3,
      "image_produit": "laptop_pro_x.jpg",
      "reference": "1-1",
      "created_at": "2025-03-15T10:30:00.000000Z",
      "updated_at": "2025-03-15T10:30:00.000000Z",
      "marque": {
        "id": 1,
        "nom_marque": "TechPro",
        "logo_marque": "techpro_logo.png"
      },
      "sousSousCategorie": {
        "id": 3,
        "nom": "Ordinateurs portables",
        "sous_categorie_id": 2
      }
    },
    {
      "id": 2,
      "nom_produit": "Smartphone XYZ",
      "description_produit": "Smartphone dernière génération",
      "prix_produit": 499.99,
      "quantite_produit": 25,
      "marque_id": 2,
      "sous_sous_categorie_id": 5,
      "image_produit": "smartphone_xyz.jpg",
      "reference": "2-2",
      "created_at": "2025-03-16T14:20:00.000000Z",
      "updated_at": "2025-03-16T14:20:00.000000Z",
      "marque": {
        "id": 2,
        "nom_marque": "MobileTech",
        "logo_marque": "mobiletech_logo.png"
      },
      "sousSousCategorie": {
        "id": 5,
        "nom": "Smartphones",
        "sous_categorie_id": 3
      }
    }
  ],
  "first_page_url": "http://localhost:8000/api/produits?page=1",
  "from": 1,
  "last_page": 5,
  "last_page_url": "http://localhost:8000/api/produits?page=5",
  "links": [
    {
      "url": null,
      "label": "&laquo; Précédent",
      "active": false
    },
    {
      "url": "http://localhost:8000/api/produits?page=1",
      "label": "1",
      "active": true
    },
    {
      "url": "http://localhost:8000/api/produits?page=2",
      "label": "2",
      "active": false
    },
    {
      "url": "http://localhost:8000/api/produits?page=3",
      "label": "3",
      "active": false
    },
    {
      "url": "http://localhost:8000/api/produits?page=2",
      "label": "Suivant &raquo;",
      "active": false
    }
  ],
  "next_page_url": "http://localhost:8000/api/produits?page=2",
  "path": "http://localhost:8000/api/produits",
  "per_page": 15,
  "prev_page_url": null,
  "to": 15,
  "total": 68
}
```

### Récupérer un produit spécifique

```http
GET /api/produits/{id}
```

Retourne les détails d'un produit spécifique avec ses attributs.

#### Exemple de réponse

```json
{
  "id": 1,
  "nom": "Laptop Pro X",
  "description": "Ordinateur portable haute performance",
  "prix": 1299.99,
  "quantite_stock": 15,
  "marque_id": 1,
  "sous_categorie_id": 3,
  "image": "laptop_pro_x.jpg",
  "created_at": "2025-03-15T10:30:00.000000Z",
  "updated_at": "2025-03-15T10:30:00.000000Z",
  "marque": {
    "id": 1,
    "nom": "TechPro",
    "logo": "techpro_logo.png"
  },
  "sous_categorie": {
    "id": 3,
    "nom": "Ordinateurs portables",
    "categorie_id": 1,
    "categorie": {
      "id": 1,
      "nom": "Informatique",
      "image": "informatique.jpg"
    }
  },
  "attributs": [
    {
      "id": 1,
      "nom": "Processeur",
      "valeur": "Intel Core i7"
    },
    {
      "id": 2,
      "nom": "Mémoire RAM", 
      "valeur": "16 Go"
    },
    {
      "id": 3,
      "nom": "Stockage",
      "valeur": "512 Go SSD"
    }
  ]
}
```

### Créer un nouveau produit

```http
POST /api/produits
```

Crée un nouveau produit.

#### Paramètres de la requête

| Paramètre        | Type    | Description                                |
|------------------|---------|--------------------------------------------|
| nom              | string  | Nom du produit                             |
| description      | string  | Description du produit                     |
| prix             | decimal | Prix du produit                            |
| quantite_stock   | integer | Quantité disponible en stock               |
| marque_id        | integer | ID de la marque                            |
| sous_categorie_id| integer | ID de la sous-catégorie                    |
| image            | string  | Nom du fichier image                       |
| attributs | array   | Tableau des attributs du produit    |

#### Exemple de requête

```json
{
  "nom": "Tablette Ultra",
  "description": "Tablette tactile haute résolution",
  "prix": 349.99,
  "quantite_stock": 30,
  "marque_id": 2,
  "sous_categorie_id": 6,
  "image": "tablette_ultra.jpg",
  "attributs": [
    {
      "attribut_id": 4,
      "valeur": "10 pouces"
    },
    {
      "attribut_id": 5,
      "valeur": "64 Go"
    }
  ]
}
```

### Mettre à jour un produit

```http
PUT /api/produits/{id}
```

Met à jour les informations d'un produit existant.

#### Paramètres de la requête

Mêmes paramètres que pour la création, tous optionnels.

### Supprimer un produit

```http
DELETE /api/produits/{id}
```

Supprime un produit existant.

### Récupérer les produits par marque

```http
GET /api/marques/{id}/produits
```

Retourne tous les produits d'une marque spécifique.

#### Exemple de réponse

```json
[
  {
    "id": 1,
    "nom": "Laptop Pro X",
    "description": "Ordinateur portable haute performance",
    "prix": 1299.99,
    "quantite_stock": 15,
    "marque_id": 1,
    "sous_categorie_id": 3,
    "image": "laptop_pro_x.jpg"
  },
  {
    "id": 3,
    "nom": "Desktop Power",
    "description": "Ordinateur de bureau puissant",
    "prix": 899.99,
    "quantite_stock": 10,
    "marque_id": 1,
    "sous_categorie_id": 4,
    "image": "desktop_power.jpg"
  }
]
```

## Gestion des attributs

Les attributs des produits sont gérés via les endpoints suivants:

### Récupérer les attributs d'un produit

```http
GET /api/produits/{id}/attributs
```

### Définir les attributs d'un produit

```http
POST /api/produits/{id}/attributs
```

#### Paramètres de la requête

```json
{
  "attributs": [
    {
      "attribut_id": 4,
      "valeur": "10 pouces"
    },
    {
      "attribut_id": 5,
      "valeur": "64 Go"
    }
  ]
}
```

### Mettre à jour un attribut spécifique d'un produit

```http
PUT /api/produits/{id}/attributs/{attributId}
```

#### Paramètres de la requête

```json
{
  "valeur": "12 pouces"
}
```

### Supprimer un attribut d'un produit

```http
DELETE /api/produits/{id}/attributs/{attributId}
```

### Récupérer les attributs filtrables

```http
GET /api/attributs/filtrables
```

Retourne la liste des attributs qui peuvent être utilisés pour filtrer les produits, organisés par groupe.
