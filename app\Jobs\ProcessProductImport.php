<?php

namespace App\Jobs;

use App\Models\Produit;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessProductImport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 600; // 10 minutes timeout
    public $tries = 2;

    protected $products;
    protected $batchSize;

    public function __construct(array $products, int $batchSize = 50)
    {
        $this->products = $products;
        $this->batchSize = $batchSize;
        $this->onQueue('imports'); // Use dedicated queue for imports
    }

    public function handle(): void
    {
        Log::info('Starting product import batch', [
            'product_count' => count($this->products),
            'batch_size' => $this->batchSize
        ]);

        $chunks = array_chunk($this->products, $this->batchSize);
        $processed = 0;
        $failed = 0;

        foreach ($chunks as $chunk) {
            foreach ($chunk as $productData) {
                try {
                    // Create the product
                    $product = Produit::create([
                        'nom_produit' => $productData['nom_produit'],
                        'description' => $productData['description'] ?? '',
                        'prix_produit' => $productData['prix_produit'],
                        'stock' => $productData['stock'] ?? 0,
                        'marque_id' => $productData['marque_id'] ?? null,
                        'sous_sous_categorie_id' => $productData['sous_sous_categorie_id'] ?? null,
                        'active' => $productData['active'] ?? true,
                        // Add other fields as needed
                    ]);

                    // Process images in background if provided
                    if (isset($productData['images']) && !empty($productData['images'])) {
                        ProcessProductImages::dispatch($product, $productData['images'])
                            ->delay(now()->addSeconds(5)); // Small delay to prevent overwhelming
                    }

                    // Update search index in background
                    UpdateSearchIndex::dispatch($product)
                        ->delay(now()->addSeconds(10));

                    $processed++;

                } catch (\Exception $e) {
                    $failed++;
                    Log::error('Failed to import product', [
                        'product_data' => $productData,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            // Small delay between chunks
            usleep(500000); // 0.5 second
        }

        Log::info('Product import batch completed', [
            'processed' => $processed,
            'failed' => $failed,
            'total' => count($this->products)
        ]);
    }

    public function failed(\Throwable $exception): void
    {
        Log::error('Product import job failed permanently', [
            'product_count' => count($this->products),
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
