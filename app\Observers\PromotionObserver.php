<?php

namespace App\Observers;

use App\Models\Promotion;
use App\Services\CacheService;
use Illuminate\Support\Facades\Cache;

class PromotionObserver
{
    protected $cacheService;

    public function __construct(CacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    /**
     * Handle the Promotion "created" event.
     */
    public function created(Promotion $promotion): void
    {
        $this->invalidatePromotionCaches();
    }

    /**
     * Handle the Promotion "updated" event.
     */
    public function updated(Promotion $promotion): void
    {
        $this->invalidatePromotionCaches();

        // Clear specific promotion cache
        Cache::forget('promotion_' . $promotion->id);
    }

    /**
     * Handle the Promotion "deleted" event.
     */
    public function deleted(Promotion $promotion): void
    {
        $this->invalidatePromotionCaches();

        // Clear specific promotion cache
        Cache::forget('promotion_' . $promotion->id);
    }

    /**
     * Handle the Promotion "restored" event.
     */
    public function restored(Promotion $promotion): void
    {
        $this->invalidatePromotionCaches();
    }

    /**
     * Handle the Promotion "force deleted" event.
     */
    public function forceDeleted(Promotion $promotion): void
    {
        $this->invalidatePromotionCaches();

        // Clear specific promotion cache
        Cache::forget('promotion_' . $promotion->id);
    }

    /**
     * Invalidate promotion-related caches
     */
    protected function invalidatePromotionCaches(): void
    {
        // Clear active promotions cache
        $this->cacheService->forgetActivePromotions();

        // Clear product lists cache (since promotions affect product listings)
        Cache::flush(); // This might be too aggressive - consider more targeted approach
    }
}
