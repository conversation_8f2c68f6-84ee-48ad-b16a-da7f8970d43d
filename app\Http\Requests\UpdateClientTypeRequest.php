<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateClientTypeRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'type_client' => [
                'required',
                Rule::in(['normal', 'partenaire', 'point_de_vente', 'groupe', 'standard', 'premium', 'affilie', 'groupe'])
            ],
            'point_de_vente_id' => 'nullable|exists:points_de_vente,id',
            'groupe_client_id' => 'nullable|exists:groupes_clients,id',
        ];
    }

    public function messages()
    {
        return [
            'type_client.required' => 'Le type de client est obligatoire.',
            'type_client.in' => 'Le type de client est invalide.',
            'point_de_vente_id.exists' => 'Le point de vente sélectionné est invalide.',
            'groupe_client_id.exists' => 'Le groupe client sélectionné est invalide.',
        ];
    }
} 