# TND to EUR Currency Conversion for Stripe Payments

## Overview

This implementation provides automatic currency conversion from Tunisian Dinars (TND) to Euros (EUR) for Stripe payment processing, since Stripe doesn't natively support TND.

## 🌟 Features

- **Automatic TND to EUR Conversion**: Seamlessly converts TND amounts to EUR for Stripe processing
- **Multiple Exchange Rate Sources**: Uses multiple APIs with fallback mechanisms
- **Real-time Exchange Rates**: Fetches current exchange rates with 1-hour caching
- **Comprehensive Logging**: Tracks all conversion activities and rates used
- **Database Storage**: Stores both original and converted amounts with conversion metadata
- **Fallback Rate**: Uses a fallback rate if all APIs are unavailable
- **Manual Rate Override**: Allows manual exchange rate setting for testing

## 🏗️ Architecture

### Components

1. **CurrencyConversionService**: Core service handling currency conversion logic
2. **PaiementController**: Updated to handle currency conversion in payment flow
3. **Database Schema**: Extended to store conversion data
4. **API Endpoints**: Enhanced Stripe payment endpoints with conversion support

### Flow Diagram

```
TND Payment Request
    ↓
Check if TND currency
    ↓
Fetch TND→EUR rate (with caching)
    ↓
Convert amount to EUR
    ↓
Create Stripe Payment Intent (EUR)
    ↓
Store conversion data in database
    ↓
Return payment intent with conversion info
```

## 🔧 Configuration

### Environment Variables

Add these to your `.env` file:

```bash
# Optional: Exchange rate API keys (for better rate accuracy)
FREECURRENCY_API_KEY=your_free_currency_api_key
CURRENCYLAYER_API_KEY=your_currency_layer_api_key

# Optional: Custom fallback rate (default: 0.305)
TND_EUR_FALLBACK_RATE=0.305
```

### Exchange Rate APIs

The system supports multiple exchange rate APIs with automatic fallback:

1. **ExchangeRate-API** (Free, no key required)
2. **FreeCurrencyAPI** (Free tier available)
3. **CurrencyLayer** (Free tier available)
4. **Fallback Rate** (0.305 TND = 1 EUR)

## 📊 Database Schema

### New Fields in `paiements` Table

```sql
-- Original currency information
original_currency VARCHAR(3) DEFAULT 'TND'
converted_amount DECIMAL(10,2) NULL
stripe_currency VARCHAR(3) NULL
exchange_rate DECIMAL(10,6) NULL
conversion_timestamp TIMESTAMP NULL
```

## 🚀 API Usage

### Create Payment Intent with TND

```javascript
// Request
POST /api/stripe/create-payment-intent
{
    "amount": 100.00,
    "currency": "TND",
    "commande_id": 32,
    "customer_email": "<EMAIL>"
}

// Response
{
    "success": true,
    "client_secret": "pi_xxx_secret_xxx",
    "payment_intent_id": "pi_3RUQSUD1WIE8Jd791BTJcyRV",
    "amount": 100,
    "currency": "TND",
    "stripe_amount": 29.6,
    "stripe_currency": "eur",
    "conversion_data": {
        "original_amount": 100,
        "original_currency": "TND",
        "converted_amount": 29.6,
        "converted_currency": "EUR",
        "exchange_rate": 0.296,
        "conversion_timestamp": "2025-05-30T10:46:39.468548Z"
    },
    "paiement_id": 7
}
```

### Create Payment Intent with EUR

```javascript
// Request
POST /api/stripe/create-payment-intent
{
    "amount": 50.00,
    "currency": "EUR",
    "commande_id": 32,
    "customer_email": "<EMAIL>"
}

// Response
{
    "success": true,
    "client_secret": "pi_xxx_secret_xxx",
    "payment_intent_id": "pi_3RUQSYD1WIE8Jd790k8OWXO9",
    "amount": 50,
    "currency": "EUR",
    "stripe_amount": 50,
    "stripe_currency": "eur",
    "conversion_data": null,
    "paiement_id": 8
}
```

## 💻 Frontend Integration

### JavaScript Example with Conversion Display

```javascript
async function createPaymentIntent(amount, currency = 'TND') {
    try {
        const response = await fetch('/api/stripe/create-payment-intent', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                amount: amount,
                currency: currency,
                commande_id: orderId,
                customer_email: customerEmail
            })
        });

        const data = await response.json();
        
        if (data.success) {
            // Display conversion information to user
            if (data.conversion_data) {
                displayConversionInfo(data);
            }
            
            // Initialize Stripe Elements
            return initializeStripePayment(data.client_secret);
        } else {
            throw new Error(data.message || 'Payment initialization failed');
        }
    } catch (error) {
        console.error('Payment error:', error);
        throw error;
    }
}

function displayConversionInfo(paymentData) {
    const conversionInfo = document.getElementById('conversion-info');
    const { conversion_data } = paymentData;
    
    conversionInfo.innerHTML = `
        <div class="conversion-notice">
            <h4>💱 Currency Conversion Applied</h4>
            <p><strong>Original Amount:</strong> ${conversion_data.original_amount} ${conversion_data.original_currency}</p>
            <p><strong>Converted Amount:</strong> €${conversion_data.converted_amount}</p>
            <p><strong>Exchange Rate:</strong> 1 TND = €${conversion_data.exchange_rate}</p>
            <p><small>Rate updated: ${new Date(conversion_data.conversion_timestamp).toLocaleString()}</small></p>
        </div>
    `;
}
```

### React Example

```jsx
import React, { useState, useEffect } from 'react';

const PaymentForm = ({ orderId, customerEmail }) => {
    const [amount, setAmount] = useState('');
    const [currency, setCurrency] = useState('TND');
    const [conversionData, setConversionData] = useState(null);
    const [loading, setLoading] = useState(false);

    const createPaymentIntent = async () => {
        setLoading(true);
        try {
            const response = await fetch('/api/stripe/create-payment-intent', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    amount: parseFloat(amount),
                    currency,
                    commande_id: orderId,
                    customer_email: customerEmail
                })
            });

            const data = await response.json();
            
            if (data.success) {
                setConversionData(data.conversion_data);
                // Initialize Stripe payment...
            }
        } catch (error) {
            console.error('Payment error:', error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="payment-form">
            <div className="form-group">
                <label>Amount:</label>
                <input
                    type="number"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    placeholder="Enter amount"
                />
            </div>
            
            <div className="form-group">
                <label>Currency:</label>
                <select value={currency} onChange={(e) => setCurrency(e.target.value)}>
                    <option value="TND">TND (Tunisian Dinar)</option>
                    <option value="EUR">EUR (Euro)</option>
                    <option value="USD">USD (US Dollar)</option>
                </select>
            </div>

            {conversionData && (
                <div className="conversion-info">
                    <h4>💱 Currency Conversion</h4>
                    <p>Original: {conversionData.original_amount} {conversionData.original_currency}</p>
                    <p>Converted: €{conversionData.converted_amount}</p>
                    <p>Rate: 1 TND = €{conversionData.exchange_rate}</p>
                </div>
            )}

            <button onClick={createPaymentIntent} disabled={loading}>
                {loading ? 'Processing...' : 'Create Payment'}
            </button>
        </div>
    );
};
```

## 🔒 Security Considerations

1. **API Rate Limiting**: Exchange rate APIs have rate limits
2. **Fallback Strategy**: Always have a fallback rate for service availability
3. **Input Validation**: Validate currency codes and amounts
4. **Logging**: Log all conversion activities for audit trails
5. **Cache TTL**: Balance between rate accuracy and API calls

## 🧪 Testing

### Run Currency Conversion Tests

```bash
php test_currency_conversion.php
```

### Test Scenarios

1. **TND to EUR Conversion**: ✅ Automatic conversion applied
2. **EUR Direct Processing**: ✅ No conversion needed
3. **Unsupported Currency**: ✅ Proper error handling
4. **Exchange Rate Caching**: ✅ Cached rates used efficiently
5. **Fallback Rate**: ✅ Used when APIs unavailable

### Manual Testing

```php
// Test conversion service directly
$service = new App\Services\CurrencyConversionService();

// Convert 100 TND to EUR
$result = $service->convertTndToEur(100);
echo "100 TND = €{$result['converted_amount']} (Rate: {$result['exchange_rate']})";

// Set manual rate for testing
$service->setManualExchangeRate(0.30);

// Clear cache
$service->clearCache();
```

## 📈 Monitoring and Maintenance

### Key Metrics to Monitor

1. **Conversion Success Rate**: Track successful vs failed conversions
2. **Exchange Rate Accuracy**: Compare with multiple sources
3. **API Response Times**: Monitor exchange rate API performance
4. **Cache Hit Rate**: Monitor how often cached rates are used
5. **Fallback Usage**: Track when fallback rates are used

### Maintenance Tasks

1. **Update Fallback Rate**: Regularly update the fallback exchange rate
2. **Monitor API Limits**: Keep track of API usage limits
3. **Review Conversion Logs**: Analyze conversion patterns and issues
4. **Update Supported Currencies**: Add more currencies as needed

## 🎯 Future Enhancements

1. **Multi-Currency Support**: Add support for more currencies
2. **Historical Rate Tracking**: Store historical exchange rates
3. **Rate Alerts**: Notify when rates change significantly
4. **Admin Dashboard**: Web interface for managing exchange rates
5. **Webhook Integration**: Real-time rate updates via webhooks
6. **A/B Testing**: Test different rate sources for accuracy

## 📚 References

- [Stripe Supported Currencies](https://stripe.com/docs/currencies)
- [Exchange Rate API Documentation](https://exchangerate-api.com/docs)
- [Laravel HTTP Client](https://laravel.com/docs/http-client)
- [Stripe Payment Intents](https://stripe.com/docs/payments/payment-intents)

---

**Status**: ✅ **COMPLETED** - Currency conversion system fully implemented and tested
**Last Updated**: May 30, 2025
**Test Results**: All currency conversion tests passing ✅
