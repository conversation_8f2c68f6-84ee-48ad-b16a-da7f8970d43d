<?php

namespace App\Jobs;

use App\Models\Produit;
use App\Services\ImageService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessProductImages implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 300; // 5 minutes timeout
    public $tries = 3;

    protected $product;
    protected $images;

    public function __construct(Produit $product, array $images)
    {
        $this->product = $product;
        $this->images = $images;
        $this->onQueue('images'); // Use dedicated queue for image processing
    }

    public function handle(ImageService $imageService): void
    {
        Log::info('Processing images for product', [
            'product_id' => $this->product->id,
            'image_count' => count($this->images)
        ]);

        try {
            foreach ($this->images as $index => $imageData) {
                $imageService->processAndStore($this->product, $imageData, $index);

                // Add a small delay to prevent overwhelming the server
                usleep(100000); // 0.1 second
            }

            Log::info('Images processed successfully', [
                'product_id' => $this->product->id,
                'processed_count' => count($this->images)
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to process product images', [
                'product_id' => $this->product->id,
                'error' => $e->getMessage()
            ]);

            throw $e; // Re-throw to trigger retry mechanism
        }
    }

    public function failed(\Throwable $exception): void
    {
        Log::error('Product image processing job failed permanently', [
            'product_id' => $this->product->id,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
