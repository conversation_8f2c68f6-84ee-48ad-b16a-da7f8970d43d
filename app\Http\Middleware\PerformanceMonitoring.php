<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class PerformanceMonitoring
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $start = microtime(true);
        $startMemory = memory_get_usage();
        $startPeakMemory = memory_get_peak_usage();

        // Enable query logging for this request if in debug mode
        if (config('app.debug')) {
            \DB::enableQueryLog();
        }

        $response = $next($request);

        $duration = (microtime(true) - $start) * 1000; // Convert to milliseconds
        $memoryUsed = memory_get_usage() - $startMemory;
        $peakMemoryUsed = memory_get_peak_usage() - $startPeakMemory;

        // Get query information if available
        $queryCount = 0;
        $queryTime = 0;
        if (config('app.debug')) {
            $queries = \DB::getQueryLog();
            $queryCount = count($queries);
            $queryTime = array_sum(array_column($queries, 'time'));
        }

        // Performance metrics
        $metrics = [
            'duration_ms' => round($duration, 2),
            'memory_mb' => round($memoryUsed / 1024 / 1024, 2),
            'peak_memory_mb' => round($peakMemoryUsed / 1024 / 1024, 2),
            'query_count' => $queryCount,
            'query_time_ms' => round($queryTime, 2),
        ];

        // Add performance headers for debugging
        if (config('app.debug') || $request->header('X-Debug-Performance')) {
            $response->headers->add([
                'X-Response-Time' => $metrics['duration_ms'] . 'ms',
                'X-Memory-Usage' => $metrics['memory_mb'] . 'MB',
                'X-Peak-Memory' => $metrics['peak_memory_mb'] . 'MB',
                'X-Query-Count' => $metrics['query_count'],
                'X-Query-Time' => $metrics['query_time_ms'] . 'ms',
            ]);
        }

        // Log slow requests (>1 second)
        if ($duration > 1000) {
            Log::warning('Slow request detected', array_merge($metrics, [
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'route' => $request->route()?->getName(),
                'user_id' => auth()->id(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]));
        }

        // Log high memory usage (>50MB)
        if ($peakMemoryUsed > 50 * 1024 * 1024) {
            Log::warning('High memory usage detected', array_merge($metrics, [
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'route' => $request->route()?->getName(),
                'user_id' => auth()->id(),
            ]));
        }

        // Log excessive database queries (>50 queries)
        if ($queryCount > 50) {
            Log::warning('Excessive database queries detected', array_merge($metrics, [
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'route' => $request->route()?->getName(),
                'user_id' => auth()->id(),
            ]));
        }

        // Log performance metrics for monitoring (optional - only in production with monitoring service)
        if (config('app.env') === 'production' && config('services.monitoring.enabled', false)) {
            $this->sendMetricsToMonitoring($request, $metrics);
        }

        return $response;
    }

    /**
     * Send metrics to monitoring service
     */
    protected function sendMetricsToMonitoring(Request $request, array $metrics): void
    {
        // This would integrate with your monitoring service
        // Examples: DataDog, New Relic, CloudWatch, etc.

        try {
            // Example implementation for future use:
            // MonitoringService::gauge('request.duration', $metrics['duration_ms'], [
            //     'route' => $request->route()?->getName(),
            //     'method' => $request->method(),
            //     'status' => $response->getStatusCode(),
            // ]);

            // For now, just log at info level for collection
            Log::info('Performance metrics', array_merge($metrics, [
                'route' => $request->route()?->getName(),
                'method' => $request->method(),
                'endpoint' => $request->path(),
            ]));

        } catch (\Exception $e) {
            // Don't let monitoring failures affect the request
            Log::error('Failed to send metrics to monitoring service', [
                'error' => $e->getMessage()
            ]);
        }
    }
}
