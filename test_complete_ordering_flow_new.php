<?php

/**
 * Complete Ordering Flow Test
 * Tests the entire ordering process including performance
 */

class CompleteOrderingFlowTest
{
    private $baseUrl;
    private $results = [];
    
    public function __construct($baseUrl = 'http://localhost:8000/api')
    {
        $this->baseUrl = rtrim($baseUrl, '/');
    }
    
    public function runCompleteTest()
    {
        echo "🛒 Complete Ordering Flow Test\n";
        echo "==============================\n\n";
        
        // Test 1: Currency conversion endpoints
        $this->testCurrencyEndpoints();
        
        // Test 2: Payment intent with currency conversion
        $this->testPaymentIntentWithConversion();
        
        // Test 3: Performance test
        $this->testPerformance();
        
        // Test 4: Edge cases
        $this->testEdgeCases();
        
        $this->displayResults();
    }
    
    private function testCurrencyEndpoints()
    {
        echo "💱 Test 1: Currency Conversion Endpoints\n";
        echo "----------------------------------------\n";
        
        try {
            // Test exchange rate endpoint
            $response = $this->makeRequest('GET', '/test-exchange-rate');
            
            if ($response && isset($response['success']) && $response['success']) {
                $rate = $response['rate'];
                echo "✅ Exchange Rate: $rate\n";
                echo "   Source: {$response['source']}\n";
                echo "   Cached: " . ($response['cached'] ? 'Yes' : 'No') . "\n";
                $this->results['exchange_rate'] = 'PASS';
            } else {
                echo "❌ Failed to get exchange rate\n";
                $this->results['exchange_rate'] = 'FAIL';
            }
            
            // Test conversion endpoint
            $conversionData = [
                'amount' => 250.00,
                'from_currency' => 'TND',
                'to_currency' => 'EUR'
            ];
            
            $response = $this->makeRequest('POST', '/test-currency-conversion', $conversionData);
            
            if ($response && isset($response['success']) && $response['success']) {
                $converted = $response['conversion_data']['converted_amount'];
                $cents = $response['stripe_amount_cents'];
                echo "✅ Conversion Test: 250 TND → $converted EUR → $cents cents\n";
                
                if ($cents >= 7000 && $cents <= 8000) {
                    echo "✅ Amount is in correct range\n";
                    $this->results['conversion'] = 'PASS';
                } else {
                    echo "❌ Amount out of expected range\n";
                    $this->results['conversion'] = 'FAIL';
                }
            } else {
                echo "❌ Failed conversion test\n";
                $this->results['conversion'] = 'FAIL';
            }
            
        } catch (Exception $e) {
            echo "❌ Error: " . $e->getMessage() . "\n";
            $this->results['currency_endpoints'] = 'ERROR';
        }
        
        echo "\n";
    }
    
    private function testPaymentIntentWithConversion()
    {
        echo "💳 Test 2: Payment Intent with Currency Conversion\n";
        echo "--------------------------------------------------\n";
        
        try {
            // Test the exact scenario from the issue report
            $paymentData = [
                'amount' => 25000, // Frontend sends 250.00 * 100
                'currency' => 'eur',
                'original_currency' => 'TND',
                'original_amount' => 250.00,
                'commande_id' => 1, // Use dummy order ID
                'customer_email' => '<EMAIL>'
            ];
            
            $response = $this->makeRequest('POST', '/stripe/create-payment-intent', $paymentData);
            
            if ($response && isset($response['success']) && $response['success']) {
                echo "✅ Payment Intent created successfully\n";
                echo "   Payment Intent ID: {$response['payment_intent_id']}\n";
                echo "   Original: {$response['amount']} {$response['currency']}\n";
                echo "   Stripe: {$response['stripe_amount_cents']} cents ({$response['stripe_amount_eur']} EUR)\n";
                
                if (isset($response['conversion_data'])) {
                    $conv = $response['conversion_data'];
                    echo "   Exchange Rate: {$conv['exchange_rate']}\n";
                    echo "   Converted: {$conv['converted_amount']} {$conv['converted_currency']}\n";
                }
                
                // Verify the fix
                $stripeCents = $response['stripe_amount_cents'];
                if ($stripeCents >= 7000 && $stripeCents <= 8000) {
                    echo "✅ CURRENCY ISSUE FIXED: Correct amount ($stripeCents cents)\n";
                    $this->results['payment_intent'] = 'PASS';
                } else if ($stripeCents > 700000) {
                    echo "❌ CURRENCY ISSUE PERSISTS: Amount too high ($stripeCents cents)\n";
                    $this->results['payment_intent'] = 'FAIL';
                } else {
                    echo "⚠️  Unexpected amount: $stripeCents cents\n";
                    $this->results['payment_intent'] = 'PARTIAL';
                }
                
            } else {
                echo "❌ Failed to create payment intent\n";
                if (isset($response['message'])) {
                    echo "   Error: {$response['message']}\n";
                }
                $this->results['payment_intent'] = 'FAIL';
            }
            
        } catch (Exception $e) {
            echo "❌ Error: " . $e->getMessage() . "\n";
            $this->results['payment_intent'] = 'ERROR';
        }
        
        echo "\n";
    }
    
    private function testPerformance()
    {
        echo "⚡ Test 3: Performance Testing\n";
        echo "-----------------------------\n";
        
        $testCount = 5;
        $successCount = 0;
        $totalTime = 0;
        
        echo "Running $testCount performance tests...\n";
        
        for ($i = 1; $i <= $testCount; $i++) {
            $startTime = microtime(true);
            
            try {
                $response = $this->makeRequest('POST', '/test-currency-conversion', [
                    'amount' => 100.00,
                    'from_currency' => 'TND',
                    'to_currency' => 'EUR'
                ]);
                
                $endTime = microtime(true);
                $requestTime = $endTime - $startTime;
                $totalTime += $requestTime;
                
                if ($response && isset($response['success']) && $response['success']) {
                    $successCount++;
                    echo "  Test $i: ✅ " . number_format($requestTime * 1000, 2) . "ms\n";
                } else {
                    echo "  Test $i: ❌ Failed\n";
                }
                
            } catch (Exception $e) {
                echo "  Test $i: ❌ Error\n";
            }
        }
        
        $avgTime = $totalTime / $testCount;
        $successRate = ($successCount / $testCount) * 100;
        
        echo "\nPerformance Results:\n";
        echo "  Average Time: " . number_format($avgTime * 1000, 2) . "ms\n";
        echo "  Success Rate: $successCount/$testCount ($successRate%)\n";
        
        if ($avgTime < 1.0 && $successRate >= 80) {
            echo "✅ Performance is good\n";
            $this->results['performance'] = 'PASS';
        } else {
            echo "⚠️  Performance could be improved\n";
            $this->results['performance'] = 'PARTIAL';
        }
        
        echo "\n";
    }
    
    private function testEdgeCases()
    {
        echo "🔍 Test 4: Edge Cases\n";
        echo "--------------------\n";
        
        $edgeCases = [
            ['amount' => 0.01, 'description' => 'Very small amount'],
            ['amount' => 10000.00, 'description' => 'Large amount'],
            ['amount' => 999.99, 'description' => 'Decimal precision']
        ];
        
        $passCount = 0;
        
        foreach ($edgeCases as $case) {
            try {
                $response = $this->makeRequest('POST', '/test-currency-conversion', [
                    'amount' => $case['amount'],
                    'from_currency' => 'TND',
                    'to_currency' => 'EUR'
                ]);
                
                if ($response && isset($response['success']) && $response['success']) {
                    $converted = $response['conversion_data']['converted_amount'];
                    echo "  ✅ {$case['description']}: {$case['amount']} TND → $converted EUR\n";
                    $passCount++;
                } else {
                    echo "  ❌ {$case['description']}: Failed\n";
                }
                
            } catch (Exception $e) {
                echo "  ❌ {$case['description']}: Error\n";
            }
        }
        
        $this->results['edge_cases'] = $passCount === count($edgeCases) ? 'PASS' : 'PARTIAL';
        echo "\n";
    }
    
    private function makeRequest($method, $endpoint, $data = null)
    {
        $url = $this->baseUrl . $endpoint;
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        
        if ($method === 'POST' && $data) {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($response === false || $httpCode >= 400) {
            return null;
        }
        
        return json_decode($response, true);
    }
    
    private function displayResults()
    {
        echo "📊 Test Results Summary\n";
        echo "======================\n";
        
        $totalTests = count($this->results);
        $passedTests = 0;
        
        foreach ($this->results as $test => $result) {
            $icon = match($result) {
                'PASS' => '✅',
                'FAIL' => '❌',
                'PARTIAL' => '⚠️',
                'ERROR' => '💥',
                'SKIP' => '⏭️',
                default => '❓'
            };
            
            if ($result === 'PASS') $passedTests++;
            
            echo "$icon " . ucwords(str_replace('_', ' ', $test)) . ": $result\n";
        }
        
        echo "\nOverall Score: $passedTests/$totalTests tests passed\n";
        
        if ($passedTests === $totalTests) {
            echo "🎉 All tests passed! The ordering system is working correctly.\n";
        } else if ($passedTests >= $totalTests * 0.8) {
            echo "✅ Most tests passed. System is functional with minor issues.\n";
        } else {
            echo "⚠️  Several tests failed. Please review the implementation.\n";
        }
        
        echo "\n🎯 Currency Conversion Fix Status:\n";
        if (isset($this->results['payment_intent']) && $this->results['payment_intent'] === 'PASS') {
            echo "✅ FIXED: Currency conversion issue has been resolved!\n";
            echo "   Customers will now be charged the correct amount.\n";
        } else {
            echo "❌ Currency conversion issue may still exist.\n";
        }
    }
}

// Run the test
$tester = new CompleteOrderingFlowTest();
$tester->runCompleteTest();
