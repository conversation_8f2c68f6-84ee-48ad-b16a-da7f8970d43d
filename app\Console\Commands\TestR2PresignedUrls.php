<?php

namespace App\Console\Commands;

use App\Models\Image;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class TestR2PresignedUrls extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:r2-presigned-urls';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the generation of presigned URLs for Cloudflare R2';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info("R2 Presigned URLs Test");
        $this->info("=====================");
        $this->newLine();

        try {
            // Test 1: Générer une URL présignée directement avec Storage
            $this->info("Test 1: Générer une URL présignée directement avec Storage");
            
            // Récupérer un fichier existant
            $files = Storage::disk('s3')->files();
            
            if (count($files) === 0) {
                $this->error("Aucun fichier trouvé dans le bucket R2.");
                return 1;
            }
            
            $testFile = $files[0];
            $this->line("Fichier de test: {$testFile}");
            
            // Générer une URL présignée
            $presignedUrl = Storage::disk('s3')->temporaryUrl(
                $testFile,
                now()->addMinutes(5)
            );
            
            $this->line("URL présignée: {$presignedUrl}");
            $this->newLine();
            
            // Test 2: Récupérer une URL présignée via le modèle Image
            $this->info("Test 2: Récupérer une URL présignée via le modèle Image");
            
            $image = Image::first();
            if (!$image) {
                $this->error("Aucune image trouvée dans la base de données.");
                return 1;
            }
            
            $this->line("Image: {$image->path}");
            $this->line("URL présignée via getUrlAttribute: {$image->url}");
            $this->newLine();
            
            // Test 3: Récupérer une URL présignée pour une miniature
            $this->info("Test 3: Récupérer une URL présignée pour une miniature");
            
            $this->line("URL présignée pour miniature small: {$image->getThumbnailUrl('small')}");
            $this->line("URL présignée pour miniature medium: {$image->getThumbnailUrl('medium')}");
            $this->line("URL présignée pour miniature large: {$image->getThumbnailUrl('large')}");
            $this->newLine();
            
            // Test 4: Vérifier si les URLs sont accessibles
            $this->info("Test 4: Vérifier si les URLs sont accessibles");
            $this->line("Pour vérifier si les URLs sont accessibles, ouvrez-les dans un navigateur.");
            $this->line("URL présignée: {$presignedUrl}");
            $this->line("URL de l'image: {$image->url}");
            $this->newLine();
            
            $this->info("Tous les tests sont terminés!");
            return 0;
        } catch (\Exception $e) {
            $this->error("Erreur: " . $e->getMessage());
            $this->line("Trace: " . $e->getTraceAsString());
            return 1;
        }
    }
}
