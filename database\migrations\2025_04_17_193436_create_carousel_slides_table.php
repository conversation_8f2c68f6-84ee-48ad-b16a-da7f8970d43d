<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('carousel_slides', function (Blueprint $table) {
            $table->id();
            $table->foreignId('carousel_id')->constrained()->onDelete('cascade');
            $table->string('titre')->comment('Titre du slide');
            $table->text('description')->nullable()->comment('Description du slide');
            $table->string('bouton_texte')->nullable()->comment('Texte du bouton');
            $table->string('bouton_lien')->nullable()->comment('Lien du bouton');
            $table->integer('ordre')->default(0)->comment('Ordre d\'affichage');
            $table->boolean('actif')->default(true)->comment('Statut actif du slide');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('carousel_slides');
    }
};
