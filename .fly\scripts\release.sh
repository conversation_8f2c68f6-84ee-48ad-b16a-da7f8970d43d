#!/usr/bin/env bash

set -e

echo "Running release tasks..."

# Run database migrations
echo "Running database migrations..."
/usr/bin/php /var/www/html/artisan migrate --force --no-ansi -q

if [ $? -eq 0 ]; then
    echo "Migrations completed successfully"
else
    echo "Migration failed with exit code $?"
    exit 1
fi

# Cache configuration, routes, and views
echo "Caching configuration..."
/usr/bin/php /var/www/html/artisan config:cache --no-ansi -q

echo "Caching routes..."
/usr/bin/php /var/www/html/artisan route:cache --no-ansi -q

echo "Caching views..."
/usr/bin/php /var/www/html/artisan view:cache --no-ansi -q

echo "Release tasks completed successfully!"
