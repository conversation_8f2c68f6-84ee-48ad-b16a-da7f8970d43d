<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, update existing data to ensure it's numeric
        DB::statement("UPDATE commandes SET total_commande = '0' WHERE total_commande = '' OR total_commande IS NULL");
        DB::statement("UPDATE commandes SET remise_commande = '0' WHERE remise_commande = '' OR remise_commande IS NULL");

        // Change string columns to decimal with explicit USING clause for PostgreSQL
        DB::statement('ALTER TABLE commandes ALTER COLUMN total_commande TYPE DECIMAL(10,2) USING total_commande::DECIMAL(10,2)');
        DB::statement('ALTER TABLE commandes ALTER COLUMN remise_commande TYPE DECIMAL(5,2) USING remise_commande::DECIMAL(5,2)');

        Schema::table('commandes', function (Blueprint $table) {
            // Add missing fields only if they don't exist
            if (!Schema::hasColumn('commandes', 'numero_commande')) {
                $table->string('numero_commande')->unique()->nullable()->after('user_id');
            }

            if (!Schema::hasColumn('commandes', 'client_id')) {
                $table->foreignId('client_id')->nullable()->constrained('clients')->onDelete('set null')->after('user_id');
            }

            // Status already exists, but let's make sure it has the right enum values
            if (Schema::hasColumn('commandes', 'status')) {
                // Modify existing status column to have proper enum values
                DB::statement("ALTER TABLE commandes DROP CONSTRAINT IF EXISTS commandes_status_check");
                DB::statement("ALTER TABLE commandes ADD CONSTRAINT commandes_status_check CHECK (status IN ('en_attente', 'confirmee', 'en_preparation', 'expediee', 'livree', 'annulee', 'remboursee'))");
            }

            if (!Schema::hasColumn('commandes', 'payment_status')) {
                $table->enum('payment_status', ['pending', 'paid', 'failed', 'refunded'])
                    ->default('pending')->after('status');
            }
            if (!Schema::hasColumn('commandes', 'subtotal')) {
                $table->decimal('subtotal', 10, 2)->default(0)->after('payment_status');
            }
            if (!Schema::hasColumn('commandes', 'tax_amount')) {
                $table->decimal('tax_amount', 10, 2)->default(0)->after('subtotal');
            }
            if (!Schema::hasColumn('commandes', 'shipping_cost')) {
                $table->decimal('shipping_cost', 10, 2)->default(0)->after('tax_amount');
            }
            if (!Schema::hasColumn('commandes', 'discount_amount')) {
                $table->decimal('discount_amount', 10, 2)->default(0)->after('shipping_cost');
            }
            if (!Schema::hasColumn('commandes', 'shipping_address')) {
                $table->json('shipping_address')->nullable()->after('discount_amount');
            }
            if (!Schema::hasColumn('commandes', 'billing_address')) {
                $table->json('billing_address')->nullable()->after('shipping_address');
            }
            if (!Schema::hasColumn('commandes', 'notes')) {
                $table->text('notes')->nullable()->after('billing_address');
            }
            if (!Schema::hasColumn('commandes', 'code_promo')) {
                $table->string('code_promo')->nullable()->after('notes');
            }
            if (!Schema::hasColumn('commandes', 'methode_paiement')) {
                $table->string('methode_paiement')->nullable()->after('code_promo');
            }
            if (!Schema::hasColumn('commandes', 'date_commande')) {
                $table->timestamp('date_commande')->nullable()->after('methode_paiement');
            }
            if (!Schema::hasColumn('commandes', 'confirmed_at')) {
                $table->timestamp('confirmed_at')->nullable()->after('date_commande');
            }
            if (!Schema::hasColumn('commandes', 'preparation_started_at')) {
                $table->timestamp('preparation_started_at')->nullable()->after('confirmed_at');
            }
            if (!Schema::hasColumn('commandes', 'shipped_at')) {
                $table->timestamp('shipped_at')->nullable()->after('preparation_started_at');
            }
            if (!Schema::hasColumn('commandes', 'delivered_at')) {
                $table->timestamp('delivered_at')->nullable()->after('shipped_at');
            }
            if (!Schema::hasColumn('commandes', 'cancelled_at')) {
                $table->timestamp('cancelled_at')->nullable()->after('delivered_at');
            }
            if (!Schema::hasColumn('commandes', 'refunded_at')) {
                $table->timestamp('refunded_at')->nullable()->after('cancelled_at');
            }
        });

        // Rename existing address fields in a separate alter statement
        Schema::table('commandes', function (Blueprint $table) {
            if (Schema::hasColumn('commandes', 'adresse_commande') && !Schema::hasColumn('commandes', 'shipping_street')) {
                $table->renameColumn('adresse_commande', 'shipping_street');
            }
            if (Schema::hasColumn('commandes', 'ville_commande') && !Schema::hasColumn('commandes', 'shipping_city')) {
                $table->renameColumn('ville_commande', 'shipping_city');
            }
            if (Schema::hasColumn('commandes', 'code_postal_commande') && !Schema::hasColumn('commandes', 'shipping_postal_code')) {
                $table->renameColumn('code_postal_commande', 'shipping_postal_code');
            }
        });

        Schema::table('commandes', function (Blueprint $table) {
            if (Schema::hasColumn('commandes', 'telephone_client') && !Schema::hasColumn('commandes', 'telephone_commande')) {
                $table->renameColumn('telephone_client', 'telephone_commande');
            }
            // Ensure telephone_commande is nullable
            if (Schema::hasColumn('commandes', 'telephone_commande')) {
                $table->string('telephone_commande')->nullable()->change();
            }

            if (Schema::hasColumn('commandes', 'email_client') && !Schema::hasColumn('commandes', 'email_commande')) {
                $table->renameColumn('email_client', 'email_commande');
            }
            // Ensure email_commande is nullable
            if (Schema::hasColumn('commandes', 'email_commande')) {
                $table->string('email_commande')->nullable()->change();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('commandes', function (Blueprint $table) {
            // Revert column renames
            $table->renameColumn('shipping_street', 'adresse_commande');
            $table->renameColumn('shipping_city', 'ville_commande');
            $table->renameColumn('shipping_postal_code', 'code_postal_commande');
        });

        Schema::table('commandes', function (Blueprint $table) {
            // Revert data type changes
            $table->string('total_commande')->change();
            $table->string('remise_commande')->change();

            // Remove added columns
            $table->dropColumn([
                'numero_commande',
                'client_id',
                'status',
                'payment_status',
                'subtotal',
                'tax_amount',
                'shipping_cost',
                'discount_amount',
                'shipping_address',
                'billing_address',
                'notes',
                'code_promo',
                'methode_paiement',
                'date_commande',
                'confirmed_at',
                'preparation_started_at',
                'shipped_at',
                'delivered_at',
                'cancelled_at',
                'refunded_at'
            ]);
        });
    }
};
