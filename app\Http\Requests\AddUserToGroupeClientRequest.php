<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AddUserToGroupeClientRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'user_id' => 'required|exists:users,id',
        ];
    }

    public function messages()
    {
        return [
            'user_id.required' => "L'identifiant de l'utilisateur est obligatoire.",
            'user_id.exists' => "L'utilisateur sélectionné est invalide.",
        ];
    }
} 