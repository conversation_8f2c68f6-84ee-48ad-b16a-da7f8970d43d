<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateAttributRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'nom' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string',
            'type_valeur' => 'sometimes|required|in:texte,nombre,date,booleen,liste',
            'groupe_id' => 'nullable|exists:groupes_attributs,id',
            'obligatoire' => 'nullable|boolean',
            'filtrable' => 'nullable|boolean',
            'comparable' => 'nullable|boolean',
            'sous_categories' => 'nullable|array',
            'sous_categories.*.id' => 'required|exists:sous_categories,id',
            'sous_categories.*.obligatoire' => 'nullable|boolean',
        ];
    }

    public function messages(): array
    {
        return [
            'nom.required' => 'Le nom est obligatoire.',
            'nom.string' => 'Le nom doit être une chaîne de caractères.',
            'nom.max' => 'Le nom ne doit pas dépasser 255 caractères.',
            'description.string' => 'La description doit être une chaîne de caractères.',
            'type_valeur.required' => 'Le type de valeur est obligatoire.',
            'type_valeur.in' => 'Le type de valeur est invalide.',
            'groupe_id.exists' => 'Le groupe d\'attribut sélectionné est invalide.',
            'obligatoire.boolean' => 'Le champ obligatoire doit être vrai ou faux.',
            'filtrable.boolean' => 'Le champ filtrable doit être vrai ou faux.',
            'comparable.boolean' => 'Le champ comparable doit être vrai ou faux.',
            'sous_categories.array' => 'Les sous-catégories doivent être un tableau.',
            'sous_categories.*.id.required' => 'L\'identifiant de la sous-catégorie est obligatoire.',
            'sous_categories.*.id.exists' => 'La sous-catégorie sélectionnée est invalide.',
            'sous_categories.*.obligatoire.boolean' => 'Le champ obligatoire de la sous-catégorie doit être vrai ou faux.',
        ];
    }
}
