<?php

namespace App\Http\Controllers;

use App\Models\Partenaire;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use App\Http\Requests\StorePartenaireRequest;
use App\Http\Requests\UpdatePartenaireRequest;

class PartenaireController extends Controller
{
    /**
     * Display a listing of all partners.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        try {
            // Get all users with the 'partenaire' role and their partner details
            $partenaires = User::whereJsonContains('roles', 'partenaire')
                ->where('type_client', 'partenaire')
                ->with('partenaire')
                ->get();
                
            return response()->json($partenaires);
        } catch (\Exception $e) {
            return response()->json([
                "error" => "Problème de récupération des partenaires",
                "message" => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created partner in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(StorePartenaireRequest $request)
    {
        try {
            $validatedData = $request->validated();
            // Start a transaction
            DB::beginTransaction();
            // Get the user
            $user = User::findOrFail($validatedData['user_id']);
            // Update user type
            $user->type_client = 'partenaire';
            // Add partenaire role if not already present
            $roles = $user->roles ?? [];
            if (!in_array('partenaire', $roles)) {
                $roles[] = 'partenaire';
                $user->roles = $roles;
            }
            $user->save();
            // Create or update partner record
            $partenaire = Partenaire::updateOrCreate(
                ['user_id' => $validatedData['user_id']],
                [
                    'remise' => $validatedData['remise'],
                    'description' => $validatedData['description'] ?? null,
                    'statut' => $validatedData['statut'] ?? 'actif'
                ]
            );
            // Commit the transaction
            DB::commit();
            // Load the relationship
            $partenaire->load('user');
            return response()->json($partenaire, 201);
        } catch (\Exception $e) {
            // Rollback the transaction
            DB::rollBack();
            return response()->json([
                "error" => "Problème lors de la création du partenaire",
                "message" => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified partner.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $partenaire = Partenaire::with('user')->findOrFail($id);
            return response()->json($partenaire);
        } catch (\Exception $e) {
            return response()->json([
                "error" => "Problème de récupération du partenaire",
                "message" => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified partner in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(UpdatePartenaireRequest $request, $id)
    {
        try {
            $validatedData = $request->validated();
            $partenaire = Partenaire::findOrFail($id);
            $partenaire->update($validatedData);
            // Load the relationship
            $partenaire->load('user');
            return response()->json($partenaire);
        } catch (\Exception $e) {
            return response()->json([
                "error" => "Problème lors de la mise à jour du partenaire",
                "message" => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the partner status from a user.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            // Start a transaction
            DB::beginTransaction();
            
            $partenaire = Partenaire::findOrFail($id);
            $user = $partenaire->user;
            
            // Delete the partner record
            $partenaire->delete();
            
            // Update user type if this was their only special status
            if ($user) {
                // Remove partenaire role
                $roles = $user->roles ?? [];
                $roles = array_diff($roles, ['partenaire']);
                $user->roles = $roles;
                
                // Reset client type if no other special type
                if ($user->type_client === 'partenaire') {
                    $user->type_client = 'normal';
                }
                
                $user->save();
            }
            
            // Commit the transaction
            DB::commit();
            
            return response()->json([
                "message" => "Partenaire supprimé avec succès"
            ]);
        } catch (\Exception $e) {
            // Rollback the transaction
            DB::rollBack();
            
            return response()->json([
                "error" => "Problème lors de la suppression du partenaire",
                "message" => $e->getMessage()
            ], 500);
        }
    }
}
