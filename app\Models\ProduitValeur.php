<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ProduitValeur extends Model
{
    use HasFactory;

    protected $fillable = [
        'produit_id',
        'attribut_id',
        'valeur_texte',
        'valeur_nombre',
        'valeur_date',
        'valeur_booleen'
    ];

    protected $casts = [
        'valeur_nombre' => 'decimal:6',
        'valeur_date' => 'date',
        'valeur_booleen' => 'boolean'
    ];

    /**
     * Le produit auquel appartient cette valeur
     */
    public function produit()
    {
        return $this->belongsTo(Produit::class, 'produit_id');
    }

    /**
     * L'attribut auquel appartient cette valeur
     */
    public function attribut()
    {
        return $this->belongsTo(Attribut::class, 'attribut_id');
    }

    /**
     * Accesseur pour obtenir la valeur typée selon le type de l'attribut
     */
    public function getValeurAttribute()
    {
        if (!$this->attribut) {
            return null;
        }

        return match ($this->attribut->type_valeur) {
            'texte' => $this->valeur_texte,
            'nombre' => $this->valeur_nombre,
            'date' => $this->valeur_date,
            'booleen' => $this->valeur_booleen,
            'liste' => $this->valeur_texte, // Pour les listes, on stocke l'ID ou la valeur sélectionnée
            default => $this->valeur_texte
        };
    }
}
