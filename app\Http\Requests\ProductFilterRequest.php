<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ProductFilterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'search' => 'nullable|string|max:255|regex:/^[a-zA-Z0-9\s\-_\u00C0-\u017F]+$/u',
            'page' => 'nullable|integer|min:1|max:1000',
            'per_page' => 'nullable|integer|min:5|max:100',
            'prix_min' => 'nullable|numeric|min:0|max:999999',
            'prix_max' => 'nullable|numeric|min:0|max:999999|gte:prix_min',
            'marque_id' => 'nullable|integer|exists:marques,id',
            'sous_sous_categorie_id' => 'nullable|integer|exists:sous_sous_categories,id',
            'collection_ids' => 'nullable|array',
            'collection_ids.*' => 'integer|exists:collections,id',
            'sort_by' => 'nullable|string|in:id,nom_produit,prix_produit,created_at,stock',
            'sort_direction' => 'nullable|string|in:asc,desc',
            'with' => 'nullable|string',
            'with_promotions' => 'nullable|boolean',
            'en_stock' => 'nullable|boolean',
            'actif' => 'nullable|boolean',
            'attributs' => 'nullable|array',
            'attributs.*.attribut_id' => 'required_with:attributs|integer|exists:attributs,id',
            'attributs.*.valeur' => 'required_with:attributs|string|max:255',
        ];
    }

    /**
     * Get custom error messages for validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'search.regex' => 'Le terme de recherche contient des caractères non autorisés.',
            'prix_max.gte' => 'Le prix maximum doit être supérieur ou égal au prix minimum.',
            'page.max' => 'Le numéro de page ne peut pas dépasser 1000.',
            'per_page.max' => 'Le nombre d\'éléments par page ne peut pas dépasser 100.',
            'per_page.min' => 'Le nombre d\'éléments par page doit être d\'au moins 5.',
            'marque_id.exists' => 'La marque sélectionnée n\'existe pas.',
            'sous_sous_categorie_id.exists' => 'La sous-sous-catégorie sélectionnée n\'existe pas.',
            'collection_ids.*.exists' => 'Une des collections sélectionnées n\'existe pas.',
            'sort_by.in' => 'Le critère de tri doit être l\'un des suivants: id, nom_produit, prix_produit, created_at, stock.',
            'sort_direction.in' => 'La direction de tri doit être "asc" ou "desc".',
            'attributs.*.attribut_id.exists' => 'Un des attributs sélectionnés n\'existe pas.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Sanitize search input
        if ($this->has('search')) {
            $this->merge([
                'search' => strip_tags(trim($this->input('search')))
            ]);
        }

        // Set default values
        $this->merge([
            'page' => $this->input('page', 1),
            'per_page' => $this->input('per_page', 12),
            'sort_by' => $this->input('sort_by', 'created_at'),
            'sort_direction' => $this->input('sort_direction', 'desc'),
        ]);

        // Convert string booleans to actual booleans
        if ($this->has('with_promotions')) {
            $this->merge([
                'with_promotions' => filter_var($this->input('with_promotions'), FILTER_VALIDATE_BOOLEAN)
            ]);
        }

        if ($this->has('en_stock')) {
            $this->merge([
                'en_stock' => filter_var($this->input('en_stock'), FILTER_VALIDATE_BOOLEAN)
            ]);
        }

        if ($this->has('actif')) {
            $this->merge([
                'actif' => filter_var($this->input('actif'), FILTER_VALIDATE_BOOLEAN)
            ]);
        }
    }

    /**
     * Get the validated data with additional processing.
     */
    public function getFilteredData(): array
    {
        $validated = $this->validated();

        // Remove null values to avoid unnecessary filtering
        return array_filter($validated, function ($value) {
            return $value !== null && $value !== '';
        });
    }

    /**
     * Get validated input data for use in services.
     */
    public function getValidatedInput(): array
    {
        return $this->getFilteredData();
    }
}
