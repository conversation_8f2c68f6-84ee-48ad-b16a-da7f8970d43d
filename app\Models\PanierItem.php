<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PanierItem extends Model
{
    protected $table = 'panier_items';

    protected $fillable = ['panier_id', 'produit_id', 'variante_id', 'quantite', 'prix_unitaire'];

    /**
     * Relation avec le panier
     */
    public function panier()
    {
        return $this->belongsTo(Panier::class);
    }

    /**
     * Relation avec le produit
     */
    public function produit()
    {
        return $this->belongsTo(Produit::class);
    }

    /**
     * Relation avec la variante
     */
    public function variante()
    {
        return $this->belongsTo(ProduitVariante::class);
    }

    /**
     * Calcule le sous-total de l'item
     */
    public function getSubtotal()
    {
        return $this->prix_unitaire * $this->quantite;
    }
}
