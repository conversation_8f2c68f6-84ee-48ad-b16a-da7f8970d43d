# 📧 Guide Complet de l'API Contact

## Table des Matières

1. [Aperçu](#aperçu)
2. [Démarrage Rapide](#démarrage-rapide)
3. [Points de Terminaison API](#points-de-terminaison-api)
4. [Authentification et Sécurité](#authentification-et-sécurité)
5. [Règles de Validation](#règles-de-validation)
6. [Formats de Réponse](#formats-de-réponse)
7. [Gestion des Erreurs](#gestion-des-erreurs)
8. [Limitation de Débit](#limitation-de-débit)
9. [Exemples d'Intégration Frontend](#exemples-dintégration-frontend)
10. [Tests](#tests)
11. [Dépannage](#dépannage)
12. [Bonnes Pratiques](#bonnes-pratiques)

## Aperçu

L'API Contact Laravel fournit un système de soumission de formulaire de contact robuste et sécurisé avec livraison d'e-mails, validation complète et fonctionnalités de sécurité intégrées. Ce guide couvre tout ce que vous devez savoir pour implémenter et utiliser efficacement le point de terminaison de contact.

### Fonctionnalités Clés

- ✅ **Livraison d'E-mails Sécurisée**: E-mails HTML professionnels envoyés au destinataire configuré
- ✅ **Limitation de Débit**: 5 soumissions par heure par adresse IP
- ✅ **Validation Complète**: Validation côté serveur avec messages d'erreur détaillés
- ✅ **Sécurité**: Suivi IP, journalisation de l'agent utilisateur, protection XSS
- ✅ **Support de File d'Attente**: Traitement asynchrone des e-mails pour de meilleures performances
- ✅ **Piste d'Audit**: Journalisation complète de toutes les soumissions et erreurs
- ✅ **Support Multi-Langues**: Support pour différentes locales

## Démarrage Rapide

### URL de Base

```
https://votre-domaine.com/api/contact
```

### Exemple Simple

```bash
curl -X POST https://votre-domaine.com/api/contact/submit \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "name": "Jean Dupont",
    "email": "<EMAIL>",
    "message": "Bonjour, ceci est un message de test avec plus de 10 caractères."
  }'
```

## Points de Terminaison API

### 1. Soumettre un Formulaire de Contact

**POST** `/api/contact/submit`

Soumet un formulaire de contact et envoie un e-mail au destinataire configuré.

#### En-têtes de Requête

```http
Content-Type: application/json
Accept: application/json
User-Agent: VotreApp/1.0 (optionnel mais recommandé)
```

#### Corps de la Requête

```json
{
  "name": "Jean Dupont",
  "email": "<EMAIL>", 
  "message": "Votre message détaillé ici (minimum 10 caractères requis)"
}
```

#### Réponse de Succès (200 OK)

```json
{
  "success": true,
  "message": "Message envoyé avec succès",
  "data": {
    "message": "Votre message a été envoyé avec succès. Nous vous répondrons dans les plus brefs délais.",
    "submitted_at": "29/01/2025 à 14:30",
    "reference_id": "contact_67986f8a"
  }
}
```

#### Réponse d'Erreur (422 Entité Non Traitable)

```json
{
  "success": false,
  "message": "Erreur de validation",
  "errors": {
    "name": ["Le nom doit contenir au moins 2 caractères."],
    "email": ["L'adresse e-mail doit être valide."],
    "message": ["Le message doit contenir au moins 10 caractères."]
  }
}
```

### 2. Obtenir les Informations du Formulaire de Contact

**GET** `/api/contact/info`

Récupère les règles de validation et les informations de configuration du formulaire.

#### Réponse de Succès (200 OK)

```json
{
  "success": true,
  "message": "Informations récupérées avec succès",
  "data": {
    "validation_rules": {
      "name": {
        "required": true,
        "min_length": 2,
        "max_length": 255,
        "type": "string"
      },
      "email": {
        "required": true,
        "type": "email",
        "max_length": 255
      },
      "message": {
        "required": true,
        "min_length": 10,
        "max_length": 5000,
        "type": "text"
      }
    },
    "rate_limit": {
      "max_attempts": 5,
      "decay_minutes": 60,
      "description": "5 soumissions par heure par adresse IP"
    },
    "supported_fields": ["name", "email", "message"],
    "recipient_email": "<EMAIL>"
  }
}
```

## Authentification et Sécurité

### Aucune Authentification Requise

Le point de terminaison de contact est public et ne nécessite pas de jetons d'authentification.

### Fonctionnalités de Sécurité

- **Limitation de Débit**: 5 soumissions par heure par adresse IP
- **Suivi IP**: Toutes les soumissions sont journalisées avec les adresses IP
- **Journalisation de l'Agent Utilisateur**: Les informations du navigateur/client sont enregistrées
- **Assainissement des Entrées**: Protection XSS sur toutes les entrées
- **Validation d'E-mail**: Validation du format d'e-mail côté serveur
- **Protection CSRF**: Protection CSRF intégrée de Laravel (pour les formulaires web)

### En-têtes de Sécurité

```http
X-Frame-Options: DENY
X-Content-Type-Options: nosniff
X-XSS-Protection: 1; mode=block
```

## Règles de Validation

### Champ Nom

- **Requis**: Oui
- **Type**: Chaîne de caractères
- **Longueur Minimale**: 2 caractères
- **Longueur Maximale**: 255 caractères
- **Règles**: Aucune validation spéciale au-delà de la longueur

### Champ E-mail

- **Requis**: Oui
- **Type**: Adresse e-mail valide
- **Longueur Maximale**: 255 caractères
- **Format**: Doit correspondre au format d'e-mail standard (RFC 5322)

### Champ Message

- **Requis**: Oui
- **Type**: Texte
- **Longueur Minimale**: 10 caractères
- **Longueur Maximale**: 5000 caractères
- **Règles**: Aucune balise HTML autorisée (supprimée pour la sécurité)

### Messages d'Erreur de Validation (Français)

```json
{
  "name.required": "Le nom est obligatoire.",
  "name.min": "Le nom doit contenir au moins 2 caractères.",
  "name.max": "Le nom ne peut pas dépasser 255 caractères.",
  "email.required": "L'adresse e-mail est obligatoire.",
  "email.email": "L'adresse e-mail doit être valide.",
  "email.max": "L'adresse e-mail ne peut pas dépasser 255 caractères.",
  "message.required": "Le message est obligatoire.",
  "message.min": "Le message doit contenir au moins 10 caractères.",
  "message.max": "Le message ne peut pas dépasser 5000 caractères."
}
```

## Formats de Réponse

### Réponse de Succès Standard

```json
{
  "success": true,
  "message": "Message de succès descriptif",
  "data": {
    // Objet de données de réponse
  }
}
```

### Réponse d'Erreur Standard

```json
{
  "success": false,
  "message": "Description de l'erreur",
  "errors": {
    // Erreurs spécifiques aux champs
  }
}
```

### Codes de Statut HTTP

- **200 OK**: Soumission réussie ou récupération d'informations
- **422 Entité Non Traitable**: Erreurs de validation
- **429 Trop de Requêtes**: Limite de débit dépassée
- **500 Erreur Interne du Serveur**: Erreur du serveur

## Gestion des Erreurs

### Erreurs de Validation (422)

```json
{
  "success": false,
  "message": "Erreur de validation",
  "errors": {
    "nom_du_champ": ["Message d'erreur 1", "Message d'erreur 2"]
  }
}
```

### Limite de Débit Dépassée (429)

```json
{
  "success": false,
  "message": "Trop de tentatives. Veuillez réessayer plus tard.",
  "data": {
    "retry_after": 3600,
    "max_attempts": 5,
    "remaining_attempts": 0
  }
}
```

### Erreur du Serveur (500)

```json
{
  "success": false,
  "message": "Une erreur interne s'est produite. Veuillez réessayer plus tard.",
  "error_code": "INTERNAL_ERROR"
}
```

## Limitation de Débit

### Limites par Défaut

- **5 soumissions par heure** par adresse IP
- **Fenêtre glissante**: 60 minutes

### En-têtes de Limite de Débit

```http
X-RateLimit-Limit: 5
X-RateLimit-Remaining: 4
X-RateLimit-Reset: 1643723400
```

### Gestion des Limites de Débit

Lorsque la limite de débit est dépassée, attendez le temps spécifié avant de réessayer :

```javascript
const response = await fetch('/api/contact/submit', options);

if (response.status === 429) {
  const data = await response.json();
  const retryAfter = data.data.retry_after; // secondes
  console.log(`Limite de débit dépassée. Réessayer après ${retryAfter} secondes`);
}
```

**POST** `/api/contact/submit`

Submits a contact form and sends an email to the configured recipient.

#### Request Headers
```http
Content-Type: application/json
Accept: application/json
User-Agent: YourApp/1.0 (optional but recommended)
```

#### Request Body
```json
{
  "name": "John Doe",
  "email": "<EMAIL>", 
  "message": "Your detailed message here (minimum 10 characters required)"
}
```

#### Success Response (200 OK)
```json
{
  "success": true,
  "message": "Message envoyé avec succès",
  "data": {
    "message": "Votre message a été envoyé avec succès. Nous vous répondrons dans les plus brefs délais.",
    "submitted_at": "29/01/2025 à 14:30",
    "reference_id": "contact_67986f8a"
  }
}
```

#### Error Response (422 Unprocessable Entity)
```json
{
  "success": false,
  "message": "Erreur de validation",
  "errors": {
    "name": ["Le nom doit contenir au moins 2 caractères."],
    "email": ["L'adresse e-mail doit être valide."],
    "message": ["Le message doit contenir au moins 10 caractères."]
  }
}
```

### 2. Get Contact Form Information

**GET** `/api/contact/info`

Retrieves validation rules and form configuration information.

#### Success Response (200 OK)
```json
{
  "success": true,
  "message": "Informations récupérées avec succès",
  "data": {
    "validation_rules": {
      "name": {
        "required": true,
        "min_length": 2,
        "max_length": 255,
        "type": "string"
      },
      "email": {
        "required": true,
        "type": "email",
        "max_length": 255
      },
      "message": {
        "required": true,
        "min_length": 10,
        "max_length": 5000,
        "type": "text"
      }
    },
    "rate_limit": {
      "max_attempts": 5,
      "decay_minutes": 60,
      "description": "5 submissions per hour per IP address"
    },
    "supported_fields": ["name", "email", "message"],
    "recipient_email": "<EMAIL>"
  }
}
```

## Authentication & Security

### No Authentication Required
The contact endpoint is public and doesn't require authentication tokens.

### Security Features
- **Rate Limiting**: 5 submissions per hour per IP address
- **IP Tracking**: All submissions are logged with IP addresses
- **User Agent Logging**: Browser/client information is recorded
- **Input Sanitization**: XSS protection on all inputs
- **Email Validation**: Server-side email format validation
- **CSRF Protection**: Laravel's built-in CSRF protection (for web forms)

### Security Headers
```http
X-Frame-Options: DENY
X-Content-Type-Options: nosniff
X-XSS-Protection: 1; mode=block
```

## Validation Rules

### Name Field
- **Required**: Yes
- **Type**: String
- **Min Length**: 2 characters
- **Max Length**: 255 characters
- **Rules**: No special validation beyond length

### Email Field
- **Required**: Yes
- **Type**: Valid email address
- **Max Length**: 255 characters
- **Format**: Must match standard email format (RFC 5322)

### Message Field
- **Required**: Yes
- **Type**: Text
- **Min Length**: 10 characters
- **Max Length**: 5000 characters
- **Rules**: No HTML tags allowed (stripped for security)

### Validation Error Messages (French)
```json
{
  "name.required": "Le nom est obligatoire.",
  "name.min": "Le nom doit contenir au moins 2 caractères.",
  "name.max": "Le nom ne peut pas dépasser 255 caractères.",
  "email.required": "L'adresse e-mail est obligatoire.",
  "email.email": "L'adresse e-mail doit être valide.",
  "email.max": "L'adresse e-mail ne peut pas dépasser 255 caractères.",
  "message.required": "Le message est obligatoire.",
  "message.min": "Le message doit contenir au moins 10 caractères.",
  "message.max": "Le message ne peut pas dépasser 5000 caractères."
}
```

## Response Formats

### Standard Success Response
```json
{
  "success": true,
  "message": "Descriptive success message",
  "data": {
    // Response data object
  }
}
```

### Standard Error Response
```json
{
  "success": false,
  "message": "Error description",
  "errors": {
    // Field-specific errors
  }
}
```

### HTTP Status Codes
- **200 OK**: Successful submission or info retrieval
- **422 Unprocessable Entity**: Validation errors
- **429 Too Many Requests**: Rate limit exceeded
- **500 Internal Server Error**: Server error

## Error Handling

### Validation Errors (422)
```json
{
  "success": false,
  "message": "Erreur de validation",
  "errors": {
    "field_name": ["Error message 1", "Error message 2"]
  }
}
```

### Rate Limit Exceeded (429)
```json
{
  "success": false,
  "message": "Trop de tentatives. Veuillez réessayer plus tard.",
  "data": {
    "retry_after": 3600,
    "max_attempts": 5,
    "remaining_attempts": 0
  }
}
```

### Server Error (500)
```json
{
  "success": false,
  "message": "Une erreur interne s'est produite. Veuillez réessayer plus tard.",
  "error_code": "INTERNAL_ERROR"
}
```

## Rate Limiting

### Default Limits
- **5 submissions per hour** per IP address
- **Rolling window**: 60 minutes

### Rate Limit Headers
```http
X-RateLimit-Limit: 5
X-RateLimit-Remaining: 4
X-RateLimit-Reset: 1643723400
```

### Handling Rate Limits
When rate limit is exceeded, wait for the specified time before retry:

```javascript
const response = await fetch('/api/contact/submit', options);

if (response.status === 429) {
  const data = await response.json();
  const retryAfter = data.data.retry_after; // seconds
  console.log(`Rate limit exceeded. Retry after ${retryAfter} seconds`);
}
```

## Frontend Integration Examples

### JavaScript (Vanilla)
```javascript
async function submitContactForm(formData) {
  try {
    const response = await fetch('/api/contact/submit', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(formData)
    });

    const result = await response.json();

    if (response.ok && result.success) {
      showSuccessMessage(result.data.message);
      return result;
    } else {
      handleErrors(result.errors || result.message);
      return null;
    }
  } catch (error) {
    console.error('Network error:', error);
    showErrorMessage('Network error. Please try again.');
    return null;
  }
}

// Usage
const formData = {
  name: document.getElementById('name').value,
  email: document.getElementById('email').value,
  message: document.getElementById('message').value
};

submitContactForm(formData);
```

### React Hook Example
```jsx
import { useState } from 'react';

const useContactForm = () => {
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [success, setSuccess] = useState(false);

  const submitForm = async (formData) => {
    setLoading(true);
    setErrors({});
    setSuccess(false);

    try {
      const response = await fetch('/api/contact/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      const result = await response.json();

      if (response.ok && result.success) {
        setSuccess(true);
        return result;
      } else if (response.status === 422) {
        setErrors(result.errors || {});
      } else {
        setErrors({ general: result.message });
      }
    } catch (error) {
      setErrors({ general: 'Network error. Please try again.' });
    } finally {
      setLoading(false);
    }
  };

  return { submitForm, loading, errors, success };
};

// Component usage
const ContactForm = () => {
  const { submitForm, loading, errors, success } = useContactForm();
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);
    await submitForm(Object.fromEntries(formData));
  };

  return (
    <form onSubmit={handleSubmit}>
      <input name="name" placeholder="Your Name" required />
      {errors.name && <span className="error">{errors.name[0]}</span>}
      
      <input name="email" type="email" placeholder="Your Email" required />
      {errors.email && <span className="error">{errors.email[0]}</span>}
      
      <textarea name="message" placeholder="Your Message" required />
      {errors.message && <span className="error">{errors.message[0]}</span>}
      
      <button type="submit" disabled={loading}>
        {loading ? 'Sending...' : 'Send Message'}
      </button>
      
      {success && <div className="success">Message sent successfully!</div>}
      {errors.general && <div className="error">{errors.general}</div>}
    </form>
  );
};
```

### Vue.js Example
```vue
<template>
  <form @submit.prevent="submitForm">
    <div>
      <input 
        v-model="form.name" 
        type="text" 
        placeholder="Your Name" 
        required 
      />
      <span v-if="errors.name" class="error">{{ errors.name[0] }}</span>
    </div>
    
    <div>
      <input 
        v-model="form.email" 
        type="email" 
        placeholder="Your Email" 
        required 
      />
      <span v-if="errors.email" class="error">{{ errors.email[0] }}</span>
    </div>
    
    <div>
      <textarea 
        v-model="form.message" 
        placeholder="Your Message" 
        required
      ></textarea>
      <span v-if="errors.message" class="error">{{ errors.message[0] }}</span>
    </div>
    
    <button type="submit" :disabled="loading">
      {{ loading ? 'Sending...' : 'Send Message' }}
    </button>
    
    <div v-if="success" class="success">Message sent successfully!</div>
    <div v-if="errors.general" class="error">{{ errors.general }}</div>
  </form>
</template>

<script>
export default {
  data() {
    return {
      form: {
        name: '',
        email: '',
        message: ''
      },
      loading: false,
      errors: {},
      success: false
    };
  },
  methods: {
    async submitForm() {
      this.loading = true;
      this.errors = {};
      this.success = false;

      try {
        const response = await fetch('/api/contact/submit', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify(this.form)
        });

        const result = await response.json();

        if (response.ok && result.success) {
          this.success = true;
          this.form = { name: '', email: '', message: '' };
        } else if (response.status === 422) {
          this.errors = result.errors || {};
        } else {
          this.errors = { general: result.message };
        }
      } catch (error) {
        this.errors = { general: 'Network error. Please try again.' };
      } finally {
        this.loading = false;
      }
    }
  }
};
</script>
```

### HTML Form Example
```html
<!DOCTYPE html>
<html>
<head>
    <title>Contact Form</title>
    <style>
        .form-group { margin-bottom: 15px; }
        .error { color: red; font-size: 14px; }
        .success { color: green; font-size: 14px; }
        input, textarea { width: 100%; padding: 8px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; }
        button:disabled { background: #ccc; }
    </style>
</head>
<body>
    <form id="contactForm">
        <div class="form-group">
            <input type="text" id="name" placeholder="Your Name" required>
            <div class="error" id="nameError"></div>
        </div>
        
        <div class="form-group">
            <input type="email" id="email" placeholder="Your Email" required>
            <div class="error" id="emailError"></div>
        </div>
        
        <div class="form-group">
            <textarea id="message" rows="5" placeholder="Your Message" required></textarea>
            <div class="error" id="messageError"></div>
        </div>
        
        <button type="submit" id="submitBtn">Send Message</button>
        <div class="success" id="successMessage" style="display: none;"></div>
        <div class="error" id="generalError"></div>
    </form>

    <script>
        document.getElementById('contactForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            // Clear previous errors
            document.querySelectorAll('.error').forEach(el => el.textContent = '');
            document.getElementById('successMessage').style.display = 'none';
            
            const submitBtn = document.getElementById('submitBtn');
            submitBtn.disabled = true;
            submitBtn.textContent = 'Sending...';
            
            const formData = {
                name: document.getElementById('name').value,
                email: document.getElementById('email').value,
                message: document.getElementById('message').value
            };
            
            try {
                const response = await fetch('/api/contact/submit', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    document.getElementById('successMessage').textContent = result.data.message;
                    document.getElementById('successMessage').style.display = 'block';
                    document.getElementById('contactForm').reset();
                } else if (response.status === 422) {
                    // Display validation errors
                    if (result.errors) {
                        Object.keys(result.errors).forEach(field => {
                            const errorElement = document.getElementById(field + 'Error');
                            if (errorElement) {
                                errorElement.textContent = result.errors[field][0];
                            }
                        });
                    }
                } else {
                    document.getElementById('generalError').textContent = result.message;
                }
            } catch (error) {
                document.getElementById('generalError').textContent = 'Network error. Please try again.';
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = 'Send Message';
            }
        });
    </script>
</body>
</html>
```

## Testing

### Manual Testing with cURL

#### Test Successful Submission
```bash
curl -X POST https://your-domain.com/api/contact/submit \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>",
    "message": "This is a test message with sufficient length."
  }' \
  -w "\nHTTP Status: %{http_code}\n"
```

#### Test Validation Errors
```bash
# Test with invalid data
curl -X POST https://your-domain.com/api/contact/submit \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "name": "A",
    "email": "invalid-email",
    "message": "Short"
  }' \
  -w "\nHTTP Status: %{http_code}\n"
```

#### Test Rate Limiting
```bash
# Submit 6 requests quickly to trigger rate limiting
for i in {1..6}; do
  echo "Request $i:"
  curl -X POST https://your-domain.com/api/contact/submit \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{
      "name": "Test User",
      "email": "<EMAIL>",
      "message": "This is test message number '$i' with sufficient length."
    }' \
    -w "\nHTTP Status: %{http_code}\n"
  echo "---"
done
```

#### Get Contact Info
```bash
curl -X GET https://your-domain.com/api/contact/info \
  -H "Accept: application/json" \
  -w "\nHTTP Status: %{http_code}\n"
```

### Automated Testing Script

Create a file `test_contact_api_comprehensive.php`:

```php
<?php

class ContactApiTester {
    private $baseUrl;
    
    public function __construct($baseUrl) {
        $this->baseUrl = rtrim($baseUrl, '/');
    }
    
    public function runAllTests() {
        echo "🧪 Starting Contact API Tests\n";
        echo str_repeat("=", 50) . "\n";
        
        $this->testContactInfo();
        $this->testValidSubmission();
        $this->testValidationErrors();
        $this->testRateLimiting();
        
        echo "\n✅ All tests completed!\n";
    }
    
    private function testContactInfo() {
        echo "\n📋 Testing GET /api/contact/info\n";
        
        $response = $this->makeRequest('GET', '/api/contact/info');
        
        if ($response['httpCode'] === 200 && $response['data']['success']) {
            echo "✅ Contact info retrieved successfully\n";
            echo "   Validation rules present: " . (isset($response['data']['data']['validation_rules']) ? 'Yes' : 'No') . "\n";
        } else {
            echo "❌ Failed to get contact info\n";
            print_r($response);
        }
    }
    
    private function testValidSubmission() {
        echo "\n📧 Testing Valid Submission\n";
        
        $validData = [
            'name' => 'Test User ' . time(),
            'email' => 'test' . time() . '@example.com',
            'message' => 'This is a comprehensive test message that is definitely longer than 10 characters and tests the contact API functionality.'
        ];
        
        $response = $this->makeRequest('POST', '/api/contact/submit', $validData);
        
        if ($response['httpCode'] === 200 && $response['data']['success']) {
            echo "✅ Valid submission successful\n";
            echo "   Message: " . $response['data']['data']['message'] . "\n";
        } else {
            echo "❌ Valid submission failed\n";
            print_r($response);
        }
    }
    
    private function testValidationErrors() {
        echo "\n🚫 Testing Validation Errors\n";
        
        $invalidData = [
            'name' => 'A',  // Too short
            'email' => 'invalid-email',  // Invalid format
            'message' => 'Short'  // Too short
        ];
        
        $response = $this->makeRequest('POST', '/api/contact/submit', $invalidData);
        
        if ($response['httpCode'] === 422 && !$response['data']['success']) {
            echo "✅ Validation errors returned correctly\n";
            echo "   Errors found for: " . implode(', ', array_keys($response['data']['errors'])) . "\n";
        } else {
            echo "❌ Validation test failed\n";
            print_r($response);
        }
    }
    
    private function testRateLimiting() {
        echo "\n⏱️ Testing Rate Limiting (submitting 6 requests)\n";
        
        $rateLimitHit = false;
        
        for ($i = 1; $i <= 6; $i++) {
            $data = [
                'name' => 'Rate Test User ' . $i,
                'email' => 'ratetest' . $i . time() . '@example.com',
                'message' => 'Rate limiting test message number ' . $i . ' with sufficient length for validation.'
            ];
            
            $response = $this->makeRequest('POST', '/api/contact/submit', $data);
            
            echo "   Request $i: HTTP {$response['httpCode']}";
            
            if ($response['httpCode'] === 429) {
                echo " (Rate limit hit)";
                $rateLimitHit = true;
            } elseif ($response['httpCode'] === 200) {
                echo " (Success)";
            } else {
                echo " (Error)";
            }
            echo "\n";
            
            // Small delay between requests
            sleep(1);
        }
        
        if ($rateLimitHit) {
            echo "✅ Rate limiting is working correctly\n";
        } else {
            echo "⚠️ Rate limiting may not be working (no 429 responses)\n";
        }
    }
    
    private function makeRequest($method, $endpoint, $data = null) {
        $url = $this->baseUrl . $endpoint;
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Accept: application/json'
            ],
            CURLOPT_TIMEOUT => 30
        ]);
        
        if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            return [
                'httpCode' => 0,
                'data' => ['error' => $error],
                'raw' => null
            ];
        }
        
        return [
            'httpCode' => $httpCode,
            'data' => json_decode($response, true),
            'raw' => $response
        ];
    }
}

// Usage
$baseUrl = 'http://localhost:8000';  // Change to your API URL
$tester = new ContactApiTester($baseUrl);
$tester->runAllTests();
```

Run the test script:
```bash
php test_contact_api_comprehensive.php
```

## Troubleshooting

### Common Issues

#### 1. 500 Internal Server Error
**Possible Causes:**
- Mail configuration issues
- Database connection problems
- Missing environment variables

**Solutions:**
- Check Laravel logs: `storage/logs/laravel.log`
- Verify mail configuration in `.env`
- Ensure database is accessible
- Check file permissions on storage directories

#### 2. 422 Validation Errors
**Possible Causes:**
- Invalid input data
- Missing required fields
- Data exceeds length limits

**Solutions:**
- Verify all required fields are present
- Check field length requirements
- Ensure email format is valid
- Use GET `/api/contact/info` to see validation rules

#### 3. 429 Rate Limit Exceeded
**Possible Causes:**
- Too many submissions from same IP
- Previous failed attempts count toward limit

**Solutions:**
- Wait for rate limit window to reset (1 hour)
- Use different IP address for testing
- Contact administrator to reset rate limits

#### 4. CORS Issues (Browser)
**Possible Causes:**
- Frontend and API on different domains
- Missing CORS headers

**Solutions:**
- Configure CORS in Laravel
- Add proper headers to API responses
- Use proxy in development

#### 5. Email Not Received
**Possible Causes:**
- Mail server configuration issues
- Email in spam folder
- Queue not processing

**Solutions:**
- Check mail configuration
- Verify queue worker is running
- Check email spam folder
- Test with different email provider

### Debugging Steps

1. **Check API Response**
   ```javascript
   console.log('Response status:', response.status);
   console.log('Response data:', await response.json());
   ```

2. **Verify Request Format**
   ```javascript
   console.log('Request headers:', {
     'Content-Type': 'application/json',
     'Accept': 'application/json'
   });
   console.log('Request body:', JSON.stringify(formData));
   ```

3. **Check Network Tab**
   - Open browser dev tools
   - Monitor Network tab during submission
   - Check request/response details

4. **Review Server Logs**
   ```bash
   tail -f storage/logs/laravel.log
   ```

### Error Response Reference

| Status Code | Error Type | Description |
|-------------|------------|-------------|
| 200 | Success | Request completed successfully |
| 422 | Validation Error | Input validation failed |
| 429 | Rate Limited | Too many requests |
| 500 | Server Error | Internal server error |

## Best Practices

### Frontend Implementation

1. **Input Validation**
   - Implement client-side validation
   - Don't rely solely on server validation
   - Provide real-time feedback

2. **User Experience**
   - Show loading states during submission
   - Clear error messages
   - Success confirmation
   - Form reset after successful submission

3. **Error Handling**
   - Handle network errors gracefully
   - Display field-specific errors
   - Provide retry mechanisms

4. **Security**
   - Never expose sensitive data in frontend
   - Validate all inputs
   - Use HTTPS in production

### Performance Optimization

1. **Request Optimization**
   - Use compression (gzip)
   - Minimize request size
   - Implement proper caching

2. **Rate Limiting**
   - Respect rate limits
   - Implement exponential backoff
   - Show countdown timers

3. **Queue Management**
   - Monitor queue workers
   - Handle failed jobs
   - Set appropriate timeouts

### Security Considerations

1. **Data Protection**
   - Never log sensitive information
   - Use HTTPS for all requests
   - Implement proper input sanitization

2. **Rate Limiting**
   - Monitor for abuse patterns
   - Implement progressive penalties
   - Use IP-based and user-based limits

3. **Monitoring**
   - Log all submissions
   - Monitor error rates
   - Set up alerts for unusual activity

### Production Deployment

1. **Environment Configuration**
   ```env
   MAIL_MAILER=smtp
   MAIL_HOST=your-smtp-host
   MAIL_PORT=587
   MAIL_USERNAME=your-email
   MAIL_PASSWORD=your-password
   MAIL_ENCRYPTION=tls
   MAIL_FROM_ADDRESS=<EMAIL>
   MAIL_FROM_NAME="Your App Name"
   
   CONTACT_RECIPIENT_EMAIL=<EMAIL>
   ```

2. **Queue Configuration**
   ```bash
   # Start queue worker
   php artisan queue:work --daemon
   
   # Or use supervisor for production
   sudo supervisorctl start laravel-worker:*
   ```

3. **Monitoring Setup**
   - Set up error tracking (Sentry, Bugsnag)
   - Monitor email delivery rates
   - Track API response times
   - Set up uptime monitoring

### Code Examples for Different Scenarios

#### Progressive Form Enhancement
```javascript
class ContactFormHandler {
  constructor(formSelector) {
    this.form = document.querySelector(formSelector);
    this.setupEventListeners();
    this.loadValidationRules();
  }
  
  async loadValidationRules() {
    try {
      const response = await fetch('/api/contact/info');
      const data = await response.json();
      this.validationRules = data.data.validation_rules;
      this.setupClientValidation();
    } catch (error) {
      console.warn('Could not load validation rules');
    }
  }
  
  setupClientValidation() {
    // Implement real-time validation based on server rules
    const nameInput = this.form.querySelector('[name="name"]');
    nameInput.addEventListener('blur', () => {
      this.validateField('name', nameInput.value);
    });
    
    // Similar for other fields...
  }
  
  validateField(fieldName, value) {
    const rules = this.validationRules[fieldName];
    const errors = [];
    
    if (rules.required && !value.trim()) {
      errors.push(`${fieldName} is required`);
    }
    
    if (rules.min_length && value.length < rules.min_length) {
      errors.push(`${fieldName} must be at least ${rules.min_length} characters`);
    }
    
    // Show/hide errors
    this.displayFieldErrors(fieldName, errors);
  }
  
  displayFieldErrors(fieldName, errors) {
    const errorElement = this.form.querySelector(`[data-error="${fieldName}"]`);
    if (errorElement) {
      errorElement.textContent = errors.join(', ');
      errorElement.style.display = errors.length ? 'block' : 'none';
    }
  }
}

// Initialize
const contactForm = new ContactFormHandler('#contact-form');
```

This comprehensive guide covers everything you need to know about implementing and using the Laravel Contact API. Keep this documentation updated as the API evolves and add any project-specific customizations as needed.
