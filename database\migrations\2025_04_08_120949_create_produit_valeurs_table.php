<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('produit_valeurs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('produit_id')->constrained()->onDelete('cascade');
            $table->foreignId('attribut_id')->constrained()->onDelete('cascade');
            $table->string('valeur_texte')->nullable();
            $table->decimal('valeur_nombre', 15, 6)->nullable();
            $table->date('valeur_date')->nullable();
            $table->boolean('valeur_booleen')->nullable();
            $table->timestamps();

            $table->unique(['produit_id', 'attribut_id']);
            $table->index('valeur_texte');
            $table->index('valeur_nombre');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('produit_valeurs');
    }
};
