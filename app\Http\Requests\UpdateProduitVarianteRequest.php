<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateProduitVarianteRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $id = $this->route('id');
        return [
            'sku' => 'sometimes|required|string|max:100|unique:produit_variantes,sku,' . $id,
            'prix_supplement' => 'sometimes|required|numeric|min:0',
            'stock' => 'sometimes|required|integer|min:0',
            'actif' => 'nullable|boolean',
            'valeurs' => 'nullable|array',
            'valeurs.*.attribut_id' => 'required|exists:attributs,id',
            'valeurs.*.valeur' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'sku.required' => 'Le champ sku est obligatoire.',
            'sku.unique' => 'Le sku doit être unique.',
            'prix_supplement.required' => 'Le prix supplémentaire est obligatoire.',
            'stock.required' => 'Le stock est obligatoire.',
            'valeurs.*.attribut_id.required' => 'L\'attribut est obligatoire.',
            'valeurs.*.attribut_id.exists' => 'L\'attribut sélectionné est invalide.',
            'valeurs.*.valeur.required' => 'La valeur de l\'attribut est obligatoire.',
        ];
    }
} 