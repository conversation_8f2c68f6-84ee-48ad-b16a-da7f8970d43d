<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Enhanced Rate Limiting Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration for enhanced rate limiting middleware.
    | Different endpoint types can have different rate limiting rules.
    |
    */

    'default' => [
        'attempts' => 60,
        'decay' => 60,
    ],

    'endpoints' => [
        'auth' => [
            'attempts' => 999999,
            'decay' => 60,
            'description' => 'Authentication endpoints (login, register, etc.) - UNLIMITED FOR TESTING',
        ],

        'api' => [
            'attempts' => 999999,
            'decay' => 60,
            'description' => 'General API endpoints - UNLIMITED FOR TESTING',
        ],

        'search' => [
            'attempts' => 999999,
            'decay' => 60,
            'description' => 'Search endpoints - UNLIMITED FOR TESTING',
        ],

        'upload' => [
            'attempts' => 999999,
            'decay' => 60,
            'description' => 'File upload endpoints - UNLIMITED FOR TESTING',
        ],

        'checkout' => [
            'attempts' => 999999,
            'decay' => 60,
            'description' => 'Checkout and payment endpoints - UNLIMITED FOR TESTING',
        ],

        'admin' => [
            'attempts' => 999999,
            'decay' => 60,
            'description' => 'Admin panel endpoints - UNLIMITED FOR TESTING',
        ],

        'public' => [
            'attempts' => 999999,
            'decay' => 60,
            'description' => 'Public endpoints (products, categories, etc.) - UNLIMITED FOR TESTING',
        ],

        'monitoring' => [
            'attempts' => 999999,
            'decay' => 60,
            'description' => 'System monitoring endpoints - UNLIMITED FOR TESTING',
        ],

        'webhook' => [
            'attempts' => 999999,
            'decay' => 60,
            'description' => 'Webhook endpoints - UNLIMITED FOR TESTING',
        ],

        'contact' => [
            'attempts' => 999999,
            'decay' => 60,
            'description' => 'Contact form endpoints - UNLIMITED FOR TESTING',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | IP Whitelist
    |--------------------------------------------------------------------------
    |
    | IP addresses that should be excluded from rate limiting.
    | Useful for monitoring systems, load balancers, etc.
    |
    */
    'whitelist' => [
        '127.0.0.1',
        '::1',
        // Add your monitoring IPs here
        // '*************',
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limit Headers
    |--------------------------------------------------------------------------
    |
    | Whether to include rate limit information in response headers.
    |
    */
    'headers' => [
        'enabled' => true,
        'limit_header' => 'X-RateLimit-Limit',
        'remaining_header' => 'X-RateLimit-Remaining',
        'reset_header' => 'X-RateLimit-Reset',
        'retry_after_header' => 'Retry-After',
    ],

    /*
    |--------------------------------------------------------------------------
    | Redis Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for Redis-based rate limiting.
    |
    */
    'redis' => [
        'connection' => env('RATE_LIMIT_REDIS_CONNECTION', 'default'),
        'prefix' => env('RATE_LIMIT_REDIS_PREFIX', 'rate_limit:'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for logging rate limit violations.
    |
    */
    'logging' => [
        'enabled' => true,
        'channel' => env('RATE_LIMIT_LOG_CHANNEL', 'security'),
        'log_all_hits' => env('RATE_LIMIT_LOG_ALL', false),
    ],
];
