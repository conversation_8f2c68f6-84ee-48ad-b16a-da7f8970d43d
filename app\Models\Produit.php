<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Produit extends Model
{
    use HasFactory;
    protected $fillable = [
        'nom_produit',
        'prix_produit',
        'quantite_produit',
        'description_produit',
        "image_produit", // Deprecated - will be removed in favor of images relation
        'marque_id',
        'sous_sous_categorie_id',
        'reference'
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($produit) {
            if (!$produit->reference) {
                $produit->reference = $produit->marque_id . '-' . ($produit->id ?? (Produit::max('id') + 1));
            }
        });
    }

    public function marque()
    {
        return $this->belongsTo(Marque::class);
    }

    public function sousSousCategorie()
    {
        return $this->belongsTo(sous_sousCategorie::class);
    }



    public function commandes()
    {
        return $this->belongsToMany(Commande::class, 'commande_produit', 'produit_id', 'commande_id')
            ->withPivot('quantite', 'prix_unitaire')
            ->withTimestamps();
    }

    /**
     * Les collections auxquelles ce produit appartient
     */
    public function collections()
    {
        return $this->belongsToMany(Collection::class, 'collection_produit')
            ->withPivot('ordre', 'featured')
            ->withTimestamps();
    }

    /**
     * Les promotions associées à ce produit
     */
    public function promotions()
    {
        return $this->belongsToMany(Promotion::class, 'produit_promotion')
            ->withPivot('date_debut', 'date_fin')
            ->withTimestamps();
    }

    /**
     * Les valeurs d'attributs de ce produit
     */
    public function valeurs()
    {
        return $this->hasMany(ProduitValeur::class, 'produit_id');
    }

    /**
     * Les variantes de ce produit
     */
    public function variantes()
    {
        return $this->hasMany(ProduitVariante::class, 'produit_parent_id');
    }

    /**
     * Méthode pour obtenir la valeur d'un attribut spécifique
     */
    public function getValeurAttribut($attributId)
    {
        $valeur = $this->valeurs()->where('attribut_id', $attributId)->first();
        return $valeur ? $valeur->valeur : null;
    }

    /**
     * Méthode pour définir la valeur d'un attribut
     */
    public function setValeurAttribut($attributId, $valeur)
    {
        $attribut = Attribut::findOrFail($attributId);
        $colonne = 'valeur_' . $attribut->type_valeur;

        return $this->valeurs()->updateOrCreate(
            ['attribut_id' => $attributId],
            [$colonne => $valeur]
        );
    }

    /**
     * Get all images for this product
     */
    public function images(): MorphMany
    {
        return $this->morphMany(Image::class, 'imageable')->orderBy('order');
    }

    /**
     * Get the primary image for this product
     */
    public function getPrimaryImageAttribute()
    {
        return $this->images()->where('is_primary', true)->first() ?? $this->images()->first();
    }

    /**
     * Get the primary image URL for this product
     */
    public function getPrimaryImageUrlAttribute()
    {
        if ($this->primaryImage) {
            return $this->primaryImage->url;
        }

        // Fallback to the old image_produit field if no images are associated
        if ($this->image_produit) {
            return $this->image_produit;
        }

        return null;
    }

    /**
     * Historique des mouvements de stock pour ce produit
     */
    public function stockHistorique()
    {
        return $this->hasMany(StockHistorique::class)->orderBy('created_at', 'desc');
    }

    /**
     * Vérifie si le produit est en stock
     */
    public function getEnStockAttribute()
    {
        return $this->quantite_produit > 0;
    }

    /**
     * Vérifie si le produit est en rupture de stock
     */
    public function getEnRuptureAttribute()
    {
        return $this->quantite_produit <= 0;
    }

    /**
     * Vérifie si le produit est en stock limité
     */
    public function getStockLimiteAttribute()
    {
        // Considérer comme stock limité si moins de 5 unités disponibles
        return $this->quantite_produit > 0 && $this->quantite_produit <= 5;
    }
}

