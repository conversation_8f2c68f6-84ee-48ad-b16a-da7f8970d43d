<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('variante_valeurs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('produit_variante_id')->constrained()->onDelete('cascade');
            $table->foreignId('attribut_id')->constrained()->onDelete('cascade');
            $table->string('valeur_texte')->nullable();
            $table->decimal('valeur_nombre', 15, 6)->nullable();
            $table->date('valeur_date')->nullable();
            $table->boolean('valeur_booleen')->nullable();
            $table->timestamps();

            $table->unique(['produit_variante_id', 'attribut_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('variante_valeurs');
    }
};
