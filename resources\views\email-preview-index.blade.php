<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Templates Preview - Lack parisien</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600&family=Inter:wght@300;400;500&display=swap');
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            line-height: 1.7;
            color: #5a4a3a;
            margin: 0;
            padding: 40px 20px;
            background: linear-gradient(135deg, #f8f6f0 0%, #f1ede4 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0 8px 32px rgba(139, 117, 95, 0.12);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #d4c4a8 0%, #c8b896 100%);
            color: #5a4a3a;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-family: 'Playfair Display', serif;
            font-size: 32px;
            font-weight: 600;
            letter-spacing: -0.5px;
        }

        .header p {
            margin: 10px 0 0 0;
            font-size: 16px;
            opacity: 0.8;
        }

        .content {
            padding: 40px;
        }

        .warning {
            background: linear-gradient(135deg, #fdf8e8 0%, #faf5e0 100%);
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #d4c4a8;
            margin-bottom: 30px;
        }

        .warning h3 {
            margin: 0 0 10px 0;
            color: #8b7560;
            font-family: 'Playfair Display', serif;
            font-size: 18px;
        }

        .warning p {
            margin: 0;
            color: #6b5b4b;
        }

        .templates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .template-card {
            background: linear-gradient(135deg, #faf9f6 0%, #f5f3ee 100%);
            border-radius: 8px;
            padding: 25px;
            border-left: 3px solid #d4c4a8;
            transition: all 0.3s ease;
        }

        .template-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(139, 117, 95, 0.15);
        }

        .template-card h3 {
            margin: 0 0 10px 0;
            color: #5a4a3a;
            font-family: 'Playfair Display', serif;
            font-size: 20px;
            font-weight: 600;
        }

        .template-card p {
            margin: 0 0 20px 0;
            color: #6b5b4b;
            font-size: 14px;
        }

        .preview-btn {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(135deg, #8b7560 0%, #7a6550 100%);
            color: #ffffff;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            font-size: 14px;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        .preview-btn:hover {
            background: linear-gradient(135deg, #7a6550 0%, #6b5b4b 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(139, 117, 95, 0.3);
            text-decoration: none;
            color: #ffffff;
        }

        .footer {
            margin-top: 40px;
            padding: 30px 40px;
            background: linear-gradient(135deg, #faf9f6 0%, #f5f3ee 100%);
            text-align: center;
            color: #8b7560;
            font-size: 13px;
            border-top: 1px solid #f0ede6;
        }

        @media (max-width: 600px) {
            .templates-grid {
                grid-template-columns: 1fr;
            }
            
            .header, .content {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Email Templates Preview</h1>
            <p>Lack parisien - Aperçu des modèles d'emails</p>
        </div>

        <div class="content">
            <div class="warning">
                <h3>⚠️ Environnement de développement</h3>
                <p>Ces routes de prévisualisation sont uniquement destinées au développement. Assurez-vous de les supprimer avant la mise en production.</p>
            </div>

            <div class="templates-grid">
                <div class="template-card">
                    <h3>Confirmation de commande</h3>
                    <p>Email envoyé automatiquement lors de la confirmation d'une commande client.</p>
                    <a href="/email-preview/order-confirmation" class="preview-btn" target="_blank">Prévisualiser</a>
                </div>

                <div class="template-card">
                    <h3>Paiement réussi</h3>
                    <p>Email de confirmation envoyé après un paiement réussi.</p>
                    <a href="/email-preview/payment-success" class="preview-btn" target="_blank">Prévisualiser</a>
                </div>

                <div class="template-card">
                    <h3>Échec de paiement</h3>
                    <p>Email d'information envoyé en cas d'échec de paiement.</p>
                    <a href="/email-preview/payment-failed" class="preview-btn" target="_blank">Prévisualiser</a>
                </div>

                <div class="template-card">
                    <h3>Formulaire de contact</h3>
                    <p>Email reçu lorsqu'un client utilise le formulaire de contact.</p>
                    <a href="/email-preview/contact-form" class="preview-btn" target="_blank">Prévisualiser</a>
                </div>

                <div class="template-card">
                    <h3>Newsletter</h3>
                    <p>Email de newsletter avec les promotions et nouvelles collections.</p>
                    <a href="/email-preview/newsletter" class="preview-btn" target="_blank">Prévisualiser</a>
                </div>
            </div>
        </div>

        <div class="footer">
            <p><strong>Lack parisien</strong> - L'art de vivre à la française</p>
            <p>Système de prévisualisation des emails - Développement uniquement</p>
        </div>
    </div>
</body>
</html>
