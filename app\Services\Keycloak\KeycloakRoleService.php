<?php

namespace App\Services\Keycloak;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class KeycloakRoleService
{
    private string $baseUrl;
    private string $realm;
    private string $clientId;
    private string $clientSecret;
    private ?string $adminToken = null;

    public function __construct()
    {
        $this->baseUrl = config('services.keycloak.base_url');
        $this->realm = config('services.keycloak.realm');
        $this->clientId = config('services.keycloak.client_id');
        $this->clientSecret = config('services.keycloak.client_secret');
    }

    /**
     * Get admin token for Keycloak API
     *
     * @return string
     * @throws Exception
     */
    protected function getAdminToken(): string
    {
        if ($this->adminToken) {
            return $this->adminToken;
        }

        try {
            $response = Http::asForm()->post(
                "{$this->baseUrl}/realms/master/protocol/openid-connect/token",
                [
                    'grant_type' => 'client_credentials',
                    'client_id' => config('services.keycloak.admin_client_id', 'admin-cli'),
                    'client_secret' => config('services.keycloak.admin_client_secret'),
                ]
            );

            if ($response->successful()) {
                $this->adminToken = $response->json('access_token');
                return $this->adminToken;
            }

            throw new Exception("Failed to get admin token: " . $response->body());
        } catch (Exception $e) {
            Log::error("Keycloak admin token error: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get a role by name
     *
     * @param string $roleName
     * @return array|null
     */
    public function getRole(string $roleName): ?array
    {
        try {
            $token = $this->getAdminToken();
            $response = Http::withToken($token)
                ->get("{$this->baseUrl}/admin/realms/{$this->realm}/roles/{$roleName}");

            if ($response->successful()) {
                return $response->json();
            }

            Log::warning("Role {$roleName} not found in Keycloak");
            return null;
        } catch (Exception $e) {
            Log::error("Error getting role from Keycloak: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Add a role to a user
     *
     * @param string $keycloakId
     * @param string $roleName
     * @return bool
     */
    public function addRoleToUser(string $keycloakId, string $roleName): bool
    {
        try {
            $role = $this->getRole($roleName);
            if (!$role) {
                Log::warning("Cannot add role {$roleName} to user: Role not found");
                return false;
            }

            $token = $this->getAdminToken();
            $response = Http::withToken($token)
                ->post(
                    "{$this->baseUrl}/admin/realms/{$this->realm}/users/{$keycloakId}/role-mappings/realm",
                    [$role]
                );

            return $response->successful();
        } catch (Exception $e) {
            Log::error("Error adding role to user in Keycloak: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Remove a role from a user
     *
     * @param string $keycloakId
     * @param string $roleName
     * @return bool
     */
    public function removeRoleFromUser(string $keycloakId, string $roleName): bool
    {
        try {
            $role = $this->getRole($roleName);
            if (!$role) {
                Log::warning("Cannot remove role {$roleName} from user: Role not found");
                return false;
            }

            $token = $this->getAdminToken();
            $response = Http::withToken($token)
                ->delete(
                    "{$this->baseUrl}/admin/realms/{$this->realm}/users/{$keycloakId}/role-mappings/realm",
                    [$role]
                );

            return $response->successful();
        } catch (Exception $e) {
            Log::error("Error removing role from user in Keycloak: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get user's roles
     *
     * @param string $keycloakId
     * @return array
     */
    public function getUserRoles(string $keycloakId): array
    {
        try {
            $token = $this->getAdminToken();
            $response = Http::withToken($token)
                ->get("{$this->baseUrl}/admin/realms/{$this->realm}/users/{$keycloakId}/role-mappings/realm");

            if ($response->successful()) {
                return $response->json();
            }

            return [];
        } catch (Exception $e) {
            Log::error("Error getting user roles from Keycloak: " . $e->getMessage());
            return [];
        }
    }
}
