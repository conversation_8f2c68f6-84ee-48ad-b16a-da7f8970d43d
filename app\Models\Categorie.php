<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Categorie extends Model
{
    protected $fillable = [
        'nom_categorie',
        'image_categorie', // Deprecated - will be removed in favor of images relation
        'description_categorie',
        'featured',
        'featured_order'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'featured' => 'boolean',
        'featured_order' => 'integer',
    ];

    public function sousCategories()
    {
        return $this->hasMany(SousCategorie::class);
    }

    public function caracteristiques()
    {
        return $this->hasMany(Caracteristique::class);
    }

    /**
     * Get all images for this category
     */
    public function images(): MorphMany
    {
        return $this->morphMany(Image::class, 'imageable')->orderBy('order');
    }

    /**
     * Get the primary image for this category
     */
    public function getPrimaryImageAttribute()
    {
        return $this->images()->where('is_primary', true)->first() ?? $this->images()->first();
    }

    /**
     * Get the primary image URL for this category
     */
    public function getPrimaryImageUrlAttribute()
    {
        if ($this->primaryImage) {
            return $this->primaryImage->url;
        }

        // Fallback to the old image_categorie field if no images are associated
        if ($this->image_categorie) {
            return $this->image_categorie;
        }

        return null;
    }

    /**
     * Scope a query to only include featured categories.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFeatured($query)
    {
        return $query->where('featured', true)->orderBy('featured_order');
    }
}
