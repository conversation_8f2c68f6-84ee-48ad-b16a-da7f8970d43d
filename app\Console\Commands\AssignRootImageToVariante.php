<?php

namespace App\Console\Commands;

use App\Models\Image;
use App\Models\ProduitVariante;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class AssignRootImageToVariante extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'images:assign-root
                            {--list : Lister toutes les images dans le répertoire racine}
                            {--assign : Assigner des images aux variantes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Assigne des images du répertoire racine aux variantes de produits';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $list = $this->option('list');
        $assign = $this->option('assign');

        if (!$list && !$assign) {
            $this->error("Vous devez spécifier au moins une option: --list ou --assign");
            return 1;
        }

        // Récupérer toutes les images dans le répertoire racine
        $files = Storage::disk('s3')->files();

        // Filtrer pour ne garder que les fichiers image
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp'];
        $images = array_filter($files, function ($file) use ($imageExtensions) {
            $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
            return in_array($extension, $imageExtensions);
        });

        if (empty($images)) {
            $this->error("Aucune image trouvée dans le répertoire racine.");
            return 1;
        }

        $this->info("Nombre d'images trouvées dans le répertoire racine: " . count($images));

        // Lister les images
        if ($list) {
            $this->listImages($images);
        }

        // Assigner des images
        if ($assign) {
            $this->assignImages($images);
        }

        return 0;
    }

    /**
     * Lister les images trouvées
     */
    protected function listImages(array $images)
    {
        $this->info("Liste des images dans le répertoire racine:");

        $headers = ['#', 'Nom du fichier', 'Taille', 'URL'];
        $rows = [];

        foreach ($images as $index => $image) {
            $size = Storage::disk('s3')->size($image);
            $sizeFormatted = $this->formatSize($size);
            $url = Storage::disk('s3')->url($image);

            $rows[] = [
                $index + 1,
                $image,
                $sizeFormatted,
                $url
            ];
        }

        $this->table($headers, $rows);
    }

    /**
     * Assigner des images aux variantes
     */
    protected function assignImages(array $images)
    {
        $this->info("Assignation d'images aux variantes:");

        // Demander à l'utilisateur comment il souhaite procéder
        $method = $this->choice(
            'Comment souhaitez-vous procéder?',
            [
                'manual' => 'Assigner manuellement chaque image',
                'batch' => 'Assigner par lot (en utilisant un motif dans le nom de fichier)',
                'select' => 'Sélectionner une image spécifique à assigner'
            ],
            'manual'
        );

        switch ($method) {
            case 'manual':
                $this->assignManually($images);
                break;

            case 'batch':
                $this->assignBatch($images);
                break;

            case 'select':
                $this->assignSelected($images);
                break;
        }
    }

    /**
     * Assigner manuellement chaque image
     */
    protected function assignManually(array $images)
    {
        $totalProcessed = 0;
        $totalAssigned = 0;
        $totalSkipped = 0;

        foreach ($images as $index => $image) {
            $this->info("\nImage " . ($index + 1) . "/" . count($images) . ": {$image}");
            $this->info("URL: " . Storage::disk('s3')->url($image));

            if (!$this->confirm("Voulez-vous assigner cette image à une variante?", true)) {
                $this->warn("Image ignorée.");
                $totalSkipped++;
                continue;
            }

            // Demander l'ID ou le SKU de la variante
            $search = $this->ask("Entrez l'ID ou le SKU de la variante");

            // Rechercher la variante
            $variante = null;
            if (is_numeric($search)) {
                $variante = ProduitVariante::find($search);
            } else {
                $variante = ProduitVariante::where('sku', 'like', "%{$search}%")->first();
            }

            if (!$variante) {
                $this->error("Aucune variante trouvée avec cet ID ou SKU.");
                $totalSkipped++;
                continue;
            }

            $this->info("Variante trouvée: {$variante->sku} (ID: {$variante->id})");

            // Vérifier si l'image est déjà associée
            $existingImage = $variante->images()->where('path', $image)->first();
            if ($existingImage) {
                $this->warn("Cette image est déjà associée à cette variante.");
                $totalSkipped++;
                continue;
            }

            // Demander si c'est l'image principale
            $isPrimary = $this->confirm("Définir comme image principale?", !$variante->images()->exists());

            // Créer l'enregistrement d'image
            $imageRecord = new Image([
                'path' => $image,
                'filename' => basename($image),
                'disk' => 's3',
                'mime_type' => Storage::disk('s3')->mimeType($image),
                'size' => Storage::disk('s3')->size($image),
                'alt_text' => "Image pour {$variante->sku}",
                'title' => "Variante {$variante->sku}",
                'is_primary' => $isPrimary,
                'order' => $variante->images()->count(),
                'metadata' => [
                    'original_filename' => basename($image),
                    'extension' => pathinfo($image, PATHINFO_EXTENSION),
                ],
            ]);

            // Si cette image est définie comme principale, mettre à jour les autres images
            if ($isPrimary) {
                $variante->images()->update(['is_primary' => false]);
            }

            // Associer l'image à la variante
            $variante->images()->save($imageRecord);

            $this->info("Image associée avec succès à la variante.");
            $totalAssigned++;
            $totalProcessed++;

            // Demander si l'utilisateur souhaite continuer
            if ($index < count($images) - 1 && !$this->confirm("Continuer avec l'image suivante?", true)) {
                $this->info("Opération interrompue par l'utilisateur.");
                break;
            }
        }

        $this->newLine();
        $this->info("Résumé de l'opération:");
        $this->info("- Images traitées: {$totalProcessed}");
        $this->info("- Images assignées: {$totalAssigned}");
        $this->info("- Images ignorées: {$totalSkipped}");
    }

    /**
     * Assigner des images par lot
     */
    protected function assignBatch(array $images)
    {
        // Demander le motif à rechercher dans les noms de fichiers
        $pattern = $this->ask("Entrez un motif à rechercher dans les noms de fichiers (ex: variante_)");

        // Filtrer les images selon le motif
        $filteredImages = array_filter($images, function ($image) use ($pattern) {
            return stripos(basename($image), $pattern) !== false;
        });

        if (empty($filteredImages)) {
            $this->error("Aucune image ne correspond au motif '{$pattern}'.");
            return;
        }

        $this->info("Nombre d'images correspondant au motif: " . count($filteredImages));

        // Demander comment extraire l'identifiant de la variante
        $extractMethod = $this->choice(
            'Comment extraire l\'identifiant de la variante du nom de fichier?',
            [
                'sku' => 'Le nom de fichier contient le SKU de la variante',
                'id' => 'Le nom de fichier contient l\'ID de la variante',
                'manual' => 'Spécifier manuellement pour chaque image'
            ],
            'sku'
        );

        $totalProcessed = 0;
        $totalAssigned = 0;
        $totalSkipped = 0;

        foreach ($filteredImages as $image) {
            $this->info("\nTraitement de l'image: {$image}");

            $variante = null;

            if ($extractMethod === 'manual') {
                // Demander l'ID ou le SKU de la variante
                $search = $this->ask("Entrez l'ID ou le SKU de la variante pour cette image");

                // Rechercher la variante
                if (is_numeric($search)) {
                    $variante = ProduitVariante::find($search);
                } else {
                    $variante = ProduitVariante::where('sku', 'like', "%{$search}%")->first();
                }
            } else {
                $filename = basename($image);

                if ($extractMethod === 'sku') {
                    // Extraire le SKU du nom de fichier
                    $sku = $this->extractSkuFromFilename($filename, $pattern);
                    if ($sku) {
                        $variante = ProduitVariante::where('sku', 'like', "%{$sku}%")->first();
                        if ($variante) {
                            $this->info("Variante trouvée avec SKU: {$variante->sku} (ID: {$variante->id})");
                        } else {
                            $this->warn("Aucune variante trouvée avec SKU contenant: {$sku}");
                        }
                    } else {
                        $this->warn("Impossible d'extraire un SKU du nom de fichier: {$filename}");
                    }
                } else { // extractMethod === 'id'
                    // Extraire l'ID du nom de fichier
                    $id = $this->extractIdFromFilename($filename);
                    if ($id) {
                        $variante = ProduitVariante::find($id);
                        if ($variante) {
                            $this->info("Variante trouvée avec ID: {$variante->id}");
                        } else {
                            $this->warn("Aucune variante trouvée avec ID: {$id}");
                        }
                    } else {
                        $this->warn("Impossible d'extraire un ID du nom de fichier: {$filename}");
                    }
                }
            }

            if (!$variante) {
                $this->error("Aucune variante trouvée pour cette image. Image ignorée.");
                $totalSkipped++;
                continue; // C'est OK ici car nous sommes dans une boucle foreach simple
            }

            // Vérifier si l'image est déjà associée
            $existingImage = $variante->images()->where('path', $image)->first();
            if ($existingImage) {
                $this->warn("Cette image est déjà associée à cette variante.");
                $totalSkipped++;
                continue; // C'est OK ici car nous sommes dans une boucle foreach simple
            }

            // Demander si c'est l'image principale
            $isPrimary = $this->confirm("Définir comme image principale?", !$variante->images()->exists());

            // Créer l'enregistrement d'image
            $imageRecord = new Image([
                'path' => $image,
                'filename' => basename($image),
                'disk' => 's3',
                'mime_type' => Storage::disk('s3')->mimeType($image),
                'size' => Storage::disk('s3')->size($image),
                'alt_text' => "Image pour {$variante->sku}",
                'title' => "Variante {$variante->sku}",
                'is_primary' => $isPrimary,
                'order' => $variante->images()->count(),
                'metadata' => [
                    'original_filename' => basename($image),
                    'extension' => pathinfo($image, PATHINFO_EXTENSION),
                ],
            ]);

            // Si cette image est définie comme principale, mettre à jour les autres images
            if ($isPrimary) {
                $variante->images()->update(['is_primary' => false]);
            }

            // Associer l'image à la variante
            $variante->images()->save($imageRecord);

            $this->info("Image associée avec succès à la variante.");
            $totalAssigned++;
            $totalProcessed++;
        }

        $this->newLine();
        $this->info("Résumé de l'opération:");
        $this->info("- Images traitées: {$totalProcessed}");
        $this->info("- Images assignées: {$totalAssigned}");
        $this->info("- Images ignorées: {$totalSkipped}");
    }

    /**
     * Assigner une image spécifique
     */
    protected function assignSelected(array $images)
    {
        // Afficher la liste des images
        $this->listImages($images);

        // Demander quelle image assigner
        $imageIndex = $this->ask("Entrez le numéro de l'image à assigner (1-" . count($images) . ")");

        if (!is_numeric($imageIndex) || $imageIndex < 1 || $imageIndex > count($images)) {
            $this->error("Numéro d'image invalide.");
            return;
        }

        $image = $images[$imageIndex - 1];
        $this->info("Image sélectionnée: {$image}");
        $this->info("URL: " . Storage::disk('s3')->url($image));

        // Demander l'ID ou le SKU de la variante
        $search = $this->ask("Entrez l'ID ou le SKU de la variante");

        // Rechercher la variante
        $variante = null;
        if (is_numeric($search)) {
            $variante = ProduitVariante::find($search);
        } else {
            $variante = ProduitVariante::where('sku', 'like', "%{$search}%")->first();
        }

        if (!$variante) {
            $this->error("Aucune variante trouvée avec cet ID ou SKU.");
            return;
        }

        $this->info("Variante trouvée: {$variante->sku} (ID: {$variante->id})");

        // Vérifier si l'image est déjà associée
        $existingImage = $variante->images()->where('path', $image)->first();
        if ($existingImage) {
            $this->warn("Cette image est déjà associée à cette variante.");
            return;
        }

        // Demander si c'est l'image principale
        $isPrimary = $this->confirm("Définir comme image principale?", !$variante->images()->exists());

        // Créer l'enregistrement d'image
        $imageRecord = new Image([
            'path' => $image,
            'filename' => basename($image),
            'disk' => 's3',
            'mime_type' => Storage::disk('s3')->mimeType($image),
            'size' => Storage::disk('s3')->size($image),
            'alt_text' => "Image pour {$variante->sku}",
            'title' => "Variante {$variante->sku}",
            'is_primary' => $isPrimary,
            'order' => $variante->images()->count(),
            'metadata' => [
                'original_filename' => basename($image),
                'extension' => pathinfo($image, PATHINFO_EXTENSION),
            ],
        ]);

        // Si cette image est définie comme principale, mettre à jour les autres images
        if ($isPrimary) {
            $variante->images()->update(['is_primary' => false]);
        }

        // Associer l'image à la variante
        $variante->images()->save($imageRecord);

        $this->info("Image associée avec succès à la variante.");
    }

    /**
     * Extraire le SKU du nom de fichier
     */
    protected function extractSkuFromFilename($filename, $pattern = '')
    {
        // Supprimer l'extension
        $name = pathinfo($filename, PATHINFO_FILENAME);

        // Supprimer le motif du nom de fichier
        if ($pattern) {
            $name = str_ireplace($pattern, '', $name);
        }

        // Nettoyer le nom
        $name = trim($name);

        // Méthode 1: Rechercher un motif de SKU (ex: ABC-123)
        if (preg_match('/([A-Za-z0-9]+-[A-Za-z0-9]+)/', $name, $matches)) {
            return $matches[1];
        }

        // Méthode 2: Utiliser le nom de fichier nettoyé comme SKU
        return $name;
    }

    /**
     * Extraire l'ID du nom de fichier
     */
    protected function extractIdFromFilename($filename)
    {
        // Rechercher un nombre dans le nom de fichier
        if (preg_match('/(\d+)/', $filename, $matches)) {
            return $matches[1];
        }

        return null;
    }

    /**
     * Formater la taille du fichier
     */
    protected function formatSize($size)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $i = 0;
        while ($size >= 1024 && $i < count($units) - 1) {
            $size /= 1024;
            $i++;
        }
        return round($size, 2) . ' ' . $units[$i];
    }
}
