<?php

namespace App\Http\Controllers;

use App\Models\Image;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\StreamedResponse;

class ImageProxyController extends Controller
{
    /**
     * Serve an image from storage.
     *
     * @param  int  $id
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    public function serve($id)
    {
        // Find the image
        $image = Image::findOrFail($id);
        
        // Check if the file exists
        if (!Storage::disk($image->disk)->exists($image->path)) {
            abort(404, 'Image not found in storage');
        }
        
        // Get the file mime type
        $mimeType = $image->mime_type ?: Storage::disk($image->disk)->mimeType($image->path);
        
        // Return the file as a streamed response
        return Storage::disk($image->disk)->response($image->path, null, [
            'Content-Type' => $mimeType,
            'Content-Disposition' => 'inline; filename="' . $image->filename . '"',
            'Cache-Control' => 'public, max-age=86400',
        ]);
    }
    
    /**
     * Serve a thumbnail of an image from storage.
     *
     * @param  int  $id
     * @param  string  $size
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    public function thumbnail($id, $size)
    {
        // Validate size
        if (!in_array($size, ['small', 'medium', 'large'])) {
            abort(400, 'Invalid thumbnail size');
        }
        
        // Find the image
        $image = Image::findOrFail($id);
        
        // Get the thumbnail path
        $path = pathinfo($image->path);
        $thumbnailPath = $path['dirname'] . '/' . $path['filename'] . "_{$size}." . $path['extension'];
        
        // Check if the thumbnail exists
        if (Storage::disk($image->disk)->exists($thumbnailPath)) {
            // Get the file mime type
            $mimeType = $image->mime_type ?: Storage::disk($image->disk)->mimeType($thumbnailPath);
            
            // Return the thumbnail
            return Storage::disk($image->disk)->response($thumbnailPath, null, [
                'Content-Type' => $mimeType,
                'Content-Disposition' => 'inline; filename="' . $path['filename'] . "_{$size}." . $path['extension'] . '"',
                'Cache-Control' => 'public, max-age=86400',
            ]);
        }
        
        // If the thumbnail doesn't exist, serve the original image
        return $this->serve($id);
    }
    
    /**
     * Serve an image by path from storage.
     *
     * @param  string  $path
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    public function serveByPath($path)
    {
        $path = urldecode($path);
        
        // Check if the file exists
        if (!Storage::disk('s3')->exists($path)) {
            abort(404, 'Image not found in storage');
        }
        
        // Get the file mime type
        $mimeType = Storage::disk('s3')->mimeType($path);
        
        // Return the file as a streamed response
        return Storage::disk('s3')->response($path, null, [
            'Content-Type' => $mimeType,
            'Content-Disposition' => 'inline; filename="' . basename($path) . '"',
            'Cache-Control' => 'public, max-age=86400',
        ]);
    }
}
