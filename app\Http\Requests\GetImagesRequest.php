<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class GetImagesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'model_type' => 'required|string|in:produit,categorie,sous_categorie,sous_sous_categorie,collection,marque,produit_variante,carousel_slide',
            'model_id' => 'required|integer',
        ];
    }

    public function messages(): array
    {
        return [
            'model_type.required' => 'Le type de modèle est obligatoire.',
            'model_type.in' => 'Le type de modèle est invalide.',
            'model_id.required' => "L'identifiant du modèle est obligatoire.",
            'model_id.integer' => "L'identifiant du modèle doit être un entier.",
        ];
    }
}
