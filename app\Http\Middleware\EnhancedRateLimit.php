<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Http\Exceptions\ThrottleRequestsException;
use Illuminate\Support\Facades\Log;

class EnhancedRateLimit
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $group = 'api'): Response
    {
        // Check if rate limiting is disabled via environment variable
        if (env('RATE_LIMITING_ENABLED', true) === false || env('RATE_LIMITING_ENABLED', true) === 'false') {
            return $next($request);
        }

        $limits = $this->getLimits();
        $limit = $limits[$group] ?? $limits['api'];

        $key = $this->resolveRequestSignature($request, $group);

        // Check if the request should be rate limited
        if (RateLimiter::tooManyAttempts($key, $limit['attempts'])) {
            $this->logRateLimitExceeded($request, $group, $key);
            throw $this->buildException($key, $limit['attempts'], $limit['decay']);
        }

        // Increment the rate limiter
        RateLimiter::hit($key, $limit['decay']);

        $response = $next($request);

        // Add rate limit headers
        $this->addRateLimitHeaders($response, $key, $limit);

        return $response;
    }

    /**
     * Get rate limit configurations
     */
    protected function getLimits(): array
    {
        return [
            'auth' => [
                'attempts' => 999999,
                'decay' => 60,
            ],
            'api' => [
                'attempts' => 999999,
                'decay' => 60,
            ],
            'search' => [
                'attempts' => 999999,
                'decay' => 60,
            ],
            'upload' => [
                'attempts' => 999999,
                'decay' => 60,
            ],
            'newsletter' => [
                'attempts' => 999999,
                'decay' => 60,
            ],
            'checkout' => [
                'attempts' => 999999,
                'decay' => 60,
            ],
        ];
    }

    /**
     * Resolve the rate limiter key for the request
     */
    protected function resolveRequestSignature(Request $request, string $group): string
    {
        $identifier = $this->getIdentifier($request);
        $route = $request->route()?->getName() ?: $request->path();

        return "rate_limit:{$group}:{$identifier}:{$route}";
    }

    /**
     * Get the identifier for rate limiting
     */
    protected function getIdentifier(Request $request): string
    {
        // Use user ID if authenticated, otherwise IP address
        if ($user = $request->user()) {
            return "user:{$user->id}";
        }

        return "ip:{$request->ip()}";
    }

    /**
     * Add rate limit headers to the response
     */
    protected function addRateLimitHeaders(Response $response, string $key, array $limit): void
    {
        $attempts = RateLimiter::attempts($key);
        $remaining = max(0, $limit['attempts'] - $attempts);
        $retryAfter = RateLimiter::availableIn($key);

        $response->headers->add([
            'X-RateLimit-Limit' => $limit['attempts'],
            'X-RateLimit-Remaining' => $remaining,
            'X-RateLimit-Reset' => now()->addSeconds($retryAfter)->timestamp,
        ]);
    }

    /**
     * Log rate limit exceeded events
     */
    protected function logRateLimitExceeded(Request $request, string $group, string $key): void
    {
        Log::warning('Rate limit exceeded', [
            'group' => $group,
            'key' => $key,
            'ip' => $request->ip(),
            'user_id' => $request->user()?->id,
            'route' => $request->route()?->getName(),
            'path' => $request->path(),
            'method' => $request->method(),
            'user_agent' => $request->userAgent(),
        ]);
    }

    /**
     * Build the throttle exception
     */
    protected function buildException(string $key, int $maxAttempts, int $decayMinutes): ThrottleRequestsException
    {
        $retryAfter = RateLimiter::availableIn($key);

        return new ThrottleRequestsException(
            'Too many attempts. Please try again in ' . $retryAfter . ' seconds.',
            null,
            $this->getHeaders($maxAttempts, 0, $retryAfter)
        );
    }

    /**
     * Get the headers for the throttle exception
     */
    protected function getHeaders(int $maxAttempts, int $remainingAttempts, int $retryAfter): array
    {
        return [
            'X-RateLimit-Limit' => $maxAttempts,
            'X-RateLimit-Remaining' => $remainingAttempts,
            'X-RateLimit-Reset' => now()->addSeconds($retryAfter)->timestamp,
            'Retry-After' => $retryAfter,
        ];
    }
}
