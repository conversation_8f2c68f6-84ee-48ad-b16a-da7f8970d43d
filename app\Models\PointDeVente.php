<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PointDeVente extends Model
{
    protected $table = 'points_de_vente';
    
    protected $fillable = [
        'nom',
        'adresse',
        'telephone',
        'email',
        'remise',
        'description',
        'statut'
    ];
    
    /**
     * Get the users (clients) associated with this point of sale
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }
    
    /**
     * Get all orders from clients of this point of sale
     */
    public function commandes()
    {
        return $this->hasManyThrough(Commande::class, User::class);
    }
}
