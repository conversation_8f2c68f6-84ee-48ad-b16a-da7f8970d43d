<?php

namespace App\Http\Controllers;

use App\Models\Produit;
use App\Models\StockHistorique;
use App\Services\StockService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Http\Requests\AjouterStockRequest;
use App\Http\Requests\RetirerStockRequest;
use App\Http\Requests\AjusterStockRequest;

class StockController extends Controller
{
    protected $stockService;

    public function __construct(StockService $stockService)
    {
        $this->stockService = $stockService;
    }

    /**
     * Obtenir l'historique des mouvements de stock d'un produit
     *
     * @param int $produitId ID du produit
     * @return \Illuminate\Http\JsonResponse
     */
    public function getHistorique($produitId)
    {
        try {
            $produit = Produit::findOrFail($produitId);
            $historique = $produit->stockHistorique()->with('user:id,name')->paginate(15);

            return response()->json([
                'produit' => [
                    'id' => $produit->id,
                    'nom' => $produit->nom_produit,
                    'reference' => $produit->reference,
                    'quantite_actuelle' => $produit->quantite_produit,
                    'en_stock' => $produit->en_stock,
                    'stock_limite' => $produit->stock_limite
                ],
                'historique' => $historique
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erreur lors de la récupération de l\'historique de stock',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Ajouter du stock à un produit
     *
     * @param AjouterStockRequest $request
     * @param int $produitId ID du produit
     * @return \Illuminate\Http\JsonResponse
     */
    public function ajouterStock(AjouterStockRequest $request, $produitId)
    {
        try {
            $produit = Produit::findOrFail($produitId);
            $historique = $this->stockService->ajouterStock(
                $produitId,
                $request->input('quantite'),
                $request->input('reference'),
                $request->input('commentaire')
            );

            return response()->json([
                'message' => 'Stock ajouté avec succès',
                'produit' => [
                    'id' => $produit->id,
                    'nom' => $produit->nom_produit,
                    'quantite_precedente' => $historique->quantite_avant,
                    'quantite_actuelle' => $historique->quantite_apres,
                    'quantite_ajoutee' => $historique->quantite_modifiee
                ],
                'mouvement' => $historique
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erreur lors de l\'ajout de stock',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Retirer du stock d'un produit
     *
     * @param RetirerStockRequest $request
     * @param int $produitId ID du produit
     * @return \Illuminate\Http\JsonResponse
     */
    public function retirerStock(RetirerStockRequest $request, $produitId)
    {
        try {
            $produit = Produit::findOrFail($produitId);

            // Vérifier si le stock est suffisant
            if (!$this->stockService->verifierStockDisponible($produitId, $request->input('quantite'))) {
                return response()->json([
                    'error' => 'Stock insuffisant',
                    'message' => "Le stock actuel ({$produit->quantite_produit}) est inférieur à la quantité demandée ({$request->input('quantite')})"
                ], 400);
            }

            $historique = $this->stockService->retirerStock(
                $produitId,
                $request->input('quantite'),
                $request->input('reference'),
                $request->input('commentaire')
            );

            return response()->json([
                'message' => 'Stock retiré avec succès',
                'produit' => [
                    'id' => $produit->id,
                    'nom' => $produit->nom_produit,
                    'quantite_precedente' => $historique->quantite_avant,
                    'quantite_actuelle' => $historique->quantite_apres,
                    'quantite_retiree' => abs($historique->quantite_modifiee)
                ],
                'mouvement' => $historique
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erreur lors du retrait de stock',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Ajuster le stock d'un produit à une valeur spécifique
     *
     * @param AjusterStockRequest $request
     * @param int $produitId ID du produit
     * @return \Illuminate\Http\JsonResponse
     */
    public function ajusterStock(AjusterStockRequest $request, $produitId)
    {
        try {
            $produit = Produit::findOrFail($produitId);
            $historique = $this->stockService->ajusterStock(
                $produitId,
                $request->input('quantite'),
                $request->input('commentaire')
            );

            return response()->json([
                'message' => 'Stock ajusté avec succès',
                'produit' => [
                    'id' => $produit->id,
                    'nom' => $produit->nom_produit,
                    'quantite_precedente' => $historique->quantite_avant,
                    'quantite_actuelle' => $historique->quantite_apres,
                    'difference' => $historique->quantite_modifiee
                ],
                'mouvement' => $historique
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erreur lors de l\'ajustement de stock',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Obtenir les produits en rupture de stock
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProduitsEnRupture()
    {
        try {
            $produits = $this->stockService->getProduitsEnRupture();

            return response()->json([
                'count' => $produits->count(),
                'produits' => $produits->map(function ($produit) {
                    return [
                        'id' => $produit->id,
                        'nom' => $produit->nom_produit,
                        'reference' => $produit->reference,
                        'quantite' => $produit->quantite_produit,
                        'marque' => $produit->marque ? $produit->marque->nom_marque : null,
                        'image' => $produit->primary_image_url
                    ];
                })
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erreur lors de la récupération des produits en rupture',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Obtenir les produits en stock limité
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProduitsStockLimite(Request $request)
    {
        try {
            $seuil = $request->input('seuil', 5);
            $produits = $this->stockService->getProduitsStockLimite($seuil);

            return response()->json([
                'count' => $produits->count(),
                'seuil' => $seuil,
                'produits' => $produits->map(function ($produit) {
                    return [
                        'id' => $produit->id,
                        'nom' => $produit->nom_produit,
                        'reference' => $produit->reference,
                        'quantite' => $produit->quantite_produit,
                        'marque' => $produit->marque ? $produit->marque->nom_marque : null,
                        'image' => $produit->primary_image_url
                    ];
                })
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erreur lors de la récupération des produits en stock limité',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
