<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('paiements', function (Blueprint $table) {
            // Rename columns
            if (Schema::hasColumn('paiements', 'montant_paiement')) {
                // Ensure existing data is numeric before changing type
                DB::statement("UPDATE paiements SET montant_paiement = '0' WHERE montant_paiement = '' OR montant_paiement IS NULL OR montant_paiement ~ '[^0-9.]'");
                $table->decimal('montant', 10, 2)->default(0);
            } else if (!Schema::hasColumn('paiements', 'montant')) {
                $table->decimal('montant', 10, 2)->default(0);
            }

            if (Schema::hasColumn('paiements', 'statut_paiement')) {
                $table->renameColumn('statut_paiement', 'status');
            } else if (!Schema::hasColumn('paiements', 'status')) {
                $table->string('status');
            }

            if (Schema::hasColumn('paiements', 'date_paiement')) {
                $table->renameColumn('date_paiement', 'processed_at');
                // Change type to timestamp, ensure existing data is compatible or handled
                DB::statement("UPDATE paiements SET processed_at = NULL WHERE processed_at = '' OR processed_at IS NULL"); // Handle empty strings
                DB::statement("ALTER TABLE paiements ALTER COLUMN processed_at TYPE TIMESTAMP USING processed_at::timestamp WITHOUT TIME ZONE");

            } else if (!Schema::hasColumn('paiements', 'processed_at')) {
                $table->timestamp('processed_at')->nullable();
            }

            // Add new columns if they don't exist
            if (!Schema::hasColumn('paiements', 'transaction_id')) {
                $table->string('transaction_id')->nullable()->after('methode_paiement');
            }
            if (!Schema::hasColumn('paiements', 'gateway_response')) {
                $table->json('gateway_response')->nullable()->after('transaction_id');
            }
        });

        // Populate the new montant column from the old one if it existed and was renamed indirectly
        if (Schema::hasColumn('paiements', 'montant_paiement') && Schema::hasColumn('paiements', 'montant')) {
            DB::statement('UPDATE paiements SET montant = montant_paiement::DECIMAL(10,2) WHERE montant_paiement IS NOT NULL');
            Schema::table('paiements', function (Blueprint $table) {
                $table->dropColumn('montant_paiement');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('paiements', function (Blueprint $table) {
            // Drop new columns
            $table->dropColumn(['transaction_id', 'gateway_response']);

            // Rename columns back
            if (Schema::hasColumn('paiements', 'montant')) {
                $table->string('montant_paiement')->nullable(); // Revert to string or original type
            } else if (!Schema::hasColumn('paiements', 'montant_paiement')) {
                $table->string('montant_paiement')->nullable();
            }

            if (Schema::hasColumn('paiements', 'status')) {
                $table->renameColumn('status', 'statut_paiement');
            } else if (!Schema::hasColumn('paiements', 'statut_paiement')) {
                $table->string('statut_paiement');
            }

            if (Schema::hasColumn('paiements', 'processed_at')) {
                $table->renameColumn('processed_at', 'date_paiement');
                DB::statement("ALTER TABLE paiements ALTER COLUMN date_paiement TYPE VARCHAR(255) USING date_paiement::VARCHAR(255)");
            } else if (!Schema::hasColumn('paiements', 'date_paiement')) {
                $table->string('date_paiement')->nullable();
            }
        });
        if (Schema::hasColumn('paiements', 'montant') && Schema::hasColumn('paiements', 'montant_paiement')) {
            DB::statement('UPDATE paiements SET montant_paiement = montant::VARCHAR(255) WHERE montant IS NOT NULL');
            Schema::table('paiements', function (Blueprint $table) {
                $table->dropColumn('montant');
            });
        }
    }
};
