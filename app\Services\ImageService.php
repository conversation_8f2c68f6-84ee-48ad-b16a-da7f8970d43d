<?php

namespace App\Services;

use App\Models\Image;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class ImageService
{
    /**
     * The image manager instance.
     *
     * @var \Intervention\Image\ImageManager
     */
    protected $imageManager;

    /**
     * The storage disk to use.
     *
     * @var string
     */
    protected $disk;

    /**
     * The available thumbnail sizes.
     *
     * @var array
     */
    protected $thumbnailSizes = [
        'small' => [150, 150],
        'medium' => [300, 300],
        'large' => [600, 600],
    ];

    /**
     * Create a new image service instance.
     *
     * @return void
     */
    public function __construct()
    {
        // Check if GD extension is available
        if (extension_loaded('gd')) {
            $this->imageManager = new ImageManager(new Driver());
        } else {
            // For testing without GD extension
            $this->imageManager = null;
        }

        $this->disk = config('filesystems.default', 's3');
    }

    /**
     * Upload an image and create thumbnails.
     *
     * @param \Illuminate\Http\UploadedFile $file
     * @param string $directory
     * @param array $options
     * @return \App\Models\Image
     */
    public function upload(UploadedFile $file, string $directory, array $options = []): Image
    {
        try {
            // Generate a unique filename
            $filename = $this->generateFilename($file);

            // Set the path where the image will be stored
            $path = $directory . '/' . $filename;

            // Check if the file exists and is readable
            if (!$file->isValid()) {
                throw new \Exception("The uploaded file is not valid. Error code: {$file->getError()}");
            }

            $realPath = $file->getRealPath();
            if (!$realPath || !file_exists($realPath) || !is_readable($realPath)) {
                throw new \Exception("Cannot read the uploaded file. File does not exist or is not readable.");
            }

            // Get the image content
            $content = file_get_contents($realPath);
            if ($content === false) {
                throw new \Exception("Failed to read the content of the uploaded file.");
            }

            // Optimize the image if needed and GD is available
            if (!empty($options['optimize']) && $this->imageManager !== null) {
                // Add mime type to options for proper encoding
                $options['mime_type'] = $file->getMimeType();
                $content = $this->optimizeImage($content, $options);
            }

            // Store the original image
            $result = Storage::disk($this->disk)->put($path, $content, 'public');
            if (!$result) {
                throw new \Exception("Failed to store the image on the disk. Check disk permissions and configuration.");
            }
        } catch (\Exception $e) {
            \Log::error('Error in ImageService::upload', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'file' => $file->getClientOriginalName(),
                'directory' => $directory,
                'disk' => $this->disk,
            ]);
            throw $e; // Re-throw the exception to be caught by the controller
        }

        // Create thumbnails if needed and GD is available
        if (!empty($options['thumbnails']) && $this->imageManager !== null) {
            $this->createThumbnails($file, $directory, $filename, $options);
        }

        // Create metadata
        $metadata = [];
        if ($this->imageManager !== null) {
            $metadata = $this->getImageMetadata($file);
        } else {
            // Basic metadata when GD is not available
            $metadata = [
                'original_filename' => $file->getClientOriginalName(),
                'extension' => $file->getClientOriginalExtension(),
            ];
        }

        // Create the image data
        $imageData = [
            'path' => $path,
            'filename' => $file->getClientOriginalName(),
            'disk' => $this->disk,
            'mime_type' => $file->getMimeType(),
            'size' => $file->getSize(),
            'alt_text' => $options['alt_text'] ?? null,
            'title' => $options['title'] ?? null,
            'is_primary' => $options['is_primary'] ?? false,
            'order' => $options['order'] ?? 0,
            'metadata' => $metadata,
        ];

        // If model is provided, set the imageable fields
        if (!empty($options['model'])) {
            $imageData['imageable_type'] = get_class($options['model']);
            $imageData['imageable_id'] = $options['model']->id;
        }

        // Create and return the image model
        return Image::create($imageData);
    }

    /**
     * Upload multiple images.
     *
     * @param array $files
     * @param string $directory
     * @param array $options
     * @return \Illuminate\Support\Collection
     */
    public function uploadMultiple(array $files, string $directory, array $options = [])
    {
        $images = collect();

        foreach ($files as $index => $file) {
            $fileOptions = $options;

            // Set the first image as primary if not specified
            if ($index === 0 && !isset($options['is_primary'])) {
                $fileOptions['is_primary'] = true;
            }

            // Set the order if not specified
            if (!isset($fileOptions['order'])) {
                $fileOptions['order'] = $index;
            }

            $images->push($this->upload($file, $directory, $fileOptions));
        }

        return $images;
    }

    /**
     * Attach an image to a model.
     *
     * @param \App\Models\Image $image
     * @param mixed $model
     * @return void
     */
    public function attachToModel(Image $image, $model): void
    {
        // Check if the image is already attached to a model
        if ($image->imageable_type && $image->imageable_id) {
            // If it's already attached to the same model, just update primary status if needed
            if ($image->imageable_type === get_class($model) && $image->imageable_id === $model->id) {
                // If this is a primary image, ensure it's the only primary one
                if ($image->is_primary) {
                    $model->images()
                        ->where('id', '!=', $image->id)
                        ->update(['is_primary' => false]);
                }
                return;
            }

            // If it's attached to a different model, detach it first
            $image->imageable_type = null;
            $image->imageable_id = null;
            $image->save();
        }

        // Attach the image to the model
        $model->images()->save($image);

        // If this is a primary image, ensure it's the only primary one
        if ($image->is_primary) {
            $model->images()
                ->where('id', '!=', $image->id)
                ->update(['is_primary' => false]);
        }
    }

    /**
     * Generate a unique filename for the image.
     *
     * @param \Illuminate\Http\UploadedFile $file
     * @return string
     */
    protected function generateFilename(UploadedFile $file): string
    {
        $extension = $file->getClientOriginalExtension();
        $name = Str::slug(pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME));
        $uniqueId = Str::random(10);

        return "{$name}_{$uniqueId}.{$extension}";
    }

    /**
     * Optimize the image content.
     *
     * @param string $content
     * @param array $options
     * @return string
     */
    protected function optimizeImage(string $content, array $options): string
    {
        try {
            $image = $this->imageManager->read($content);

            // Resize if dimensions are provided
            if (!empty($options['width']) && !empty($options['height'])) {
                $image->resize($options['width'], $options['height'], function ($constraint) use ($options) {
                    if (!empty($options['aspect']) && $options['aspect']) {
                        $constraint->aspectRatio();
                    }

                    if (!empty($options['upsize']) && $options['upsize']) {
                        $constraint->upsize();
                    }
                });
            }

            // Set the quality
            $quality = $options['quality'] ?? 80;

            // Determine the mime type and use appropriate encoding
            $mimeType = $options['mime_type'] ?? 'image/jpeg';

            // Use specific encoding methods based on mime type to avoid the encodeByMediaType issue
            if (strpos($mimeType, 'jpeg') !== false || strpos($mimeType, 'jpg') !== false) {
                return $image->toJpeg($quality);
            } elseif (strpos($mimeType, 'png') !== false) {
                return $image->toPng();
            } elseif (strpos($mimeType, 'gif') !== false) {
                return $image->toGif();
            } elseif (strpos($mimeType, 'webp') !== false) {
                return $image->toWebp($quality);
            } else {
                // Fallback to JPEG for unknown types
                return $image->toJpeg($quality);
            }
        } catch (\Exception $e) {
            \Log::error('Error optimizing image', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Return original content if optimization fails
            return $content;
        }
    }

    /**
     * Create thumbnails for the image.
     *
     * @param \Illuminate\Http\UploadedFile $file
     * @param string $directory
     * @param string $filename
     * @param array $options
     * @return void
     */
    protected function createThumbnails(UploadedFile $file, string $directory, string $filename, array $options): void
    {
        try {
            $image = $this->imageManager->read($file->getRealPath());
            $pathInfo = pathinfo($filename);
            $mimeType = $file->getMimeType();
            $quality = $options['quality'] ?? 80;

            foreach ($this->thumbnailSizes as $size => $dimensions) {
                if (empty($options['thumbnails']) || in_array($size, $options['thumbnails'])) {
                    $thumbnailFilename = $pathInfo['filename'] . "_{$size}." . $pathInfo['extension'];
                    $thumbnailPath = $directory . '/' . $thumbnailFilename;

                    $thumbnail = clone $image;
                    $thumbnail->resize($dimensions[0], $dimensions[1], function ($constraint) {
                        $constraint->aspectRatio();
                        $constraint->upsize();
                    });

                    // Use specific encoding methods based on mime type
                    $content = null;
                    if (strpos($mimeType, 'jpeg') !== false || strpos($mimeType, 'jpg') !== false) {
                        $content = $thumbnail->toJpeg($quality);
                    } elseif (strpos($mimeType, 'png') !== false) {
                        $content = $thumbnail->toPng();
                    } elseif (strpos($mimeType, 'gif') !== false) {
                        $content = $thumbnail->toGif();
                    } elseif (strpos($mimeType, 'webp') !== false) {
                        $content = $thumbnail->toWebp($quality);
                    } else {
                        // Fallback to JPEG for unknown types
                        $content = $thumbnail->toJpeg($quality);
                    }

                    Storage::disk($this->disk)->put($thumbnailPath, $content, 'public');
                }
            }
        } catch (\Exception $e) {
            \Log::error('Error creating thumbnails', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'file' => $file->getClientOriginalName(),
                'directory' => $directory
            ]);
            // Continue without thumbnails if there's an error
        }
    }

    /**
     * Get metadata for the image.
     *
     * @param \Illuminate\Http\UploadedFile $file
     * @return array
     */
    protected function getImageMetadata(UploadedFile $file): array
    {
        if ($this->imageManager === null) {
            return [
                'original_filename' => $file->getClientOriginalName(),
                'extension' => $file->getClientOriginalExtension(),
            ];
        }

        $image = $this->imageManager->read($file->getRealPath());

        return [
            'width' => $image->width(),
            'height' => $image->height(),
            'original_filename' => $file->getClientOriginalName(),
            'extension' => $file->getClientOriginalExtension(),
        ];
    }

    /**
     * Delete an image and its thumbnails.
     *
     * @param \App\Models\Image $image
     * @return bool
     */
    public function delete(Image $image): bool
    {
        // Force delete the image model which will trigger the deleting event
        // that handles the file deletion
        return $image->forceDelete();
    }
}
