<?php

namespace App\Jobs;

use App\Mail\NewsletterMailable;
use App\Models\Collection;
use App\Models\Promotion;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendNewsletterJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 300; // 5 minutes timeout
    public $tries = 2;

    protected $userIds;
    protected $batchSize;

    public function __construct(array $userIds, int $batchSize = 50)
    {
        $this->userIds = $userIds;
        $this->batchSize = $batchSize;
        $this->onQueue('emails'); // Use dedicated queue for emails
    }

    public function handle(): void
    {
        Log::info('Starting newsletter batch send', [
            'user_count' => count($this->userIds),
            'batch_size' => $this->batchSize
        ]);

        // Get active promotions and collections once
        $promotions = Promotion::where('statut', 'active')
            ->whereDate('date_fin', '>=', now())
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        $collections = Collection::orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Process users in chunks to prevent memory issues
        $chunks = array_chunk($this->userIds, $this->batchSize);
        $sent = 0;
        $failed = 0;

        foreach ($chunks as $chunk) {
            $users = User::whereIn('id', $chunk)
                ->where('newsletter_enabled', true)
                ->get();

            foreach ($users as $user) {
                try {
                    Mail::to($user->email)->send(new NewsletterMailable($promotions, $collections));
                    $sent++;

                    // Small delay to prevent overwhelming mail server
                    usleep(100000); // 0.1 second

                } catch (\Exception $e) {
                    $failed++;
                    Log::error('Failed to send newsletter to user', [
                        'user_id' => $user->id,
                        'email' => $user->email,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            // Delay between chunks
            sleep(1); // 1 second
        }

        Log::info('Newsletter batch send completed', [
            'sent' => $sent,
            'failed' => $failed,
            'total_attempted' => count($this->userIds)
        ]);
    }

    public function failed(\Throwable $exception): void
    {
        Log::error('Newsletter job failed permanently', [
            'user_count' => count($this->userIds),
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
