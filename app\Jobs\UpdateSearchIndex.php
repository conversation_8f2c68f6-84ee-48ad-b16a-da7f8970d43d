<?php

namespace App\Jobs;

use App\Models\Produit;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class UpdateSearchIndex implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 120; // 2 minutes timeout
    public $tries = 3;

    protected $product;

    public function __construct(Produit $product)
    {
        $this->product = $product;
        $this->onQueue('search'); // Use dedicated queue for search indexing
    }

    public function handle(): void
    {
        Log::info('Updating search index for product', [
            'product_id' => $this->product->id,
            'product_name' => $this->product->nom_produit
        ]);

        try {
            // Load necessary relationships for indexing
            $this->product->load([
                'marque:id,nom_marque',
                'sousSousCategorie.sousCategorie.categorie',
                'collections:id,nom',
                'caracteristiques.attribut:id,nom'
            ]);

            // Prepare search data
            $searchData = [
                'id' => $this->product->id,
                'nom_produit' => $this->product->nom_produit,
                'description' => $this->product->description,
                'prix_produit' => $this->product->prix_produit,
                'marque' => $this->product->marque?->nom_marque,
                'categorie' => $this->getCategoryPath(),
                'collections' => $this->product->collections->pluck('nom')->toArray(),
                'caracteristiques' => $this->getCharacteristics(),
                'active' => $this->product->active,
                'created_at' => $this->product->created_at,
                'updated_at' => $this->product->updated_at,
            ];

            // Here you would implement your search engine integration
            // Examples: Elasticsearch, Algolia, MeiliSearch, etc.

            // For now, we'll just log the action
            // In production, replace with actual search engine calls
            Log::info('Search index updated successfully', [
                'product_id' => $this->product->id,
                'search_data_keys' => array_keys($searchData)
            ]);

            // Example for future implementation:
            // ElasticsearchService::updateDocument('products', $this->product->id, $searchData);
            // or
            // AlgoliaService::saveObject($searchData);

        } catch (\Exception $e) {
            Log::error('Failed to update search index', [
                'product_id' => $this->product->id,
                'error' => $e->getMessage()
            ]);

            throw $e; // Re-throw to trigger retry mechanism
        }
    }

    private function getCategoryPath(): string
    {
        $path = [];

        if ($this->product->sousSousCategorie) {
            $sousCategorie = $this->product->sousSousCategorie->sousCategorie;
            if ($sousCategorie && $sousCategorie->categorie) {
                $path[] = $sousCategorie->categorie->nom;
            }
            if ($sousCategorie) {
                $path[] = $sousCategorie->nom;
            }
            $path[] = $this->product->sousSousCategorie->nom;
        }

        return implode(' > ', $path);
    }

    private function getCharacteristics(): array
    {
        return $this->product->caracteristiques->map(function ($caracteristique) {
            return [
                'attribut' => $caracteristique->attribut->nom,
                'valeur' => $caracteristique->valeur
            ];
        })->toArray();
    }

    public function failed(\Throwable $exception): void
    {
        Log::error('Search index update job failed permanently', [
            'product_id' => $this->product->id,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
