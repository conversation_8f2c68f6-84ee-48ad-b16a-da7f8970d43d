<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Str;

class User extends Authenticatable
{
    use HasFactory, Notifiable;

    protected $fillable = [
        'name',
        'email',
        'password',
        'keycloak_id',
        'roles',
        'point_de_vente_id',
        'groupe_client_id',
        'remise_personnelle',
        'profil_remise',
        'newsletter_subscribed'
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'roles' => 'array',
            'remise_personnelle' => 'decimal:2'
        ];
    }

    /**
     * Set profil_remise attribute
     *
     * @param string $value
     * @return void
     */
    public function setProfilRemiseAttribute($value)
    {
        $this->attributes['profil_remise'] = $value;
    }

    /**
     * Sync user data with Keycloak information
     *
     * @param array $keycloakUser
     * @param array $roles
     * @return User
     */
    public static function syncWithKeycloak(array $keycloakUser, array $roles): User
    {
        // First try to find user by keycloak_id
        $user = static::where('keycloak_id', $keycloakUser['sub'])->first();

        // If not found by keycloak_id, try to find by email
        if (!$user) {
            $user = static::where('email', $keycloakUser['email'])->first();
        }

        // Ensure the client role is included
        if (!in_array('client', $roles)) {
            $roles[] = 'client';
        }

        // Determine remise profile based on roles
        $profilRemise = 'standard';

        if ($user) {
            // For existing users, keep their profile if already set
            $profilRemise = $user->getRawOriginal('profil_remise') ?: $user->getProfilRemiseAttribute(null);

            // Update existing user
            $user->update([
                'keycloak_id' => $keycloakUser['sub'],
                'name' => $keycloakUser['name'],
                'email' => $keycloakUser['email'],
                'roles' => $roles
            ]);
        } else {
            // Create new user
            $user = static::create([
                'keycloak_id' => $keycloakUser['sub'],
                'name' => $keycloakUser['name'],
                'email' => $keycloakUser['email'],
                'password' => bcrypt(Str::random(32)),
                'email_verified_at' => now(),
                'roles' => $roles,
                'profil_remise' => $profilRemise
            ]);
        }

        // Ensure type_client and roles are consistent
        self::ensureRoleTypeConsistency($user);

        return $user;
    }

    /**
     * Ensure consistency between user roles and remise profile
     *
     * @param User $user
     * @return void
     */
    protected static function ensureRoleTypeConsistency(User $user): void
    {
        $roles = $user->roles ?? [];
        $updated = false;

        // Ensure all users have the client role
        if (!in_array('client', $roles)) {
            $roles[] = 'client';
            $updated = true;
        }

        // Determine profil_remise based on roles
        $newProfilRemise = null;

        // Check for partenaire role
        if (in_array('partenaire', $roles)) {
            $newProfilRemise = 'premium';
        }
        // Check for point_de_vente role
        else if (in_array('point_de_vente', $roles)) {
            $newProfilRemise = 'affilie';
        }
        // Check for groupe role (if you have one)
        else if (in_array('groupe', $roles)) {
            $newProfilRemise = 'groupe';
        }
        // Default to standard
        else {
            $newProfilRemise = 'standard';
        }

        // Update profil_remise if it has changed
        if ($newProfilRemise && $user->profil_remise !== $newProfilRemise) {
            $user->profil_remise = $newProfilRemise;
            $updated = true;
        }

        // Update if changed
        if ($updated) {
            $user->save();
        }
    }

    /**
     * Check if the user has a specific role
     */
    public function hasRole(string $role): bool
    {
        return in_array($role, $this->roles ?? []);
    }

    /**
     * Check if the user has any of the given roles
     */
    public function hasAnyRole(array $roles): bool
    {
        return !empty(array_intersect($roles, $this->roles ?? []));
    }

    public function Commandes()
    {
        if ($this->hasRole('client')) {
            return $this->hasMany(Commande::class);
        }
        return $this->hasMany(Commande::class)->whereRaw('1 = 0');
    }
    public function Produits()
    {
        if ($this->hasRole('admin')) {
            return $this->hasMany(Produit::class);
        }
        return $this->hasMany(Produit::class)->whereRaw('1 = 0');
    }

    public function partenaire()
    {
        return $this->hasOne(Partenaire::class);
    }

    public function pointDeVente()
    {
        return $this->belongsTo(PointDeVente::class);
    }

    /**
     * Get the client group this user belongs to
     */
    public function groupeClient()
    {
        return $this->belongsTo(GroupeClient::class);
    }

    /**
     * Get the partner requests made by this user
     */
    public function partnerRequests()
    {
        return $this->hasMany(PartnerRequest::class);
    }

    /**
     * Get the distributor requests made by this user
     */
    public function distributorRequests()
    {
        return $this->hasMany(DistributorRequest::class);
    }

    /**
     * Get the latest partner request made by this user
     */
    public function latestPartnerRequest()
    {
        return $this->hasOne(PartnerRequest::class)->latest();
    }

    /**
     * Get the latest distributor request made by this user
     */
    public function latestDistributorRequest()
    {
        return $this->hasOne(DistributorRequest::class)->latest();
    }

    /**
     * Get the effective discount rate for this user
     *
     * @return float The discount percentage (0-100)
     */
    public function getRemiseEffective(): float
    {
        // Personal discount takes precedence if set
        if ($this->remise_personnelle > 0) {
            return $this->remise_personnelle;
        }

        // Otherwise check client type
        switch ($this->profil_remise) {
            case 'premium':
                $partenaire = $this->partenaire;
                return $partenaire ? $partenaire->remise : 15; // Default premium discount

            case 'affilie':
                $pdv = $this->pointDeVente;
                return $pdv ? $pdv->remise : 10; // Default affiliate discount

            case 'groupe':
                $groupe = $this->groupeClient;
                return $groupe ? $groupe->remise : 5; // Default group discount

            default:
                return 0; // Standard clients get no discount by default
        }
    }
}
