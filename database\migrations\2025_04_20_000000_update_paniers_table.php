<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('paniers', function (Blueprint $table) {
            if (!Schema::hasColumn('paniers', 'client_id')) {
                $table->foreignId('client_id')->nullable()->constrained('users')->nullOnDelete();
            }
            
            if (!Schema::hasColumn('paniers', 'session_id')) {
                $table->string('session_id')->nullable()->index();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('paniers', function (Blueprint $table) {
            if (Schema::hasColumn('paniers', 'client_id')) {
                $table->dropForeign(['client_id']);
                $table->dropColumn('client_id');
            }
            
            if (Schema::hasColumn('paniers', 'session_id')) {
                $table->dropColumn('session_id');
            }
        });
    }
};
