<?php

namespace App\Services\Keycloak;

use Exception;
use Illuminate\Support\Facades\Http;

class KeycloakUserService
{
    private KeycloakAdminService $adminService;

    public function __construct(KeycloakAdminService $adminService)
    {
        $this->adminService = $adminService;
    }

    public function createUser(string $name, string $email, string $password, array $roles = ['client']): array
    {
        $adminToken = $this->adminService->getAdminToken();
        $userData = $this->prepareUserData($name, $email, $password);

        $keycloakId = $this->createKeycloakUser($userData, $adminToken);

        foreach ($roles as $role) {
            $this->assignRole($keycloakId, $role, $adminToken);
        }

        return [
            'keycloak_id' => $keycloakId,
            'name' => $name,
            'email' => $email,
        ];
    }

    private function prepareUserData(string $name, string $email, string $password): array
    {
        $nameParts = explode(' ', $name, 2);
        return [
            'username' => $email,
            'email' => $email,
            'firstName' => $nameParts[0],
            'lastName' => $nameParts[1] ?? '',
            'enabled' => true,
            'emailVerified' => false,
            'credentials' => [
                [
                    'type' => 'password',
                    'value' => $password,
                    'temporary' => false
                ]
            ]
        ];
    }

    private function createKeycloakUser(array $userData, string $adminToken): string
    {
        $response = Http::withToken($adminToken)
            ->post($this->getUsersEndpoint(), $userData);

        if (!$response->successful()) {
            throw new Exception('Failed to create Keycloak user: ' . $response->body());
        }

        // Extract user ID from Location header
        $location = $response->header('Location');
        return substr($location, strrpos($location, '/') + 1);
    }

    private function assignRole(string $userId, string $roleName, string $adminToken): void
    {
        // First get the role
        $role = $this->getRole($roleName, $adminToken);

        $response = Http::withToken($adminToken)
            ->post($this->getUserRolesEndpoint($userId), [$role]);

        if (!$response->successful()) {
            throw new Exception("Failed to assign role '$roleName' to user");
        }
    }

    private function getRole(string $roleName, string $adminToken): array
    {
        $response = Http::withToken($adminToken)
            ->get($this->getRolesEndpoint());

        if (!$response->successful()) {
            throw new Exception('Failed to fetch Keycloak roles');
        }

        $roles = $response->json();
        $role = collect($roles)->firstWhere('name', $roleName);

        if (!$role) {
            throw new Exception("Role '$roleName' not found");
        }

        return $role;
    }

    private function getUsersEndpoint(): string
    {
        return sprintf(
            '%s/admin/realms/%s/users',
            config('services.keycloak.base_url'),
            config('services.keycloak.realm')
        );
    }

    private function getUserRolesEndpoint(string $userId): string
    {
        return sprintf(
            '%s/admin/realms/%s/users/%s/role-mappings/realm',
            config('services.keycloak.base_url'),
            config('services.keycloak.realm'),
            $userId
        );
    }

    private function getRolesEndpoint(): string
    {
        return sprintf(
            '%s/admin/realms/%s/roles',
            config('services.keycloak.base_url'),
            config('services.keycloak.realm')
        );
    }
}
