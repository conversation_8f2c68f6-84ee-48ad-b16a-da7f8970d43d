<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class ProduitVariante extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'produit_parent_id',
        'sku',
        'prix_supplement',
        'stock',
        'actif'
    ];

    protected $casts = [
        'prix_supplement' => 'decimal:2',
        'stock' => 'integer',
        'actif' => 'boolean'
    ];

    /**
     * Le produit parent de cette variante
     */
    public function produitParent()
    {
        return $this->belongsTo(Produit::class, 'produit_parent_id');
    }

    /**
     * Les valeurs d'attributs de cette variante
     */
    public function valeurs()
    {
        return $this->hasMany(VarianteValeur::class, 'produit_variante_id');
    }

    /**
     * Obtenir le prix total de la variante (prix du produit parent + supplément)
     */
    public function getPrixTotalAttribute()
    {
        if (!$this->produitParent) {
            return $this->prix_supplement;
        }

        return $this->produitParent->prix_produit + $this->prix_supplement;
    }

    /**
     * Vérifier si la variante est disponible en stock
     */
    public function getDisponibleAttribute()
    {
        return $this->actif && $this->stock > 0;
    }

    /**
     * Get all images for this product variant
     */
    public function images()
    {
        return $this->morphMany(Image::class, 'imageable')->orderBy('order');
    }

    /**
     * Get the primary image for this product variant
     */
    public function getPrimaryImageAttribute()
    {
        return $this->images()->where('is_primary', true)->first()
            ?? $this->images()->first()
            ?? $this->produitParent->primaryImage;
    }

    /**
     * Get the primary image URL for this product variant
     */
    public function getPrimaryImageUrlAttribute()
    {
        if ($this->primaryImage) {
            return $this->primaryImage->url;
        }

        // Fallback to parent product's image if no variant-specific images
        if ($this->produitParent) {
            return $this->produitParent->primaryImageUrl;
        }

        return null;
    }
}
