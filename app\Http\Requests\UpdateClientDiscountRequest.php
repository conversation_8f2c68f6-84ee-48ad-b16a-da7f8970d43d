<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateClientDiscountRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'remise_personnelle' => 'required|numeric|min:0|max:100',
        ];
    }

    public function messages()
    {
        return [
            'remise_personnelle.required' => 'Le champ remise_personnelle est obligatoire.',
            'remise_personnelle.numeric' => 'La remise doit être un nombre.',
            'remise_personnelle.min' => 'La remise doit être au moins 0.',
            'remise_personnelle.max' => 'La remise ne peut pas dépasser 100.',
        ];
    }
} 