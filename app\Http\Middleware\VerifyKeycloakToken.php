<?php

namespace App\Http\Middleware;

use App\Models\User;
use App\Services\KeycloakService;
use Closure;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class VerifyKeycloakToken
{
    protected KeycloakService $keycloakService;

    public function __construct(KeycloakService $keycloakService)
    {
        $this->keycloakService = $keycloakService;
    }

    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): mixed
    {
        // Skip token verification for authentication routes
        $skipRoutes = [
            'auth/verify',
            'auth/refresh',
        ];

        $currentRoute = $request->path();

        // Skip verification for specified routes
        foreach ($skipRoutes as $route) {
            if (str_contains($currentRoute, $route)) {
                return $next($request);
            }
        }

        // Get token from cookie
        $token = $request->cookie('access_token');

        // If no token in cookie, check for Authorization header
        if (!$token && $request->bearerToken()) {
            $token = $request->bearerToken();
        }

        // If still no token, return unauthorized
        if (!$token) {
            return response()->json(['error' => 'Unauthenticated'], 401);
        }

        try {
            // Validate token
            $decoded = $this->keycloakService->validateToken($token);

            // Debug token contents
            \Log::debug('Decoded token contents:', [
                'sub' => $decoded->sub ?? null,
                'name' => $decoded->name ?? null,
                'email' => $decoded->email ?? null,
                'roles' => $decoded->realm_access->roles ?? [],
                'full_token' => json_decode(json_encode($decoded), true)
            ]);
            // Add token payload to request for other middleware and controllers
            $request->attributes->add(['token_payload' => $decoded]);

            // Extract user information from the decoded token
            $keycloakId = $decoded->sub;
            $name = $decoded->name ?? '';
            $email = $decoded->email ?? '';
            $roles = $decoded->realm_access->roles ?? [];

            // Look up the user by Keycloak ID
            $user = User::where('keycloak_id', $keycloakId)->first();

            // If user exists in database, authenticate them
            if ($user) {
                // Update user data
                $user = User::syncWithKeycloak([
                    'sub' => $keycloakId,
                    'name' => $name,
                    'email' => $email
                ], $roles);

                auth()->login($user);
            } else {
                // Create new user
                $user = User::syncWithKeycloak([
                    'sub' => $keycloakId,
                    'name' => $name,
                    'email' => $email
                ], $roles);

                auth()->login($user);
            }

            return $next($request);
        } catch (Exception $e) {
            // If token is expired, try to refresh it
            if (str_contains($e->getMessage(), 'expired')) {
                $refreshToken = $request->cookie('refresh_token');

                if ($refreshToken) {
                    try {
                        $tokens = $this->keycloakService->refreshToken($refreshToken);

                        if ($tokens) {
                            // Create new cookies
                            $cookies = $this->keycloakService->createTokenCookies(
                                $tokens['access_token'],
                                $tokens['refresh_token'],
                                $tokens['id_token'] ?? null
                            );

                            // Create a response with the new cookies
                            $response = $next($request);

                            // Add the cookies to the response
                            foreach ($cookies as $cookie) {
                                $response->headers->setCookie($cookie);
                            }

                            // Add the new token to the request for further processing
                            $decoded = $this->keycloakService->validateToken($tokens['access_token']);
                            $request->attributes->add(['token_payload' => $decoded]);

                            return $response;
                        }
                    } catch (Exception $refreshException) {
                        report($refreshException);
                    }
                }
            }

            report($e);
            return response()->json(['error' => 'Invalid token', 'message' => $e->getMessage()], 401);
        }
    }
}



