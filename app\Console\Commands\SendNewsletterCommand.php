<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Promotion;
use App\Models\Collection;
use App\Mail\NewsletterMailable;
use App\Jobs\SendNewsletterJob;
use Illuminate\Support\Facades\Mail;

class SendNewsletterCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:send-newsletter-command';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Fetching newsletter subscribers...');
        $users = User::where('newsletter_subscribed', true)->get();

        if ($users->isEmpty()) {
            $this->info('No subscribers found.');
            return 0;
        }

        $userIds = $users->pluck('id')->toArray();
        $userCount = count($userIds);

        $this->info("Found {$userCount} subscribers.");

        // Split users into batches for queue processing
        $batchSize = 50; // Process 50 users per job
        $batches = array_chunk($userIds, $batchSize);
        $batchCount = count($batches);

        $this->info("Queuing {$batchCount} newsletter jobs (batch size: {$batchSize})...");

        foreach ($batches as $index => $batch) {
            // Dispatch each batch as a separate job with a delay to spread the load
            SendNewsletterJob::dispatch($batch, $batchSize)
                ->delay(now()->addSeconds($index * 30)); // 30-second delay between batches
        }

        $this->info("Newsletter jobs queued successfully!");
        $this->info("Run 'php artisan queue:work --queue=emails' to process the email queue.");

        return 0;
    }
}
