<?php

namespace App\Http\Controllers;

use App\Models\ListeSouhait;
use App\Models\ListeSouhaitItem;
use App\Models\Panier;
use App\Models\Produit;
use App\Models\ProduitVariante;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ListeSouhaitController extends Controller
{
    /**
     * Vérifie si l'utilisateur est authentifié
     *
     * @param Request $request
     * @param string $errorMessage Message d'erreur à afficher si l'utilisateur n'est pas authentifié
     * @return \Illuminate\Http\JsonResponse|null Retourne une réponse d'erreur si l'utilisateur n'est pas authentifié, null sinon
     */
    protected function checkAuthentication(Request $request, $errorMessage = 'Vous devez être connecté pour accéder à cette ressource')
    {
        // Vérifier d'abord si l'utilisateur est déjà authentifié via Auth
        if (Auth::check()) {
            return null;
        }

        // Essayer de récupérer le token depuis le cookie
        $token = $request->cookie('access_token');

        // Si pas de token dans le cookie, essayer l'en-tête Authorization
        if (!$token && $request->hasHeader('Authorization')) {
            $authHeader = $request->header('Authorization');
            if (strpos($authHeader, 'Bearer ') === 0) {
                $token = substr($authHeader, 7);
            }
        }

        // Si on a un token, essayer de l'utiliser pour authentifier l'utilisateur
        if ($token) {
            try {
                // Valider le token
                $keycloakService = app(\App\Services\KeycloakService::class);
                $decoded = $keycloakService->validateToken($token);

                // Trouver l'utilisateur par Keycloak ID
                $user = \App\Models\User::where('keycloak_id', $decoded->sub)->first();

                if ($user) {
                    // Connecter l'utilisateur
                    Auth::login($user);
                    return null;
                }
            } catch (\Exception $e) {
                // Échec de validation du token, journaliser l'erreur
                \Illuminate\Support\Facades\Log::warning('ListeSouhaitController::checkAuthentication - Échec de validation du token', [
                    'error' => $e->getMessage()
                ]);
            }
        }

        // Si on arrive ici, l'utilisateur n'est pas authentifié
        return response()->json([
            'status' => 'error',
            'message' => $errorMessage
        ], 401);
    }
    /**
     * Récupérer la liste de souhaits de l'utilisateur connecté
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        // Vérifier que l'utilisateur est connecté
        $authError = $this->checkAuthentication($request, 'Vous devez être connecté pour accéder à votre liste de souhaits');
        if ($authError) {
            return $authError;
        }

        $userId = Auth::id();

        // Récupérer la liste de souhaits par défaut de l'utilisateur
        $listeSouhait = ListeSouhait::where('client_id', $userId)
            ->where('par_defaut', true)
            ->first();

        // Si l'utilisateur n'a pas encore de liste de souhaits, en créer une
        if (!$listeSouhait) {
            $listeSouhait = ListeSouhait::create([
                'client_id' => $userId,
                'nom' => 'Ma liste de souhaits',
                'par_defaut' => true
            ]);
        }

        // Charger les items avec les produits et variantes
        $items = $listeSouhait->items()
            ->with(['produit', 'variante'])
            ->get()
            ->map(function ($item) {
                $produit = $item->produit;
                $variante = $item->variante;

                return [
                    'id' => $item->id,
                    'produit' => [
                        'id' => $produit->id,
                        'nom' => $produit->nom_produit,
                        'description' => $produit->description_produit,
                        'image' => $produit->image_produit ?? $produit->primary_image_url,
                        'prix' => $produit->prix_produit,
                        'reference' => $produit->reference,
                        'en_stock' => $produit->quantite_produit > 0
                    ],
                    'variante' => $variante ? [
                        'id' => $variante->id,
                        'nom' => $variante->nom,
                        'prix_supplement' => $variante->prix_supplement
                    ] : null,
                    'note' => $item->note,
                    'prix_reference' => $item->prix_reference,
                    'prix_actuel' => $item->getPrixActuel(),
                    'prix_a_change' => $item->prixAChange(),
                    'difference_prix' => $item->getDifferencePrix(),
                    'date_ajout' => $item->created_at
                ];
            });

        return response()->json([
            'status' => 'success',
            'data' => [
                'liste_souhait' => [
                    'id' => $listeSouhait->id,
                    'nom' => $listeSouhait->nom,
                    'description' => $listeSouhait->description,
                    'nombre_items' => $items->count()
                ],
                'items' => $items
            ]
        ]);
    }

    /**
     * Ajouter un produit à la liste de souhaits
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function addItem(Request $request)
    {
        // Vérifier que l'utilisateur est connecté
        $authError = $this->checkAuthentication($request, 'Vous devez être connecté pour ajouter un produit à votre liste de souhaits');
        if ($authError) {
            return $authError;
        }

        // Valider les données
        $validator = Validator::make($request->all(), [
            'produit_id' => 'required|exists:produits,id',
            'variante_id' => 'nullable|exists:produit_variantes,id',
            'note' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Données invalides',
                'errors' => $validator->errors()
            ], 422);
        }

        $userId = Auth::id();
        $produitId = $request->input('produit_id');
        $varianteId = $request->input('variante_id');
        $note = $request->input('note');

        // Vérifier que le produit existe
        $produit = Produit::findOrFail($produitId);

        // Vérifier que la variante existe si spécifiée
        if ($varianteId) {
            $variante = ProduitVariante::findOrFail($varianteId);

            // Vérifier que la variante appartient bien au produit
            if ($variante->produit_id != $produitId) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'La variante spécifiée n\'appartient pas à ce produit'
                ], 422);
            }
        }

        // Récupérer la liste de souhaits par défaut de l'utilisateur
        $listeSouhait = ListeSouhait::where('client_id', $userId)
            ->where('par_defaut', true)
            ->first();

        // Si l'utilisateur n'a pas encore de liste de souhaits, en créer une
        if (!$listeSouhait) {
            $listeSouhait = ListeSouhait::create([
                'client_id' => $userId,
                'nom' => 'Ma liste de souhaits',
                'par_defaut' => true
            ]);
        }

        // Vérifier si le produit est déjà dans la liste
        if ($listeSouhait->contientProduit($produitId, $varianteId)) {
            return response()->json([
                'status' => 'success',
                'message' => 'Ce produit est déjà dans votre liste de souhaits',
                'data' => $this->getListeSouhaitData($listeSouhait)
            ]);
        }

        // Ajouter le produit à la liste
        $listeSouhait->ajouterProduit($produitId, $varianteId, $note);

        return response()->json([
            'status' => 'success',
            'message' => 'Produit ajouté à votre liste de souhaits',
            'data' => $this->getListeSouhaitData($listeSouhait)
        ]);
    }

    /**
     * Supprimer un item de la liste de souhaits
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function removeItem(Request $request, $id)
    {
        // Vérifier que l'utilisateur est connecté
        $authError = $this->checkAuthentication($request, 'Vous devez être connecté pour supprimer un produit de votre liste de souhaits');
        if ($authError) {
            return $authError;
        }

        $userId = Auth::id();

        // Récupérer l'item
        $item = ListeSouhaitItem::findOrFail($id);

        // Vérifier que l'item appartient bien à l'utilisateur
        if ($item->listeSouhait->client_id != $userId) {
            return response()->json([
                'status' => 'error',
                'message' => 'Vous n\'êtes pas autorisé à supprimer cet item'
            ], 403);
        }

        // Récupérer la liste de souhaits pour la retourner après
        $listeSouhait = $item->listeSouhait;

        // Supprimer l'item
        $item->delete();

        return response()->json([
            'status' => 'success',
            'message' => 'Produit supprimé de votre liste de souhaits',
            'data' => $this->getListeSouhaitData($listeSouhait)
        ]);
    }

    /**
     * Vérifier si un produit est dans la liste de souhaits
     *
     * @param Request $request
     * @param int $produitId
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkProduct(Request $request, $produitId)
    {
        // Pour cette méthode, on ne renvoie pas d'erreur si l'utilisateur n'est pas connecté
        // On vérifie simplement s'il est connecté
        $token = $request->cookie('access_token');

        // Si pas de token dans le cookie, essayer l'en-tête Authorization
        if (!$token && $request->hasHeader('Authorization')) {
            $authHeader = $request->header('Authorization');
            if (strpos($authHeader, 'Bearer ') === 0) {
                $token = substr($authHeader, 7);
            }
        }

        // Si l'utilisateur n'est pas connecté, retourner false
        if (!Auth::check() && !$token) {
            return response()->json([
                'status' => 'success',
                'data' => [
                    'in_wishlist' => false
                ]
            ]);
        }

        $userId = Auth::id();
        $varianteId = $request->input('variante_id');

        // Récupérer la liste de souhaits par défaut de l'utilisateur
        $listeSouhait = ListeSouhait::where('client_id', $userId)
            ->where('par_defaut', true)
            ->first();

        // Si l'utilisateur n'a pas encore de liste de souhaits
        if (!$listeSouhait) {
            return response()->json([
                'status' => 'success',
                'data' => [
                    'in_wishlist' => false
                ]
            ]);
        }

        // Vérifier si le produit est dans la liste
        $inWishlist = $listeSouhait->contientProduit($produitId, $varianteId);

        return response()->json([
            'status' => 'success',
            'data' => [
                'in_wishlist' => $inWishlist
            ]
        ]);
    }

    /**
     * Déplacer un item de la liste de souhaits vers le panier
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function moveToCart(Request $request, $id)
    {
        // Vérifier que l'utilisateur est connecté
        $authError = $this->checkAuthentication($request, 'Vous devez être connecté pour déplacer un produit vers votre panier');
        if ($authError) {
            return $authError;
        }

        $userId = Auth::id();

        // Valider les données
        $validator = Validator::make($request->all(), [
            'quantite' => 'nullable|integer|min:1|max:100'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Données invalides',
                'errors' => $validator->errors()
            ], 422);
        }

        $quantite = $request->input('quantite', 1);

        // Récupérer l'item
        $item = ListeSouhaitItem::findOrFail($id);

        // Vérifier que l'item appartient bien à l'utilisateur
        if ($item->listeSouhait->client_id != $userId) {
            return response()->json([
                'status' => 'error',
                'message' => 'Vous n\'êtes pas autorisé à déplacer cet item'
            ], 403);
        }

        // Récupérer ou créer le panier de l'utilisateur
        $panier = Panier::where('client_id', $userId)->first();

        if (!$panier) {
            $panier = Panier::create([
                'client_id' => $userId,
                'guest_id' => null
            ]);
        }

        // Vérifier la disponibilité du stock
        $produit = $item->produit;

        if ($produit->quantite_produit < $quantite) {
            return response()->json([
                'status' => 'error',
                'message' => 'Stock insuffisant. Quantité disponible : ' . $produit->quantite_produit,
                'data' => [
                    'stock_disponible' => $produit->quantite_produit
                ]
            ], 422);
        }

        // Récupérer la liste de souhaits pour la retourner après
        $listeSouhait = $item->listeSouhait;

        // Ajouter au panier
        $panier->addItem($item->produit_id, $quantite, $item->variante_id);

        // Supprimer de la liste de souhaits
        $item->delete();

        // Récupérer le panier mis à jour
        $panierData = $this->getPanierData($panier);

        return response()->json([
            'status' => 'success',
            'message' => 'Produit déplacé vers votre panier',
            'data' => [
                'liste_souhait' => $this->getListeSouhaitData($listeSouhait),
                'panier' => $panierData
            ]
        ]);
    }

    /**
     * Formater les données de la liste de souhaits pour la réponse
     *
     * @param ListeSouhait $listeSouhait
     * @return array
     */
    private function getListeSouhaitData(ListeSouhait $listeSouhait)
    {
        // Recharger la liste avec les items
        $listeSouhait->load('items.produit', 'items.variante');

        $items = $listeSouhait->items->map(function ($item) {
            $produit = $item->produit;
            $variante = $item->variante;

            return [
                'id' => $item->id,
                'produit' => [
                    'id' => $produit->id,
                    'nom' => $produit->nom_produit,
                    'description' => $produit->description_produit,
                    'image' => $produit->image_produit ?? $produit->primary_image_url,
                    'prix' => $produit->prix_produit,
                    'reference' => $produit->reference,
                    'en_stock' => $produit->quantite_produit > 0
                ],
                'variante' => $variante ? [
                    'id' => $variante->id,
                    'nom' => $variante->nom,
                    'prix_supplement' => $variante->prix_supplement
                ] : null,
                'note' => $item->note,
                'prix_reference' => $item->prix_reference,
                'prix_actuel' => $item->getPrixActuel(),
                'prix_a_change' => $item->prixAChange(),
                'difference_prix' => $item->getDifferencePrix(),
                'date_ajout' => $item->created_at
            ];
        });

        return [
            'liste_souhait' => [
                'id' => $listeSouhait->id,
                'nom' => $listeSouhait->nom,
                'description' => $listeSouhait->description,
                'nombre_items' => $items->count()
            ],
            'items' => $items
        ];
    }

    /**
     * Formater les données du panier pour la réponse
     *
     * @param Panier $panier
     * @return array
     */
    private function getPanierData(Panier $panier)
    {
        // Recharger le panier avec les items
        $panier->load('items.produit', 'items.variante');

        $items = $panier->items->map(function ($item) {
            $produit = $item->produit;
            $variante = $item->variante;

            return [
                'id' => $item->id,
                'produit' => [
                    'id' => $produit->id,
                    'nom' => $produit->nom_produit,
                    'description' => $produit->description_produit,
                    'image' => $produit->image_produit ?? $produit->primary_image_url,
                    'prix' => $produit->prix_produit,
                    'reference' => $produit->reference,
                    'en_stock' => $produit->quantite_produit > 0
                ],
                'variante' => $variante ? [
                    'id' => $variante->id,
                    'nom' => $variante->nom,
                    'prix_supplement' => $variante->prix_supplement
                ] : null,
                'quantite' => $item->quantite,
                'prix_unitaire' => $item->prix_unitaire,
                'prix_total' => $item->prix_total
            ];
        });

        return [
            'id' => $panier->id,
            'nombre_items' => $items->sum('quantite'),
            'sous_total' => $panier->sous_total,
            'total' => $panier->total,
            'items' => $items
        ];
    }
}
