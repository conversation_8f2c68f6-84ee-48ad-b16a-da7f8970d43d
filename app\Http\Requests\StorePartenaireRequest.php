<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StorePartenaireRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'user_id' => 'required|exists:users,id',
            'remise' => 'required|numeric|min:0|max:100',
            'description' => 'nullable|string',
            'statut' => ['nullable', Rule::in(['actif', 'inactif'])],
        ];
    }

    public function messages()
    {
        return [
            'user_id.required' => "L'identifiant de l'utilisateur est obligatoire.",
            'user_id.exists' => "L'utilisateur sélectionné est invalide.",
            'remise.required' => 'La remise est obligatoire.',
            'remise.numeric' => 'La remise doit être un nombre.',
            'remise.min' => 'La remise doit être au moins 0.',
            'remise.max' => 'La remise ne peut pas dépasser 100.',
            'statut.in' => 'Le statut doit être "actif" ou "inactif".',
        ];
    }
} 