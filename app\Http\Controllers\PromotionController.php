<?php

namespace App\Http\Controllers;

use App\Models\Promotion;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Http\Requests\StorePromotionRequest;
use App\Http\Requests\UpdatePromotionRequest;

class PromotionController extends Controller
{
    /**
     * Afficher la liste des promotions
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = Promotion::query();

        // Filtrage par statut
        if ($request->has('statut')) {
            $query->where('statut', $request->input('statut'));
        }

        // Filtrage par type
        if ($request->has('type')) {
            $query->where('type', $request->input('type'));
        }

        // Filtrage par événement
        if ($request->has('event_id')) {
            $query->where('event_id', $request->input('event_id'));
        }

        // Filtrage par catégorie
        if ($request->has('category_id')) {
            $categoryId = $request->input('category_id');
            $query->whereHas('produits.sousSousCategorie.sousCategorie', function ($q) use ($categoryId) {
                $q->where('categorie_id', $categoryId);
            });
        }

        // Filtrage par marque
        if ($request->has('brand_id')) {
            $brandId = $request->input('brand_id');
            $query->whereHas('produits', function ($q) use ($brandId) {
                $q->where('marque_id', $brandId);
            });
        }

        // Filtrage par featured
        if ($request->has('featured') && $request->boolean('featured') !== null) {
            $query->where('featured', $request->boolean('featured'));
        }

        // Filtrage par dates
        if ($request->has('actives_seulement') && $request->boolean('actives_seulement')) {
            $now = now();
            $query->where('statut', 'active')
                ->where(function ($q) use ($now) {
                    $q->whereNull('date_debut')
                        ->orWhere('date_debut', '<=', $now);
                })
                ->where(function ($q) use ($now) {
                    $q->whereNull('date_fin')
                        ->orWhere('date_fin', '>=', $now);
                });
        }

        // Recherche par nom, code ou description
        if ($request->has('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('nom', 'like', "%{$search}%")
                    ->orWhere('code', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Tri
        $sortField = $request->input('sort', 'priorité');
        $sortDirection = $request->input('direction', 'desc');

        $allowedSortFields = ['id', 'nom', 'valeur', 'date_debut', 'date_fin', 'priorité', 'created_at'];

        if (in_array($sortField, $allowedSortFields)) {
            $query->orderBy($sortField, $sortDirection === 'desc' ? 'desc' : 'asc');
        } else {
            $query->orderBy('priorité', 'desc');
        }

        // Chargement des relations
        if ($request->has('with')) {
            $relations = explode(',', $request->input('with'));
            $allowedRelations = ['produits', 'collections', 'profilsRemise', 'event'];
            $validRelations = array_intersect($relations, $allowedRelations);

            if (!empty($validRelations)) {
                $query->with($validRelations);
            }
        }

        // Pagination
        $perPage = (int) $request->input('per_page', 15);
        $perPage = min(max(5, $perPage), 100); // Limiter entre 5 et 100

        $promotions = $query->paginate($perPage);

        return response()->json([
            'status' => 'success',
            'data' => $promotions
        ]);
    }

    /**
     * Créer une nouvelle promotion
     *
     * @param StorePromotionRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(StorePromotionRequest $request)
    {
        $promotion = Promotion::create($request->all());

        // Associer les produits si fournis
        if ($request->has('produits') && is_array($request->input('produits'))) {
            $promotion->produits()->attach($request->input('produits'));
        }

        // Associer les collections si fournies
        if ($request->has('collections') && is_array($request->input('collections'))) {
            $promotion->collections()->attach($request->input('collections'));
        }

        // Associer les profils de remise si fournis
        if ($request->has('profils_remise') && is_array($request->input('profils_remise'))) {
            foreach ($request->input('profils_remise') as $profilRemise) {
                $promotion->profilsRemise()->attach([
                    'profil_remise' => $profilRemise
                ]);
            }
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Promotion créée avec succès',
            'data' => $promotion
        ], 201);
    }

    /**
     * Afficher une promotion spécifique
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id, Request $request)
    {
        $query = Promotion::where('id', $id);

        // Chargement des relations
        $relations = ['produits', 'collections', 'profilsRemise'];

        if ($request->has('with')) {
            $requestedRelations = explode(',', $request->input('with'));
            $allowedRelations = ['produits', 'collections', 'profilsRemise', 'event'];
            $validRelations = array_intersect($requestedRelations, $allowedRelations);

            if (!empty($validRelations)) {
                $relations = array_unique(array_merge($relations, $validRelations));
            }
        }

        $promotion = $query->with($relations)->firstOrFail();

        return response()->json([
            'status' => 'success',
            'data' => $promotion
        ]);
    }

    /**
     * Récupérer les produits associés à une promotion
     *
     * @param int $id
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProducts($id, Request $request)
    {
        $promotion = Promotion::findOrFail($id);

        $query = $promotion->produits();

        // Filtrage par catégorie
        if ($request->has('category_id')) {
            $categoryId = $request->input('category_id');
            $query->whereHas('sousSousCategorie.sousCategorie', function ($q) use ($categoryId) {
                $q->where('categorie_id', $categoryId);
            });
        }

        // Filtrage par sous-catégorie
        if ($request->has('sous_categorie_id')) {
            $sousCategorieId = $request->input('sous_categorie_id');
            $query->whereHas('sousSousCategorie', function ($q) use ($sousCategorieId) {
                $q->where('sous_categorie_id', $sousCategorieId);
            });
        }

        // Filtrage par sous-sous-catégorie
        if ($request->has('sous_sous_categorie_id')) {
            $query->where('sous_sous_categorie_id', $request->input('sous_sous_categorie_id'));
        }

        // Filtrage par marque
        if ($request->has('marque_id')) {
            $query->where('marque_id', $request->input('marque_id'));
        }

        // Recherche par nom
        if ($request->has('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('nom_produit', 'like', "%{$search}%")
                    ->orWhere('description_produit', 'like', "%{$search}%")
                    ->orWhere('reference', 'like', "%{$search}%");
            });
        }

        // Tri
        $sortField = $request->input('sort', 'nom_produit');
        $sortDirection = $request->input('direction', 'asc');

        $allowedSortFields = ['id', 'nom_produit', 'prix_produit', 'quantite_produit', 'created_at'];

        if (in_array($sortField, $allowedSortFields)) {
            $query->orderBy($sortField, $sortDirection === 'desc' ? 'desc' : 'asc');
        } else {
            $query->orderBy('nom_produit', 'asc');
        }

        // Pagination
        $perPage = (int) $request->input('per_page', 15);
        $perPage = min(max(5, $perPage), 100); // Limiter entre 5 et 100

        $produits = $query->paginate($perPage);

        return response()->json([
            'status' => 'success',
            'data' => [
                'promotion' => [
                    'id' => $promotion->id,
                    'nom' => $promotion->nom,
                    'code' => $promotion->code,
                    'type' => $promotion->type,
                    'valeur' => $promotion->valeur,
                    'statut' => $promotion->statut
                ],
                'produits' => $produits
            ]
        ]);
    }

    /**
     * Mettre à jour une promotion
     *
     * @param UpdatePromotionRequest $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(UpdatePromotionRequest $request, $id)
    {
        $promotion = Promotion::findOrFail($id);
        $promotion->update($request->all());

        return response()->json([
            'status' => 'success',
            'message' => 'Promotion mise à jour avec succès',
            'data' => $promotion
        ]);
    }

    /**
     * Supprimer une promotion
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $promotion = Promotion::findOrFail($id);
        $promotion->delete();

        return response()->json([
            'status' => 'success',
            'message' => 'Promotion supprimée avec succès'
        ]);
    }

    /**
     * Récupérer les promotions mises en avant
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFeatured(Request $request)
    {
        $query = Promotion::where('featured', true)
            ->where('statut', 'active');

        // Filtrer les promotions actives par date
        $now = now();
        $query->where(function ($q) use ($now) {
            $q->whereNull('date_debut')
                ->orWhere('date_debut', '<=', $now);
        })
            ->where(function ($q) use ($now) {
                $q->whereNull('date_fin')
                    ->orWhere('date_fin', '>=', $now);
            });

        // Chargement des relations
        if ($request->has('with')) {
            $relations = explode(',', $request->input('with'));
            $allowedRelations = ['produits', 'collections', 'profilsRemise', 'event'];
            $validRelations = array_intersect($relations, $allowedRelations);

            if (!empty($validRelations)) {
                $query->with($validRelations);
            }
        }

        // Limiter le nombre de résultats
        $limit = (int) $request->input('limit', 5);
        $limit = min(max(1, $limit), 20); // Limiter entre 1 et 20

        $promotions = $query->orderBy('priorité', 'desc')
            ->limit($limit)
            ->get();

        return response()->json([
            'status' => 'success',
            'data' => $promotions
        ]);
    }

    /**
     * Récupérer les promotions similaires à une promotion donnée
     *
     * @param int $id
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getRelated($id, Request $request)
    {
        $promotion = Promotion::findOrFail($id);

        $query = Promotion::where('id', '!=', $id)
            ->where('statut', 'active');

        // Filtrer par même type
        if ($promotion->type) {
            $query->where('type', $promotion->type);
        }

        // Filtrer par même événement si disponible
        if ($promotion->event_id) {
            $query->where('event_id', $promotion->event_id);
        }

        // Filtrer les promotions actives par date
        $now = now();
        $query->where(function ($q) use ($now) {
            $q->whereNull('date_debut')
                ->orWhere('date_debut', '<=', $now);
        })
            ->where(function ($q) use ($now) {
                $q->whereNull('date_fin')
                    ->orWhere('date_fin', '>=', $now);
            });

        // Limiter le nombre de résultats
        $limit = (int) $request->input('limit', 4);
        $limit = min(max(1, $limit), 10); // Limiter entre 1 et 10

        $promotions = $query->orderBy('priorité', 'desc')
            ->limit($limit)
            ->get();

        return response()->json([
            'status' => 'success',
            'data' => $promotions
        ]);
    }
}
