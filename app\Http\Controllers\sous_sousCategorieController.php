<?php

namespace App\Http\Controllers;

use App\Models\sous_sousCategorie;
use Illuminate\Http\Request;

class sous_sousCategorieController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = sous_sousCategorie::query();
        if ($request->has('sous_categorie_id')) {
            $query->where('sous_categorie_id', $request->input('sous_categorie_id'));
        }
        $sous_sousCategories = $query->get();
        return response()->json($sous_sousCategories);
    }

    /**
     * Show the form for creating a new resource.
     */


    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $sous_souscategorie = new sous_sousCategorie([
                "nom_sous_sous_categorie" => $request->input("nom_sous_sous_categorie"),
                "description_sous_sous_categorie" => $request->input("description_sous_sous_categorie"),
                "sous_categorie_id" => $request->input("sous_categorie_id")


            ]);
            $sous_souscategorie->save();


            return response()->json($sous_souscategorie, 201);

        } catch (\Exception $e) {
            return response()->json(["error" => "probleme d'insertion {$e->getMessage()}"]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
            $sous_sousCategorie = sous_sousCategorie::findOrFail($id);
            return response()->json($sous_sousCategorie);
        } catch (\Exception $e) {
            return response()->json(["error" => "probleme de récupération des données {$e->getMessage()}"]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */


    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $sous_souscategorie = sous_sousCategorie::findorFail($id);
            $sous_souscategorie->update($request->all());
            return response()->json($sous_souscategorie);
        } catch (\Exception $e) {
            return response()->json(["error" => "probleme de modification {$e->getMessage()}"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $sous_souscategorie = sous_sousCategorie::findOrFail($id);
            $sous_souscategorie->delete();
            return response()->json(["message" => "sous_souscategorie supprimée avec succès"], 200);
        } catch (\Exception $e) {
            return response()->json(["error" => "probleme de suppression de sous_souscategorie {$e->getMessage()}"]);
        }
    }

    /**
     * Récupère les attributs associés à la sous-catégorie parente d'une sous-sous-catégorie
     *
     * @param string $id ID de la sous-sous-catégorie
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAttributs(string $id)
    {
        try {
            // Récupérer la sous-sous-catégorie avec sa sous-catégorie parente
            $sousSousCategorie = sous_sousCategorie::with('sousCategorie')->findOrFail($id);

            // Vérifier si la sous-catégorie parente existe
            if (!$sousSousCategorie->sousCategorie) {
                return response()->json([
                    "error" => "La sous-catégorie parente n'existe pas"
                ], 404);
            }

            // Récupérer les attributs de la sous-catégorie parente
            $attributs = $sousSousCategorie->sousCategorie->attributs()
                ->with('groupe') // Inclure le groupe d'attributs
                ->get()
                ->map(function ($attribut) {
                    return [
                        'id' => $attribut->id,
                        'nom' => $attribut->nom,
                        'description' => $attribut->description,
                        'type_valeur' => $attribut->type_valeur,
                        'groupe' => $attribut->groupe ? [
                            'id' => $attribut->groupe->id,
                            'nom' => $attribut->groupe->nom,
                        ] : null,
                        'obligatoire' => $attribut->pivot->obligatoire,
                        'filtrable' => $attribut->filtrable,
                        'comparable' => $attribut->comparable,
                        'affichable' => $attribut->affichable,
                        'ordre' => $attribut->ordre,
                    ];
                });

            return response()->json($attributs);
        } catch (\Exception $e) {
            return response()->json([
                "error" => "Problème de récupération des attributs",
                "message" => $e->getMessage()
            ], 500);
        }
    }
}
