<?php

namespace App\Observers;

use App\Models\Categorie;
use App\Services\CacheService;

class CategorieObserver
{
    protected $cacheService;

    public function __construct(CacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    /**
     * Handle the Categorie "created" event.
     */
    public function created(Categorie $categorie): void
    {
        $this->cacheService->forgetCategories();
    }

    /**
     * Handle the Categorie "updated" event.
     */
    public function updated(Categorie $categorie): void
    {
        $this->cacheService->forgetCategories();
    }

    /**
     * Handle the Categorie "deleted" event.
     */
    public function deleted(Categorie $categorie): void
    {
        $this->cacheService->forgetCategories();
    }

    /**
     * Handle the Categorie "restored" event.
     */
    public function restored(Categorie $categorie): void
    {
        $this->cacheService->forgetCategories();
    }

    /**
     * Handle the Categorie "force deleted" event.
     */
    public function forceDeleted(Categorie $categorie): void
    {
        $this->cacheService->forgetCategories();
    }
}
