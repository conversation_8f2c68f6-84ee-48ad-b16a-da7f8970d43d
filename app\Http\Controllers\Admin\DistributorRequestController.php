<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\DistributorRequest;
use App\Models\PointDeVente;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class DistributorRequestController extends Controller
{
    /**
     * Display a listing of distributor requests
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        if (!$user || !$user->hasAnyRole(['admin', 'staff'])) {
            return response()->json([
                'status' => 'error',
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $status = $request->query('status', 'pending');
        $perPage = $request->query('per_page', 15);

        $query = DistributorRequest::with('user');

        if ($status !== 'all') {
            $query->where('status', $status);
        }

        $requests = $query->latest()->paginate($perPage);

        return response()->json([
            'status' => 'success',
            'data' => $requests
        ]);
    }

    /**
     * Display the specified distributor request
     */
    public function show(string $id)
    {
        $user = Auth::user();

        if (!$user || !$user->hasAnyRole(['admin', 'staff'])) {
            return response()->json([
                'status' => 'error',
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $distributorRequest = DistributorRequest::with('user')->find($id);

        if (!$distributorRequest) {
            return response()->json([
                'status' => 'error',
                'message' => 'Demande de distributeur non trouvée'
            ], 404);
        }

        return response()->json([
            'status' => 'success',
            'data' => $distributorRequest
        ]);
    }

    /**
     * Approve a distributor request
     */
    public function approve(Request $request, string $id)
    {
        $user = Auth::user();

        if (!$user || !$user->hasAnyRole(['admin'])) {
            return response()->json([
                'status' => 'error',
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'remise' => 'required|numeric|min:0|max:100',
            'admin_notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $distributorRequest = DistributorRequest::with('user')->find($id);

        if (!$distributorRequest) {
            return response()->json([
                'status' => 'error',
                'message' => 'Demande de distributeur non trouvée'
            ], 404);
        }

        if ($distributorRequest->status !== 'pending') {
            return response()->json([
                'status' => 'error',
                'message' => 'Cette demande a déjà été traitée'
            ], 400);
        }

        DB::beginTransaction();

        try {
            // Update the request status
            $distributorRequest->status = 'approved';
            $distributorRequest->admin_notes = $request->admin_notes;
            $distributorRequest->processed_at = now();
            $distributorRequest->processed_by = $user->id;
            $distributorRequest->save();

            // Create point de vente record
            $pointDeVente = new PointDeVente();
            $pointDeVente->nom = $distributorRequest->company_name;
            $pointDeVente->adresse = $distributorRequest->address . ', ' . $distributorRequest->city . ', ' . $distributorRequest->country;
            $pointDeVente->telephone = $distributorRequest->phone;
            $pointDeVente->remise = $request->remise;
            $pointDeVente->description = $distributorRequest->motivation;
            $pointDeVente->statut = 'actif';

            // Check if the request is associated with a user
            if ($distributorRequest->user_id) {
                // Use the existing user
                $requestUser = $distributorRequest->user;
                $pointDeVente->email = $requestUser->email;
                $pointDeVente->save();

                // Update user roles and point_de_vente_id
                $roles = $requestUser->roles ?? [];

                if (!in_array('point_de_vente', $roles)) {
                    $roles[] = 'point_de_vente';
                    $requestUser->roles = $roles;
                }

                $requestUser->point_de_vente_id = $pointDeVente->id;
                $requestUser->save();

                // Ensure role consistency
                User::ensureRoleTypeConsistency($requestUser);
            } else {
                // For requests without a user, we need to create a user first
                // Check if a user with this email already exists
                $existingUser = User::where('email', $distributorRequest->email)->first();

                if ($existingUser) {
                    // Associate the request with the existing user
                    $distributorRequest->user_id = $existingUser->id;
                    $distributorRequest->save();

                    // Set email for point de vente
                    $pointDeVente->email = $existingUser->email;
                    $pointDeVente->save();

                    // Update user roles and point_de_vente_id
                    $roles = $existingUser->roles ?? [];

                    if (!in_array('point_de_vente', $roles)) {
                        $roles[] = 'point_de_vente';
                        $existingUser->roles = $roles;
                    }

                    $existingUser->point_de_vente_id = $pointDeVente->id;
                    $existingUser->save();

                    // Ensure role consistency
                    User::ensureRoleTypeConsistency($existingUser);
                } else {
                    // Create a new user
                    $newUser = new User();
                    $newUser->name = $distributorRequest->name;
                    $newUser->email = $distributorRequest->email;
                    $newUser->roles = ['point_de_vente'];
                    $newUser->save();

                    // Set email for point de vente
                    $pointDeVente->email = $newUser->email;
                    $pointDeVente->save();

                    // Associate the request with the new user
                    $distributorRequest->user_id = $newUser->id;
                    $distributorRequest->save();

                    // Update user with point_de_vente_id
                    $newUser->point_de_vente_id = $pointDeVente->id;
                    $newUser->save();
                }
            }

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Demande de distributeur approuvée avec succès',
                'data' => $distributorRequest
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'status' => 'error',
                'message' => 'Une erreur est survenue lors de l\'approbation de la demande',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reject a distributor request
     */
    public function reject(Request $request, string $id)
    {
        $user = Auth::user();

        if (!$user || !$user->hasAnyRole(['admin', 'staff'])) {
            return response()->json([
                'status' => 'error',
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'admin_notes' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $distributorRequest = DistributorRequest::find($id);

        if (!$distributorRequest) {
            return response()->json([
                'status' => 'error',
                'message' => 'Demande de distributeur non trouvée'
            ], 404);
        }

        if ($distributorRequest->status !== 'pending') {
            return response()->json([
                'status' => 'error',
                'message' => 'Cette demande a déjà été traitée'
            ], 400);
        }

        // Update the request status
        $distributorRequest->status = 'rejected';
        $distributorRequest->admin_notes = $request->admin_notes;
        $distributorRequest->processed_at = now();
        $distributorRequest->processed_by = $user->id;
        $distributorRequest->save();

        return response()->json([
            'status' => 'success',
            'message' => 'Demande de distributeur rejetée',
            'data' => $distributorRequest
        ]);
    }
}
