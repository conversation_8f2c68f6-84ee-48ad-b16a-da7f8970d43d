<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateCarouselRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'nom' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'actif' => 'nullable|boolean',
            'ordre' => 'nullable|integer|min:0',
        ];
    }

    public function messages(): array
    {
        return [
            'nom.string' => 'Le nom doit être une chaîne de caractères.',
            'nom.max' => 'Le nom ne doit pas dépasser 255 caractères.',
            'description.string' => 'La description doit être une chaîne de caractères.',
            'actif.boolean' => 'Le champ actif doit être vrai ou faux.',
            'ordre.integer' => 'L\'ordre doit être un entier.',
            'ordre.min' => 'L\'ordre doit être au moins 0.',
        ];
    }
}
