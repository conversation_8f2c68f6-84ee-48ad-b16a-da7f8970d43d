<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Paiement;
use App\Models\Commande;
use App\Services\CurrencyConversionService;
use Stripe\Stripe;
use Stripe\PaymentIntent;
use Stripe\Exception\ApiErrorException;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class PaiementController extends Controller
{
    protected $currencyConversionService;

    public function __construct(CurrencyConversionService $currencyConversionService)
    {
        // Initialize Stripe with secret key
        Stripe::setApiKey(config('services.stripe.secret'));
        $this->currencyConversionService = $currencyConversionService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $paimenent = Paiement::all();
        return response()->json($paimenent);
    }

    /**
     * Show the form for creating a new resource.
     */


    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $paimenent = new Paiement([
                'montant' => $request->input('montant'),
                'date_paiement' => $request->input('date_paiement'),
                'methode' => $request->input('methode'),
                'statut' => $request->input('statut'),
                'commande_id' => $request->input('commande_id')
            ]);
            $paimenent->save();
            return response()->json($paimenent, 201);
        } catch (\Exception $e) {
            return response()->json(["error" => "probleme d'insertion {$e->getMessage()}"]);

        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
            $paimenent = Paiement::find($id);
            return response()->json($paimenent);
        } catch (\Exception $e) {
            return response()->json(["error" => "probleme de récupération des données {$e->getMessage()}"]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */


    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $paimenent = Paiement::findOrFail($id);
            $paimenent->update($request->all());
            return response()->json($paimenent, 200);
        } catch (\Exception $e) {
            return response()->json(["error" => "modification impossible {$e->getMessage()}"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $paimenent = Paiement::findOrFail($id);
            $paimenent->delete();
            return response()->json(["message" => "paiement supprimée avec succès"], 200);
        } catch (\Exception $e) {
            return response()->json(["error" => "suppression impossible {$e->getMessage()}"]);
        }
    }

    /**
     * Create a new Stripe Payment Intent with currency conversion
     */
    public function createPaymentIntent(Request $request)
    {
        try {
            // Validate request
            $validator = Validator::make($request->all(), [
                'amount' => 'required|numeric|min:1',
                'currency' => 'string|max:3',
                'commande_id' => 'required|exists:commandes,id',
                'customer_email' => 'email',
                'metadata' => 'array'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'error' => 'Validation failed',
                    'details' => $validator->errors()
                ], 400);
            }

            // Get the order
            $commande = Commande::findOrFail($request->commande_id);

            // Handle currency conversion
            // Check if frontend sent original_currency and original_amount (new format)
            $originalCurrency = strtoupper($request->input('original_currency', $request->input('currency', 'TND')));
            $originalAmount = $request->input('original_amount', $request->input('amount'));
            $targetCurrency = strtolower($request->input('currency', 'eur'));
            $frontendAmount = $request->input('amount'); // This might be in cents already

            $conversionData = null;

            Log::info('Payment Intent Request Debug', [
                'original_currency' => $originalCurrency,
                'original_amount' => $originalAmount,
                'target_currency' => $targetCurrency,
                'frontend_amount' => $frontendAmount,
                'request_data' => $request->all()
            ]);

            // Check if currency conversion is needed
            if ($originalCurrency === 'TND') {
                // Convert TND to EUR for Stripe processing
                $conversionData = $this->currencyConversionService->convertTndToEur($originalAmount);
                $stripeAmountEur = $conversionData['converted_amount']; // Amount in EUR
                $stripeAmountCents = round($stripeAmountEur * 100); // Convert to cents
                $stripeCurrency = 'eur';

                Log::info('Currency conversion applied', [
                    'original_amount' => $originalAmount,
                    'original_currency' => $originalCurrency,
                    'converted_amount_eur' => $stripeAmountEur,
                    'stripe_amount_cents' => $stripeAmountCents,
                    'exchange_rate' => $conversionData['exchange_rate']
                ]);
            } elseif ($this->currencyConversionService->isSupportedByStripe($originalCurrency)) {
                // Currency is supported by Stripe, use it directly
                // Check if frontend already sent amount in cents
                if ($request->has('original_amount') && $frontendAmount > $originalAmount * 50) {
                    // Frontend likely sent amount in cents already
                    $stripeAmountCents = $frontendAmount;
                } else {
                    // Convert to cents
                    $stripeAmountCents = round($originalAmount * 100);
                }
                $stripeCurrency = strtolower($originalCurrency);

                Log::info('Direct currency processing', [
                    'original_amount' => $originalAmount,
                    'original_currency' => $originalCurrency,
                    'stripe_amount_cents' => $stripeAmountCents,
                    'frontend_sent_cents' => $request->has('original_amount')
                ]);
            } else {
                return response()->json([
                    'error' => 'Unsupported currency',
                    'message' => "Currency {$originalCurrency} is not supported. Please use TND (will be converted to EUR) or another Stripe-supported currency.",
                    'supported_currencies' => ['TND (converted to EUR)', 'EUR', 'USD', 'GBP', 'CAD', 'AUD']
                ], 400);
            }

            // Prepare payment intent data
            $amount = $stripeAmountCents; // Already in cents, don't multiply again!

            $paymentIntentData = [
                'amount' => $amount,
                'currency' => $stripeCurrency,
                'automatic_payment_methods' => [
                    'enabled' => true,
                ],
                'metadata' => [
                    'commande_id' => $commande->id,
                    'original_amount' => $originalAmount,
                    'original_currency' => $originalCurrency,
                    'customer_email' => $request->input('customer_email', $commande->email ?? ''),
                    ...$request->input('metadata', [])
                ]
            ];

            // Add conversion data to metadata if conversion was performed
            if ($conversionData) {
                $paymentIntentData['metadata']['conversion_applied'] = 'true';
                $paymentIntentData['metadata']['exchange_rate'] = $conversionData['exchange_rate'];
                $paymentIntentData['metadata']['conversion_timestamp'] = $conversionData['conversion_timestamp'];
            }

            // Add customer email if provided
            if ($request->has('customer_email')) {
                $paymentIntentData['receipt_email'] = $request->customer_email;
            }

            // Create Payment Intent
            $paymentIntent = PaymentIntent::create($paymentIntentData);

            // Store payment record in database
            $paiementData = [
                'commande_id' => $commande->id,
                'montant' => $originalAmount, // Store original amount
                'original_currency' => $originalCurrency,
                'methode_paiement' => 'stripe',
                'status' => 'pending',
                'transaction_id' => $paymentIntent->id,
                'gateway_response' => array_merge($paymentIntent->toArray(), [
                    'currency_conversion' => $conversionData
                ])
            ];

            // Add conversion data if currency conversion was performed
            if ($conversionData) {
                $paiementData['converted_amount'] = $conversionData['converted_amount'];
                $paiementData['stripe_currency'] = 'EUR';
                $paiementData['exchange_rate'] = $conversionData['exchange_rate'];
                $paiementData['conversion_timestamp'] = now();
            } else {
                $paiementData['stripe_currency'] = strtoupper($stripeCurrency);
            }

            $paiement = new Paiement($paiementData);
            $paiement->save();

            return response()->json([
                'success' => true,
                'client_secret' => $paymentIntent->client_secret,
                'payment_intent_id' => $paymentIntent->id,
                'amount' => $originalAmount,
                'currency' => $originalCurrency,
                'stripe_amount_cents' => $amount,
                'stripe_amount_eur' => $amount / 100,
                'stripe_currency' => $stripeCurrency,
                'conversion_data' => $conversionData,
                'paiement_id' => $paiement->id,
                'debug_info' => [
                    'frontend_sent_amount' => $frontendAmount,
                    'original_amount' => $originalAmount,
                    'original_currency' => $originalCurrency,
                    'target_currency' => $targetCurrency,
                    'final_stripe_cents' => $amount,
                    'conversion_applied' => $conversionData !== null
                ]
            ], 201);

        } catch (ApiErrorException $e) {
            Log::error('Stripe API Error: ' . $e->getMessage());
            return response()->json([
                'error' => 'Payment processing error',
                'message' => $e->getMessage()
            ], 400);
        } catch (\Exception $e) {
            Log::error('Payment Intent Creation Error: ' . $e->getMessage());
            return response()->json([
                'error' => 'Internal server error',
                'message' => 'Unable to create payment intent'
            ], 500);
        }
    }

    /**
     * Confirm a Stripe Payment Intent
     */
    public function confirmPayment(Request $request)
    {
        try {
            // Validate request
            $validator = Validator::make($request->all(), [
                'payment_intent_id' => 'required|string',
                'payment_method_id' => 'string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'error' => 'Validation failed',
                    'details' => $validator->errors()
                ], 400);
            }

            $paymentIntentId = $request->payment_intent_id;

            // Retrieve the payment intent from Stripe
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);

            // If payment method is provided, confirm with it
            if ($request->has('payment_method_id')) {
                $paymentIntent = $paymentIntent->confirm([
                    'payment_method' => $request->payment_method_id
                ]);
            }

            // Update local payment record
            $paiement = Paiement::where('transaction_id', $paymentIntentId)->first();

            if ($paiement) {
                $status = match ($paymentIntent->status) {
                    'succeeded' => 'completed',
                    'requires_payment_method', 'requires_confirmation' => 'pending',
                    'canceled' => 'failed',
                    default => 'pending'
                };

                $paiement->update([
                    'status' => $status,
                    'gateway_response' => $paymentIntent->toArray(),
                    'processed_at' => $paymentIntent->status === 'succeeded' ? now() : null
                ]);

                // Update order status if payment succeeded
                if ($paymentIntent->status === 'succeeded') {
                    $paiement->commande->update(['statut' => 'paid']);
                }
            }

            return response()->json([
                'success' => true,
                'status' => $paymentIntent->status,
                'payment_intent' => [
                    'id' => $paymentIntent->id,
                    'status' => $paymentIntent->status,
                    'amount' => $paymentIntent->amount / 100,
                    'currency' => $paymentIntent->currency
                ],
                'requires_action' => $paymentIntent->status === 'requires_action',
                'client_secret' => $paymentIntent->client_secret
            ]);

        } catch (ApiErrorException $e) {
            Log::error('Stripe Confirm Payment Error: ' . $e->getMessage());
            return response()->json([
                'error' => 'Payment confirmation error',
                'message' => $e->getMessage()
            ], 400);
        } catch (\Exception $e) {
            Log::error('Payment Confirmation Error: ' . $e->getMessage());
            return response()->json([
                'error' => 'Internal server error',
                'message' => 'Unable to confirm payment'
            ], 500);
        }
    }

    /**
     * Get Payment Intent status
     */
    public function getPaymentIntent(string $paymentIntentId)
    {
        try {
            // Retrieve the payment intent from Stripe
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);

            // Get local payment record
            $paiement = Paiement::where('transaction_id', $paymentIntentId)
                ->with('commande')
                ->first();

            return response()->json([
                'success' => true,
                'payment_intent' => [
                    'id' => $paymentIntent->id,
                    'status' => $paymentIntent->status,
                    'amount' => $paymentIntent->amount / 100,
                    'currency' => $paymentIntent->currency,
                    'created' => $paymentIntent->created,
                    'metadata' => $paymentIntent->metadata->toArray()
                ],
                'local_payment' => $paiement ? [
                    'id' => $paiement->id,
                    'status' => $paiement->status,
                    'processed_at' => $paiement->processed_at,
                    'commande_id' => $paiement->commande_id,
                    'commande_status' => $paiement->commande->statut ?? null
                ] : null
            ]);

        } catch (ApiErrorException $e) {
            Log::error('Stripe Get Payment Intent Error: ' . $e->getMessage());
            return response()->json([
                'error' => 'Payment retrieval error',
                'message' => $e->getMessage()
            ], 400);
        } catch (\Exception $e) {
            Log::error('Get Payment Intent Error: ' . $e->getMessage());
            return response()->json([
                'error' => 'Internal server error',
                'message' => 'Unable to retrieve payment intent'
            ], 500);
        }
    }

    /**
     * Test endpoint to check exchange rate
     */
    public function testExchangeRate()
    {
        try {
            $rate = $this->currencyConversionService->getExchangeRate();
            $manualRate = $this->currencyConversionService->getManualExchangeRate();

            return response()->json([
                'success' => true,
                'rate' => $rate,
                'manual_rate' => $manualRate,
                'source' => $manualRate ? 'manual' : 'api',
                'cached' => \Illuminate\Support\Facades\Cache::has('exchange_rate_tnd_eur'),
                'fallback_rate' => 0.305,
                'timestamp' => now()->toISOString()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test endpoint for currency conversion
     */
    public function testCurrencyConversion(Request $request)
    {
        try {
            $amount = $request->input('amount', 250.00);
            $fromCurrency = $request->input('from_currency', 'TND');
            $toCurrency = $request->input('to_currency', 'EUR');

            if ($fromCurrency === 'TND' && $toCurrency === 'EUR') {
                $conversionData = $this->currencyConversionService->convertTndToEur($amount);

                return response()->json([
                    'success' => true,
                    'input' => [
                        'amount' => $amount,
                        'from_currency' => $fromCurrency,
                        'to_currency' => $toCurrency
                    ],
                    'conversion_data' => $conversionData,
                    'stripe_amount_cents' => round($conversionData['converted_amount'] * 100),
                    'debug_info' => [
                        'calculation' => "$amount TND × {$conversionData['exchange_rate']} = {$conversionData['converted_amount']} EUR",
                        'stripe_calculation' => "{$conversionData['converted_amount']} EUR × 100 = " . round($conversionData['converted_amount'] * 100) . " cents"
                    ]
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'error' => 'Only TND to EUR conversion is currently supported'
                ], 400);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }
}

