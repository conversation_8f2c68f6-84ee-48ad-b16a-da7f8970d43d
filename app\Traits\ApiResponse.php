<?php

namespace App\Traits;

use Illuminate\Http\JsonResponse;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\App;
use Illuminate\Validation\Validator;

trait ApiResponse
{
    /**
     * Return a success response with standardized format
     *
     * @param mixed $data
     * @param string|null $message
     * @param int $code
     * @return JsonResponse
     */
    protected function success($data = null, string $message = null, int $code = 200): JsonResponse
    {
        return response()->json([
            'status' => 'success',
            'message' => $message,
            'data' => $data
        ], $code);
    }

    /**
     * Return an error response with standardized format
     *
     * @param string $message
     * @param int $code
     * @param mixed $data
     * @return JsonResponse
     */
    protected function error(string $message, int $code = 400, $data = null): JsonResponse
    {
        return response()->json([
            'status' => 'error',
            'message' => $message,
            'data' => $data
        ], $code);
    }

    /**
     * Return a success response with standardized format
     *
     * @param mixed $data
     * @param string $message
     * @param int $status
     * @return JsonResponse
     */
    protected function successResponse($data, string $message = 'Opération réussie', int $status = 200): JsonResponse
    {
        return response()->json([
            'status' => 'success',
            'message' => $message,
            'data' => $data
        ], $status);
    }

    /**
     * Return an error response with standardized format
     *
     * @param string $message
     * @param int $status
     * @param array $errors
     * @param array $debug
     * @return JsonResponse
     */
    protected function errorResponse(string $message, int $status = 400, array $errors = [], array $debug = []): JsonResponse
    {
        $response = [
            'status' => 'error',
            'message' => $message
        ];

        if (!empty($errors)) {
            $response['errors'] = $errors;
        }

        // Include debug information only in non-production environments
        if (!empty($debug) && !App::environment('production')) {
            $response['debug'] = $debug;
        }

        return response()->json($response, $status);
    }

    /**
     * Return a validation error response with standardized format
     *
     * @param Validator $validator
     * @return JsonResponse
     */
    protected function validationErrorResponse(Validator $validator): JsonResponse
    {
        return $this->errorResponse(
            'Erreur de validation',
            422,
            $validator->errors()->toArray()
        );
    }

    /**
     * Return a paginated response with standardized format
     *
     * @param LengthAwarePaginator $paginator
     * @param string $message
     * @return JsonResponse
     */
    protected function paginationResponse(LengthAwarePaginator $paginator, string $message = 'Données récupérées avec succès'): JsonResponse
    {
        return $this->successResponse($paginator, $message);
    }
}
