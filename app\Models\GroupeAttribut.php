<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class GroupeAttribut extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'groupes_attributs';

    protected $fillable = [
        'nom',
        'description'
    ];

    /**
     * Les attributs appartenant à ce groupe
     */
    public function attributs()
    {
        return $this->hasMany(Attribut::class, 'groupe_id');
    }
}
