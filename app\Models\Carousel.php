<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Carousel extends Model
{
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'nom',
        'description',
        'actif',
        'ordre'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'actif' => 'boolean',
        'ordre' => 'integer',
    ];

    /**
     * Get the slides for the carousel.
     */
    public function slides(): HasMany
    {
        return $this->hasMany(CarouselSlide::class)->orderBy('ordre');
    }

    /**
     * Scope a query to only include active carousels.
     */
    public function scopeActif($query)
    {
        return $query->where('actif', true);
    }
}
