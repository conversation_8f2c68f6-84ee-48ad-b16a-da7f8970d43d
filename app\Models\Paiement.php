<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

enum PaiementStatus: string
{
    case PENDING = 'pending';
    case COMPLETED = 'completed';
    case FAILED = 'failed';
    case REFUNDED = 'refunded';
}

class Paiement extends Model
{
    use HasFactory;

    protected $fillable = [
        'commande_id',
        'montant',
        'original_currency',
        'converted_amount',
        'stripe_currency',
        'exchange_rate',
        'conversion_timestamp',
        'methode_paiement',
        'status',
        'transaction_id',
        'gateway_response',
        'processed_at',
        'refunded_at',
    ];

    protected $casts = [
        'status' => PaiementStatus::class,
        'processed_at' => 'datetime',
        'refunded_at' => 'datetime',
        'conversion_timestamp' => 'datetime',
        'gateway_response' => 'json',
    ];

    public function commande(): BelongsTo
    {
        return $this->belongsTo(Commande::class);
    }
}

