<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add only the most critical indexes that we know exist

        // Products table indexes (we know these columns exist)
        DB::statement('CREATE INDEX IF NOT EXISTS idx_produits_marque_id ON produits(marque_id)');
        DB::statement('CREATE INDEX IF NOT EXISTS idx_produits_stock ON produits(quantite_produit)');
        DB::statement('CREATE INDEX IF NOT EXISTS idx_produits_prix ON produits(prix_produit)');
        DB::statement('CREATE INDEX IF NOT EXISTS idx_produits_nom ON produits(nom_produit)');
        DB::statement('CREATE INDEX IF NOT EXISTS idx_produits_categorie ON produits(sous_sous_categorie_id)');

        // Users table indexes
        DB::statement('CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)');

        // Commandes table indexes (check if columns exist)
        if (Schema::hasTable('commandes') && Schema::hasColumn('commandes', 'client_id')) {
            DB::statement('CREATE INDEX IF NOT EXISTS idx_commandes_client_id ON commandes(client_id)');
        }

        if (Schema::hasTable('commandes') && Schema::hasColumn('commandes', 'status')) {
            DB::statement('CREATE INDEX IF NOT EXISTS idx_commandes_status ON commandes(status)');
        }

        if (Schema::hasTable('commandes')) {
            DB::statement('CREATE INDEX IF NOT EXISTS idx_commandes_date ON commandes(created_at)');
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the indexes
        DB::statement('DROP INDEX IF EXISTS idx_produits_marque_id');
        DB::statement('DROP INDEX IF EXISTS idx_produits_stock');
        DB::statement('DROP INDEX IF EXISTS idx_produits_prix');
        DB::statement('DROP INDEX IF EXISTS idx_produits_nom');
        DB::statement('DROP INDEX IF EXISTS idx_produits_categorie');
        DB::statement('DROP INDEX IF EXISTS idx_users_email');
        DB::statement('DROP INDEX IF EXISTS idx_commandes_client_id');
        DB::statement('DROP INDEX IF EXISTS idx_commandes_status');
        DB::statement('DROP INDEX IF EXISTS idx_commandes_date');
    }
};
