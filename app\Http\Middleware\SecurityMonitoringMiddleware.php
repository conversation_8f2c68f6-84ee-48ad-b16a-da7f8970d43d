<?php

namespace App\Http\Middleware;

use App\Services\ErrorHandlingService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class SecurityMonitoringMiddleware
{
    /**
     * Suspicious patterns to monitor
     */
    private array $suspiciousPatterns = [
        'sql_injection' => [
            '/(\s|^)(union|select|insert|update|delete|drop|create|alter|exec|script)(\s|$)/i',
            '/(\s|^)(or|and)\s+\d+\s*=\s*\d+/i',
            '/\'\s*(or|and|union)/i'
        ],
        'xss_attempts' => [
            '/<script[^>]*>.*?<\/script>/is',
            '/javascript:/i',
            '/on\w+\s*=/i',
            '/<iframe[^>]*>.*?<\/iframe>/is'
        ],
        'path_traversal' => [
            '/\.\.\//',
            '/\.\.\\\\/',
            '/\/etc\/passwd/',
            '/\/proc\//',
            '/\.\.%2f/i',
            '/\.\.%5c/i'
        ],
        'command_injection' => [
            '/[\|;&`$\(\)]/i',
            '/\b(cat|ls|pwd|id|whoami|uname|wget|curl|nc|netcat)\b/i'
        ]
    ];

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        // Monitor suspicious activity before processing
        $this->monitorRequest($request);

        $response = $next($request);

        // Monitor response for data leaks
        $this->monitorResponse($request, $response);

        return $response;
    }

    /**
     * Monitor incoming request for suspicious patterns
     */
    private function monitorRequest(Request $request): void
    {
        $ip = $request->ip();
        $userAgent = $request->userAgent();
        $url = $request->fullUrl();

        // Check for suspicious patterns in request
        $allInput = array_merge(
            $request->all(),
            [$request->getPathInfo(), $request->getQueryString()]
        );

        foreach ($allInput as $key => $value) {
            if (is_string($value)) {
                $this->checkSuspiciousPatterns($value, $key, $request);
            }
        }

        // Monitor unusual request patterns
        $this->monitorRequestPatterns($request);

        // Check for suspicious headers
        $this->monitorHeaders($request);
    }

    /**
     * Check input against suspicious patterns
     */
    private function checkSuspiciousPatterns(string $input, string $field, Request $request): void
    {
        foreach ($this->suspiciousPatterns as $category => $patterns) {
            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $input)) {
                    $this->logSecurityThreat($category, $field, $input, $request);

                    // Rate limit this IP for suspicious activity
                    $this->penalizeIp($request->ip(), $category);
                    break 2; // Exit both loops
                }
            }
        }
    }

    /**
     * Monitor unusual request patterns
     */
    private function monitorRequestPatterns(Request $request): void
    {
        $ip = $request->ip();
        $cacheKey = "security_monitor:{$ip}";

        // Get request history for this IP
        $history = Cache::get($cacheKey, [
            'requests' => 0,
            'endpoints' => [],
            'methods' => [],
            'last_request' => null
        ]);

        $now = now();
        $endpoint = $request->getPathInfo();
        $method = $request->method();

        // Update history
        $history['requests']++;
        $history['endpoints'][] = $endpoint;
        $history['methods'][] = $method;
        $history['last_request'] = $now->timestamp;

        // Keep only last 100 requests
        $history['endpoints'] = array_slice($history['endpoints'], -100);
        $history['methods'] = array_slice($history['methods'], -100);

        // Detect suspicious patterns
        $this->detectSuspiciousPatterns($history, $request);

        // Cache for 1 hour
        Cache::put($cacheKey, $history, 3600);
    }

    /**
     * Detect suspicious patterns in request history
     */
    private function detectSuspiciousPatterns(array $history, Request $request): void
    {
        $ip = $request->ip();

        // Check for rapid-fire requests (potential DoS)
        if ($history['requests'] > 100) { // More than 100 requests in history
            $recentRequests = array_slice($history['endpoints'], -50);
            $uniqueEndpoints = count(array_unique($recentRequests));

            // Many requests to few endpoints = potential attack
            if ($uniqueEndpoints < 5) {
                ErrorHandlingService::logSecurityEvent('potential_dos_attack', [
                    'ip' => $ip,
                    'total_requests' => $history['requests'],
                    'unique_endpoints' => $uniqueEndpoints,
                    'recent_endpoints' => array_unique($recentRequests)
                ]);
            }
        }

        // Check for endpoint enumeration
        $recentEndpoints = array_slice($history['endpoints'], -20);
        $uniqueRecent = array_unique($recentEndpoints);

        if (count($uniqueRecent) > 15) { // Accessing many different endpoints quickly
            ErrorHandlingService::logSecurityEvent('endpoint_enumeration', [
                'ip' => $ip,
                'endpoints_accessed' => $uniqueRecent
            ]);
        }

        // Check for method abuse
        $recentMethods = array_slice($history['methods'], -20);
        $methodCounts = array_count_values($recentMethods);

        if (($methodCounts['DELETE'] ?? 0) > 5 || ($methodCounts['PUT'] ?? 0) > 10) {
            ErrorHandlingService::logSecurityEvent('method_abuse', [
                'ip' => $ip,
                'method_counts' => $methodCounts
            ]);
        }
    }

    /**
     * Monitor suspicious headers
     */
    private function monitorHeaders(Request $request): void
    {
        $suspiciousHeaders = [
            'x-forwarded-for' => '/,.*,/', // Multiple proxies
            'user-agent' => '/(bot|crawler|scanner|curl|wget|python|perl)/i',
            'accept' => '/text\/html.*application\/xml/', // Unusual accept header
        ];

        foreach ($suspiciousHeaders as $header => $pattern) {
            $value = $request->header($header);
            if ($value && preg_match($pattern, $value)) {
                ErrorHandlingService::logSecurityEvent('suspicious_header', [
                    'header' => $header,
                    'value' => $value,
                    'ip' => $request->ip()
                ]);
            }
        }

        // Check for missing standard headers
        $standardHeaders = ['user-agent', 'accept', 'accept-language'];
        $missingHeaders = [];

        foreach ($standardHeaders as $header) {
            if (!$request->hasHeader($header)) {
                $missingHeaders[] = $header;
            }
        }

        if (count($missingHeaders) >= 2) {
            ErrorHandlingService::logSecurityEvent('missing_standard_headers', [
                'missing_headers' => $missingHeaders,
                'ip' => $request->ip()
            ]);
        }
    }

    /**
     * Monitor response for potential data leaks
     */
    private function monitorResponse(Request $request, $response): void
    {
        if (method_exists($response, 'getContent')) {
            $content = $response->getContent();

            // Check for potential sensitive data leaks
            $sensitivePatterns = [
                'password' => '/password[\s\'"]*:[\s\'"]*[^,\s\'"]+/i',
                'api_key' => '/api[_-]?key[\s\'"]*:[\s\'"]*[a-zA-Z0-9]{20,}/i',
                'token' => '/token[\s\'"]*:[\s\'"]*[a-zA-Z0-9]{30,}/i',
                'secret' => '/secret[\s\'"]*:[\s\'"]*[^,\s\'"]+/i',
                'credit_card' => '/\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b/',
                'email_pattern' => '/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/'
            ];

            foreach ($sensitivePatterns as $type => $pattern) {
                if (preg_match($pattern, $content)) {
                    ErrorHandlingService::logSecurityEvent('potential_data_leak', [
                        'type' => $type,
                        'url' => $request->fullUrl(),
                        'method' => $request->method(),
                        'ip' => $request->ip(),
                        'user_id' => $request->user()?->id
                    ]);
                }
            }
        }
    }

    /**
     * Log security threat
     */
    private function logSecurityThreat(string $category, string $field, string $input, Request $request): void
    {
        ErrorHandlingService::logSecurityEvent('security_threat_detected', [
            'category' => $category,
            'field' => $field,
            'input_sample' => substr($input, 0, 200), // Limit log size
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'user_id' => $request->user()?->id
        ]);
    }

    /**
     * Penalize IP for suspicious activity
     */
    private function penalizeIp(string $ip, string $reason): void
    {
        $cacheKey = "ip_penalty:{$ip}";
        $penalties = Cache::get($cacheKey, []);

        $penalties[] = [
            'reason' => $reason,
            'timestamp' => now()->timestamp
        ];

        // Keep penalties for 24 hours
        $penalties = array_filter($penalties, function ($penalty) {
            return $penalty['timestamp'] > (now()->timestamp - 86400);
        });

        Cache::put($cacheKey, $penalties, 86400);

        // If too many penalties, consider blocking
        if (count($penalties) > 5) {
            ErrorHandlingService::logSecurityEvent('ip_requires_blocking', [
                'ip' => $ip,
                'penalty_count' => count($penalties),
                'penalties' => $penalties
            ]);
        }
    }
}
