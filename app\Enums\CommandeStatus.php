<?php

namespace App\Enums;

enum CommandeStatus: string
{
    case EN_ATTENTE = 'en_attente';
    case CONFIRMEE = 'confirmee';
    case EN_PREPARATION = 'en_preparation';
    case EXPEDIEE = 'expediee';
    case LIVREE = 'livree';
    case ANNULEE = 'annulee';
    case REMBOURSEE = 'remboursee';
    case RETOURNEE = 'retournee'; // Added RETOURNEE case

    public function label(): string
    {
        return match ($this) {
            self::EN_ATTENTE => 'En attente',
            self::CONFIRMEE => 'Confirmée',
            self::EN_PREPARATION => 'En préparation',
            self::EXPEDIEE => 'Expédiée',
            self::LIVREE => 'Livrée',
            self::ANNULEE => 'Annulée',
            self::REMBOURSEE => 'Remboursée',
            self::RETOURNEE => 'Retournée', // Added label for RETOURNEE
        };
    }
}
