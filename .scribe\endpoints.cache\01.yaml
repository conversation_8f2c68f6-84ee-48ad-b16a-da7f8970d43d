## Autogenerated by Scribe. DO NOT MODIFY.

name: Panier
description: |-

  API pour gérer le panier d'achat
endpoints:
  -
    httpMethods:
      - GET
    uri: api/panier
    metadata:
      groupName: Panier
      groupDescription: |-

        API pour gérer le panier d'achat
      subgroup: ''
      subgroupDescription: ''
      title: 'Affiche le contenu du panier'
      description: "Récupère le contenu du panier actuel de l'utilisateur, identifié soit par son cookie de session, soit par son authentification Keycloak."
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
           "panier": {
             "id": 1,
             "items": [
               {
                 "id": 1,
                 "produit": {
                   "id": 5,
                   "nom": "Smartphone XYZ",
                   "reference": "2-5",
                   "image": "smartphone_xyz.jpg"
                 },
                 "variante": {
                   "id": 3,
                   "sku": "XYZ-RED-128",
                   "attributs": [
                     {
                       "nom": "Couleur",
                       "valeur": "Rouge"
                     },
                     {
                       "nom": "Stockage",
                       "valeur": "128 Go"
                     }
                   ]
                 },
                 "quantite": 1,
                 "prix_unitaire": 599.99,
                 "sous_total": 599.99
               }
             ],
             "total": 599.99
           }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/panier/ajouter
    metadata:
      groupName: Panier
      groupDescription: |-

        API pour gérer le panier d'achat
      subgroup: ''
      subgroupDescription: ''
      title: 'Ajoute un produit au panier'
      description: |-
        Ajoute un produit au panier avec la quantité spécifiée. Si le produit existe déjà dans le panier, la quantité est augmentée.
        Vérifie également la disponibilité du stock avant l'ajout.
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      produit_id:
        name: produit_id
        description: 'ID du produit à ajouter.'
        required: true
        example: 5
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      variante_id:
        name: variante_id
        description: 'ID de la variante du produit (optionnel).'
        required: false
        example: 3
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      quantite:
        name: quantite
        description: 'Quantité à ajouter (minimum 1).'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      produit_id: 5
      variante_id: 3
      quantite: 1
    fileParameters: []
    responses:
      -
        status: 400
        content: |-
          {
            "error": "Stock insuffisant pour ce produit"
          }
        headers: []
        description: ''
        custom: []
      -
        status: 422
        content: |-
          {
            "errors": {
              "produit_id": ["Le champ produit_id est obligatoire."],
              "quantite": ["Le champ quantite est obligatoire."]
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/panier/items/{itemId}'
    metadata:
      groupName: Panier
      groupDescription: |-

        API pour gérer le panier d'achat
      subgroup: ''
      subgroupDescription: ''
      title: "Met à jour la quantité d'un item"
      description: |-
        Met à jour la quantité d'un produit dans le panier. Si la quantité est 0, l'item est supprimé du panier.
        Vérifie également la disponibilité du stock avant la mise à jour.
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      itemId:
        name: itemId
        description: "ID de l'item du panier à mettre à jour."
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      itemId: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      quantite:
        name: quantite
        description: 'Nouvelle quantité (0 pour supprimer).'
        required: true
        example: 3
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      quantite: 3
    fileParameters: []
    responses:
      -
        status: 400
        content: |-
          {
            "error": "Stock insuffisant pour ce produit"
          }
        headers: []
        description: ''
        custom: []
      -
        status: 422
        content: |-
          {
            "errors": {
              "quantite": ["Le champ quantite est obligatoire."]
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/panier/items/{itemId}'
    metadata:
      groupName: Panier
      groupDescription: |-

        API pour gérer le panier d'achat
      subgroup: ''
      subgroupDescription: ''
      title: 'Supprime un item du panier'
      description: "Supprime un produit spécifique du panier, identifié par son ID d'item."
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      itemId:
        name: itemId
        description: "ID de l'item du panier à supprimer."
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      itemId: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 400
        content: |-
          {
            "error": "Item non trouvé dans le panier"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: api/panier/vider
    metadata:
      groupName: Panier
      groupDescription: |-

        API pour gérer le panier d'achat
      subgroup: ''
      subgroupDescription: ''
      title: 'Vide le panier'
      description: 'Supprime tous les produits du panier actuel.'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "message": "Panier vidé avec succès"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/cart
    metadata:
      groupName: Panier
      groupDescription: |-

        API pour gérer le panier d'achat
      subgroup: ''
      subgroupDescription: ''
      title: 'Affiche le contenu du panier'
      description: "Récupère le contenu du panier actuel de l'utilisateur, identifié soit par son cookie de session, soit par son authentification Keycloak."
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
           "panier": {
             "id": 1,
             "items": [
               {
                 "id": 1,
                 "produit": {
                   "id": 5,
                   "nom": "Smartphone XYZ",
                   "reference": "2-5",
                   "image": "smartphone_xyz.jpg"
                 },
                 "variante": {
                   "id": 3,
                   "sku": "XYZ-RED-128",
                   "attributs": [
                     {
                       "nom": "Couleur",
                       "valeur": "Rouge"
                     },
                     {
                       "nom": "Stockage",
                       "valeur": "128 Go"
                     }
                   ]
                 },
                 "quantite": 1,
                 "prix_unitaire": 599.99,
                 "sous_total": 599.99
               }
             ],
             "total": 599.99
           }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/cart/items
    metadata:
      groupName: Panier
      groupDescription: |-

        API pour gérer le panier d'achat
      subgroup: ''
      subgroupDescription: ''
      title: 'Ajoute un produit au panier'
      description: |-
        Ajoute un produit au panier avec la quantité spécifiée. Si le produit existe déjà dans le panier, la quantité est augmentée.
        Vérifie également la disponibilité du stock avant l'ajout.
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      produit_id:
        name: produit_id
        description: 'ID du produit à ajouter.'
        required: true
        example: 5
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      variante_id:
        name: variante_id
        description: 'ID de la variante du produit (optionnel).'
        required: false
        example: 3
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      quantite:
        name: quantite
        description: 'Quantité à ajouter (minimum 1).'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      produit_id: 5
      variante_id: 3
      quantite: 1
    fileParameters: []
    responses:
      -
        status: 400
        content: |-
          {
            "error": "Stock insuffisant pour ce produit"
          }
        headers: []
        description: ''
        custom: []
      -
        status: 422
        content: |-
          {
            "errors": {
              "produit_id": ["Le champ produit_id est obligatoire."],
              "quantite": ["Le champ quantite est obligatoire."]
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/cart/items/{itemId}'
    metadata:
      groupName: Panier
      groupDescription: |-

        API pour gérer le panier d'achat
      subgroup: ''
      subgroupDescription: ''
      title: "Met à jour la quantité d'un item"
      description: |-
        Met à jour la quantité d'un produit dans le panier. Si la quantité est 0, l'item est supprimé du panier.
        Vérifie également la disponibilité du stock avant la mise à jour.
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      itemId:
        name: itemId
        description: "ID de l'item du panier à mettre à jour."
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      itemId: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      quantite:
        name: quantite
        description: 'Nouvelle quantité (0 pour supprimer).'
        required: true
        example: 3
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      quantite: 3
    fileParameters: []
    responses:
      -
        status: 400
        content: |-
          {
            "error": "Stock insuffisant pour ce produit"
          }
        headers: []
        description: ''
        custom: []
      -
        status: 422
        content: |-
          {
            "errors": {
              "quantite": ["Le champ quantite est obligatoire."]
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/cart/items/{itemId}'
    metadata:
      groupName: Panier
      groupDescription: |-

        API pour gérer le panier d'achat
      subgroup: ''
      subgroupDescription: ''
      title: 'Supprime un item du panier'
      description: "Supprime un produit spécifique du panier, identifié par son ID d'item."
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      itemId:
        name: itemId
        description: "ID de l'item du panier à supprimer."
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      itemId: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 400
        content: |-
          {
            "error": "Item non trouvé dans le panier"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: api/cart
    metadata:
      groupName: Panier
      groupDescription: |-

        API pour gérer le panier d'achat
      subgroup: ''
      subgroupDescription: ''
      title: 'Vide le panier'
      description: 'Supprime tous les produits du panier actuel.'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "message": "Panier vidé avec succès"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/cart/merge
    metadata:
      groupName: Panier
      groupDescription: |-

        API pour gérer le panier d'achat
      subgroup: ''
      subgroupDescription: ''
      title: "Fusionner un panier invité avec le panier de l'utilisateur connecté"
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      guest_id:
        name: guest_id
        description: 'The <code>guest_id</code> of an existing record in the paniers table.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      guest_id: architecto
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
