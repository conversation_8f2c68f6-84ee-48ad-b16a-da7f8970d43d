<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class CookieCartMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated using both Auth facade and token
        $isAuthenticatedBySession = auth()->check();
        $userIdFromSession = $isAuthenticatedBySession ? auth()->id() : null;

        // Check for token in cookie or Authorization header
        $token = $request->cookie('access_token');

        if (!$token && $request->hasHeader('Authorization')) {
            $authHeader = $request->header('Authorization');
            if (strpos($authHeader, 'Bearer ') === 0) {
                $token = substr($authHeader, 7);
            }
        }

        $userIdFromToken = null;
        $isAuthenticatedByToken = false;

        if ($token) {
            try {
                // Try to validate the token
                $keycloakService = app(\App\Services\KeycloakService::class);
                $decoded = $keycloakService->validateToken($token);

                // Find the user by Keycloak ID
                $user = \App\Models\User::where('keycloak_id', $decoded->sub)->first();

                if ($user) {
                    $userIdFromToken = $user->id;
                    $isAuthenticatedByToken = true;

                    // If not already authenticated by session, login the user
                    if (!$isAuthenticatedBySession) {
                        auth()->login($user);
                        $isAuthenticatedBySession = true;
                        $userIdFromSession = $user->id;
                    }
                }
            } catch (\Exception $e) {
                // Token validation failed, log the error
                \Illuminate\Support\Facades\Log::warning('Token validation failed in CookieCartMiddleware', [
                    'error' => $e->getMessage()
                ]);
            }
        }

        // Use either authentication method
        $isAuthenticated = $isAuthenticatedBySession || $isAuthenticatedByToken;
        $userId = $userIdFromSession ?? $userIdFromToken;

        // Get guest ID from cookie
        $guestId = $request->cookie('cart_guest_id');

        // Log the incoming guest ID and authentication details for debugging
        Log::debug('Cart and authentication details', [
            'guest_id' => $guestId,
            'user_id' => $userId,
            'is_authenticated' => $isAuthenticated,
            'is_authenticated_by_session' => $isAuthenticatedBySession,
            'is_authenticated_by_token' => $isAuthenticatedByToken,
            'user_id_from_session' => $userIdFromSession,
            'user_id_from_token' => $userIdFromToken,
            'has_token' => !empty($token),
            'path' => $request->path(),
            'method' => $request->method()
        ]);

        // If no guest ID exists, create a new one (only for guests)
        if (!$guestId && !$isAuthenticated) {
            $guestId = \App\Models\Panier::generateGuestId();
            Log::debug('Created new guest ID', ['guest_id' => $guestId]);
        }

        // If user is authenticated, handle cart association and merging
        if ($isAuthenticated && $userId) {
            // First, check if the user already has a cart
            $userCart = \App\Models\Panier::where('client_id', $userId)->first();

            // If there's a guest cart, check if we need to merge or associate it
            if ($guestId) {
                $guestCart = \App\Models\Panier::where('guest_id', $guestId)->first();

                if ($guestCart) {
                    if (!$userCart) {
                        // User doesn't have a cart yet, so associate the guest cart with the user
                        Log::debug('Associating guest cart with user', [
                            'guest_cart_id' => $guestCart->id,
                            'user_id' => $userId
                        ]);

                        $guestCart->client_id = $userId;
                        $guestCart->guest_id = null;
                        $guestCart->save();

                        // The guest cart is now the user cart
                        $userCart = $guestCart;
                    } else if ($guestCart->id !== $userCart->id) {
                        // User has a cart and there's a different guest cart, so merge them
                        Log::debug('Merging carts', [
                            'guest_cart_id' => $guestCart->id,
                            'user_cart_id' => $userCart->id
                        ]);

                        // Merge the carts
                        \App\Models\Panier::mergeCarts($guestId, $userId);

                        // Update the user cart reference
                        $userCart = \App\Models\Panier::where('client_id', $userId)->first();
                    }

                    // Clear the guest cart cookie since we've associated or merged it
                    $guestId = null;
                }
            }

            // If the user still doesn't have a cart, create one
            if (!$userCart) {
                Log::debug('Creating new cart for user', ['user_id' => $userId]);

                $userCart = \App\Models\Panier::create([
                    'client_id' => $userId,
                    'guest_id' => null
                ]);
            }
        }

        // Add guest ID and authentication details to request for controllers to use
        $request->attributes->add([
            'cart_guest_id' => $guestId,
            'is_authenticated' => $isAuthenticated,
            'user_id' => $userId
        ]);

        // Process the request
        $response = $next($request);

        // Always refresh the cookie to extend its lifetime
        // This ensures the cookie doesn't expire during an active session
        // Use SameSite=None for cross-domain setups

        // Déterminer si nous sommes en environnement sécurisé (HTTPS)
        $isSecure = app()->environment('production') || request()->secure();

        // En production ou en HTTPS, utiliser SameSite=None avec Secure=true
        // En développement local sans HTTPS, utiliser SameSite=Lax
        $sameSite = $isSecure ? 'none' : 'lax';

        Log::debug('CookieCartMiddleware - Configuration du cookie', [
            'environment' => app()->environment(),
            'is_secure' => $isSecure,
            'same_site' => $sameSite
        ]);

        // Set the guest ID cookie
        $cookie = cookie(
            'cart_guest_id',
            $guestId,
            43200,          // 30 days
            '/',            // Path
            null,           // Domain (null = current domain)
            $isSecure,      // Secure (true en production/HTTPS, false en dev local)
            true,           // HttpOnly (true pour plus de sécurité)
            false,          // Raw
            $sameSite       // SameSite policy (none pour cross-domain, lax pour dev local)
        );

        $response->headers->setCookie($cookie);

        // Add debug header to help troubleshoot
        $response->headers->set('X-Cart-Guest-ID', $guestId);

        // For backward compatibility, also remove the old cart_session cookie if it exists
        if ($request->cookie('cart_session')) {
            $forgetCookie = cookie()->forget('cart_session');
            $response->headers->setCookie($forgetCookie);
            Log::debug('Removing old cart session cookie');
        }

        // Log the outgoing guest ID for debugging
        Log::debug('Cart guest cookie set', [
            'guest_id' => $guestId,
            'cookie_params' => [
                'name' => 'cart_guest_id',
                'value' => $guestId,
                'expires' => 43200,
                'path' => '/',
                'domain' => null,
                'secure' => $isSecure,
                'httpOnly' => true,
                'sameSite' => $sameSite
            ]
        ]);

        return $response;
    }
}
