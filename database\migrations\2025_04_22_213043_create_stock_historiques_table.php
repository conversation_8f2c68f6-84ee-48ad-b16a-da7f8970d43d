<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stock_historiques', function (Blueprint $table) {
            $table->id();
            $table->foreignId('produit_id')->constrained('produits')->onDelete('cascade');
            $table->integer('quantite_avant')->default(0);
            $table->integer('quantite_apres')->default(0);
            $table->integer('quantite_modifiee');
            $table->string('type_mouvement'); // 'entree', 'sortie', 'ajustement', 'commande', 'retour'
            $table->string('reference_mouvement')->nullable(); // Référence externe (numéro de commande, etc.)
            $table->foreignId('user_id')->nullable()->constrained('users')->nullOnDelete();
            $table->text('commentaire')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stock_historiques');
    }
};
