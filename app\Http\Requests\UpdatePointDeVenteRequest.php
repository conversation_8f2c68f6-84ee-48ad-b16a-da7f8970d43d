<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdatePointDeVenteRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'nom' => 'sometimes|required|string|max:255',
            'adresse' => 'nullable|string',
            'telephone' => 'nullable|string',
            'email' => 'nullable|email',
            'remise' => 'sometimes|required|numeric|min:0|max:100',
            'description' => 'nullable|string',
            'statut' => ['nullable', Rule::in(['actif', 'inactif'])],
        ];
    }

    public function messages()
    {
        return [
            'nom.required' => 'Le nom est obligatoire.',
            'remise.required' => 'La remise est obligatoire.',
            'remise.numeric' => 'La remise doit être un nombre.',
            'remise.min' => 'La remise doit être au moins 0.',
            'remise.max' => 'La remise ne peut pas dépasser 100.',
            'statut.in' => 'Le statut doit être "actif" ou "inactif".',
        ];
    }
} 