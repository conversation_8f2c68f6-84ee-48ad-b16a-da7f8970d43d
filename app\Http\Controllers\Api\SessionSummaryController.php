<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\SessionSummary;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class SessionSummaryController extends Controller
{
    /**
     * Enregistrer un nouveau résumé de session
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'session_duration' => 'required|numeric|min:0',
            'total_predictions' => 'required|integer|min:0',
            'satisfied_count' => 'required|integer|min:0',
            'neutral_count' => 'required|integer|min:0',
            'unsatisfied_count' => 'required|integer|min:0',
            'average_confidence' => 'required|numeric|min:0|max:1',
            'most_common_prediction' => 'required|string|max:255',
            'session_id' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Données de validation échouées',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Ajouter des informations supplémentaires
            $data = $validator->validated();
            $data['user_agent'] = $request->header('User-Agent');
            $data['ip_address'] = $request->ip();

            $summary = SessionSummary::create($data);

            return response()->json([
                'success' => true,
                'message' => 'Résumé de session enregistré avec succès',
                'data' => $summary
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de l\'enregistrement du résumé de session',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Récupérer tous les résumés de session avec pagination
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $perPage = $request->get('per_page', 15);
            $perPage = min($perPage, 100); // Limiter à 100 par page

            $summaries = SessionSummary::orderBy('created_at', 'desc')
                ->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $summaries
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération des résumés',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Récupérer un résumé de session spécifique
     */
    public function show($id): JsonResponse
    {
        try {
            $summary = SessionSummary::findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $summary
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Résumé de session non trouvé',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Obtenir des statistiques globales
     */
    public function statistics(): JsonResponse
    {
        try {
            $stats = [
                'total_sessions' => SessionSummary::count(),
                'average_session_duration' => SessionSummary::avg('session_duration'),
                'total_predictions' => SessionSummary::sum('total_predictions'),
                'average_confidence' => SessionSummary::avg('average_confidence'),
                'satisfaction_stats' => [
                    'total_satisfied' => SessionSummary::sum('satisfied_count'),
                    'total_neutral' => SessionSummary::sum('neutral_count'),
                    'total_unsatisfied' => SessionSummary::sum('unsatisfied_count'),
                ],
                'most_common_predictions' => SessionSummary::selectRaw('most_common_prediction, COUNT(*) as count')
                    ->groupBy('most_common_prediction')
                    ->orderBy('count', 'desc')
                    ->limit(10)
                    ->get()
            ];

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors du calcul des statistiques',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
