# Système d'Attributs et Variantes

Ce document décrit le nouveau système d'attributs et de variantes qui remplace l'ancien système de caractéristiques.

## Table des matières

1. [Introduction](#introduction)
2. [Modèles de données](#modèles-de-données)
3. [API pour les attributs](#api-pour-les-attributs)
4. [API pour les variantes](#api-pour-les-variantes)
5. [Exemples d'utilisation](#exemples-dutilisation)
6. [Migration depuis le système de caractéristiques](#migration-depuis-le-système-de-caractéristiques)

## Introduction

Le système d'attributs et de variantes permet de gérer les caractéristiques des produits de manière flexible et structurée. Il remplace l'ancien système de caractéristiques qui était moins flexible et moins performant.

### Principales fonctionnalités

- Groupes d'attributs pour organiser les attributs
- Types de valeurs d'attributs (texte, nombre, booléen)
- Association d'attributs aux sous-catégories
- Création de variantes de produits basées sur des combinaisons d'attributs
- Filtrage des produits par attributs
- Comparaison de produits basée sur leurs attributs

## Modèles de données

### Groupe d'attributs

Un groupe d'attributs permet d'organiser les attributs en catégories logiques.

```mermaid
classDiagram
    class GroupeAttribut {
        +id: int
        +nom: string
        +description: string
        +created_at: timestamp
        +updated_at: timestamp
    }
```

### Attribut

Un attribut représente une caractéristique d'un produit, comme la couleur, la taille, etc.

```mermaid
classDiagram
    class Attribut {
        +id: int
        +nom: string
        +description: string
        +type_valeur: string (texte, nombre, booléen)
        +groupe_attribut_id: int
        +filtrable: boolean
        +comparable: boolean
        +affichable: boolean
        +ordre: int
        +created_at: timestamp
        +updated_at: timestamp
    }
```

### Valeur d'attribut

Une valeur d'attribut représente une valeur possible pour un attribut, comme "Rouge" pour l'attribut "Couleur".

```mermaid
classDiagram
    class ValeurAttribut {
        +id: int
        +attribut_id: int
        +valeur_texte: string
        +valeur_nombre: decimal
        +valeur_booleen: boolean
        +created_at: timestamp
        +updated_at: timestamp
    }
```

### Association attribut-sous-catégorie

Une association entre un attribut et une sous-catégorie indique que cet attribut est disponible pour les produits de cette sous-catégorie.

```mermaid
classDiagram
    class AttributCategorie {
        +attribut_id: int
        +sous_categorie_id: int
        +obligatoire: boolean
        +created_at: timestamp
        +updated_at: timestamp
    }
```

### Valeur d'attribut de produit

Une valeur d'attribut de produit représente la valeur d'un attribut pour un produit spécifique.

```mermaid
classDiagram
    class ProduitValeurAttribut {
        +id: int
        +produit_id: int
        +attribut_id: int
        +valeur_texte: string
        +valeur_nombre: decimal
        +valeur_booleen: boolean
        +created_at: timestamp
        +updated_at: timestamp
    }
```

### Variante de produit

Une variante de produit représente une version spécifique d'un produit avec des valeurs d'attributs spécifiques.

```mermaid
classDiagram
    class ProduitVariante {
        +id: int
        +produit_parent_id: int
        +sku: string
        +prix: decimal
        +stock: int
        +actif: boolean
        +created_at: timestamp
        +updated_at: timestamp
        +deleted_at: timestamp
    }
```

### Valeur d'attribut de variante

Une valeur d'attribut de variante représente la valeur d'un attribut pour une variante spécifique.

```mermaid
classDiagram
    class VarianteValeurAttribut {
        +id: int
        +variante_id: int
        +attribut_id: int
        +valeur_texte: string
        +valeur_nombre: decimal
        +valeur_booleen: boolean
        +created_at: timestamp
        +updated_at: timestamp
    }
```

## API pour les attributs

### Administration des attributs

#### Groupes d'attributs

| Méthode | URL | Description |
|---------|-----|-------------|
| GET | `/api/admin/groupes-attributs` | Liste de tous les groupes d'attributs |
| POST | `/api/admin/groupes-attributs` | Créer un nouveau groupe d'attributs |
| GET | `/api/admin/groupes-attributs/{id}` | Détails d'un groupe d'attributs spécifique |
| PUT | `/api/admin/groupes-attributs/{id}` | Mettre à jour un groupe d'attributs |
| DELETE | `/api/admin/groupes-attributs/{id}` | Supprimer un groupe d'attributs |

#### Attributs

| Méthode | URL | Description |
|---------|-----|-------------|
| GET | `/api/admin/attributs` | Liste de tous les attributs |
| POST | `/api/admin/attributs` | Créer un nouvel attribut |
| GET | `/api/admin/attributs/{id}` | Détails d'un attribut spécifique |
| PUT | `/api/admin/attributs/{id}` | Mettre à jour un attribut |
| DELETE | `/api/admin/attributs/{id}` | Supprimer un attribut |
| POST | `/api/admin/attributs/{id}/sous-categories/{sousCategorieId}` | Associer un attribut à une sous-catégorie |
| DELETE | `/api/admin/attributs/{id}/sous-categories/{sousCategorieId}` | Dissocier un attribut d'une sous-catégorie |

### Attributs de produits

| Méthode | URL | Description |
|---------|-----|-------------|
| GET | `/api/produits/{id}/attributs` | Obtenir les attributs d'un produit |
| POST | `/api/produits/{id}/attributs` | Définir les attributs d'un produit |
| PUT | `/api/produits/{id}/attributs/{attributId}` | Mettre à jour un attribut d'un produit |
| DELETE | `/api/produits/{id}/attributs/{attributId}` | Supprimer un attribut d'un produit |
| GET | `/api/attributs/filtrables` | Obtenir les attributs filtrables |

### Attributs de sous-sous-catégories

| Méthode | URL | Description |
|---------|-----|-------------|
| GET | `/api/sous_sousCategories/{id}/attributs` | Obtenir les attributs d'une sous-sous-catégorie (hérités de la sous-catégorie parente) |

## API pour les variantes

| Méthode | URL | Description |
|---------|-----|-------------|
| GET | `/api/produits/{id}/variantes` | Obtenir les variantes d'un produit |
| POST | `/api/produits/{produit_id}/variantes` | Créer une nouvelle variante pour un produit |
| GET | `/api/variantes/{id}` | Détails d'une variante spécifique |
| PUT | `/api/variantes/{id}` | Mettre à jour une variante |
| DELETE | `/api/variantes/{id}` | Supprimer une variante |
| PATCH | `/api/variantes/{id}/stock` | Mettre à jour le stock d'une variante |

## Exemples d'utilisation

### Création d'un groupe d'attributs

```http
POST /api/admin/groupes-attributs
Content-Type: application/json

{
  "nom": "Caractéristiques physiques",
  "description": "Caractéristiques physiques du produit"
}
```

### Création d'un attribut

```http
POST /api/admin/attributs
Content-Type: application/json

{
  "nom": "Couleur",
  "description": "Couleur du produit",
  "type_valeur": "texte",
  "groupe_attribut_id": 1,
  "filtrable": true,
  "comparable": true,
  "affichable": true,
  "ordre": 1
}
```

### Association d'un attribut à une sous-catégorie

```http
POST /api/admin/attributs/1/sous-categories/2
Content-Type: application/json

{
  "obligatoire": true
}
```

### Définition des attributs d'un produit

```http
POST /api/produits/1/attributs
Content-Type: application/json

{
  "attributs": [
    {
      "attribut_id": 1,
      "valeur_texte": "Rouge"
    },
    {
      "attribut_id": 2,
      "valeur_nombre": 42
    }
  ]
}
```

### Création d'une variante de produit

```http
POST /api/produits/1/variantes
Content-Type: application/json

{
  "sku": "PROD1-ROUGE-L",
  "prix": 19.99,
  "stock": 10,
  "actif": true,
  "attributs": [
    {
      "attribut_id": 1,
      "valeur_texte": "Rouge"
    },
    {
      "attribut_id": 2,
      "valeur_texte": "L"
    }
  ]
}
```

### Filtrage des produits par attributs

```http
GET /api/produits/filtrer?attributs[1]=Rouge&attributs[2]=L
```

## Migration depuis le système de caractéristiques

Le système de caractéristiques est déprécié et a été remplacé par le système d'attributs. Voici comment migrer:

1. **Groupes de caractéristiques** → **Groupes d'attributs**
   - Créez un groupe d'attributs pour chaque groupe de caractéristiques

2. **Caractéristiques** → **Attributs**
   - Créez un attribut pour chaque caractéristique
   - Définissez le type de valeur approprié (texte, nombre, booléen)
   - Associez l'attribut au groupe d'attributs correspondant

3. **Valeurs de caractéristiques** → **Valeurs d'attributs**
   - Créez une valeur d'attribut pour chaque valeur de caractéristique
   - Associez la valeur d'attribut à l'attribut correspondant

4. **Caractéristiques de produits** → **Attributs de produits**
   - Définissez les attributs de chaque produit en utilisant l'API d'attributs

5. **Variantes de produits**
   - Créez des variantes de produits basées sur les combinaisons d'attributs
   - Associez les valeurs d'attributs aux variantes

### Migration depuis le système de caractéristiques

Le système de caractéristiques a été complètement migré vers le nouveau système d'attributs. Les anciennes données ont été conservées et transformées en attributs compatibles.

### Avantages du nouveau système

- **Flexibilité**: Types de valeurs multiples (texte, nombre, booléen)
- **Organisation**: Groupes d'attributs pour une meilleure organisation
- **Performance**: Optimisé pour les requêtes de filtrage et de recherche
- **Variantes**: Support natif pour les variantes de produits
- **Filtrage**: Filtrage avancé des produits par attributs
- **Comparaison**: Comparaison de produits basée sur leurs attributs
