<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confirmation de commande - Lack parisien</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600&family=Inter:wght@300;400;500&display=swap');

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            line-height: 1.7;
            color: #5a4a3a;
            max-width: 600px;
            margin: 0 auto;
            padding: 0;
            background: linear-gradient(135deg, #f8f6f0 0%, #f1ede4 100%);
            font-weight: 300;
        }

        .container {
            background-color: #ffffff;
            margin: 20px;
            border-radius: 0;
            box-shadow: 0 8px 32px rgba(139, 117, 95, 0.12);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #d4c4a8 0%, #c8b896 100%);
            color: #5a4a3a;
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="0.5" fill="%23ffffff" opacity="0.1"/><circle cx="75" cy="75" r="0.3" fill="%23ffffff" opacity="0.08"/><circle cx="50" cy="10" r="0.4" fill="%23ffffff" opacity="0.06"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
            opacity: 0.3;
        }

        .header h1 {
            margin: 0;
            font-family: 'Playfair Display', serif;
            font-size: 28px;
            font-weight: 600;
            letter-spacing: -0.5px;
            position: relative;
            z-index: 1;
        }

        .header p {
            margin: 8px 0 0 0;
            font-size: 14px;
            opacity: 0.8;
            font-weight: 400;
            position: relative;
            z-index: 1;
        }

        .content {
            padding: 40px 30px;
        }

        .order-info {
            background: linear-gradient(135deg, #faf9f6 0%, #f5f3ee 100%);
            padding: 30px;
            border-radius: 2px;
            margin-bottom: 30px;
            border-left: 3px solid #d4c4a8;
            position: relative;
        }

        .order-info h2 {
            margin-top: 0;
            color: #5a4a3a;
            font-family: 'Playfair Display', serif;
            font-size: 22px;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .order-info p {
            margin: 0;
            color: #6b5b4b;
            line-height: 1.6;
        }

        .order-details {
            background-color: #fbfaf8;
            padding: 30px;
            border-radius: 2px;
            margin-bottom: 30px;
            border: 1px solid #f0ede6;
        }

        .order-details h3 {
            margin-top: 0;
            color: #5a4a3a;
            font-family: 'Playfair Display', serif;
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 25px;
        }

        .field {
            margin-bottom: 20px;
            padding: 18px 20px;
            background-color: #ffffff;
            border-radius: 1px;
            border: 1px solid #f0ede6;
            transition: all 0.2s ease;
        }

        .field:hover {
            border-color: #d4c4a8;
            box-shadow: 0 2px 8px rgba(139, 117, 95, 0.08);
        }

        .field-label {
            font-weight: 500;
            color: #8b7560;
            margin-bottom: 8px;
            text-transform: uppercase;
            font-size: 11px;
            letter-spacing: 1.2px;
        }

        .field-value {
            font-size: 16px;
            color: #5a4a3a;
            font-weight: 400;
        }

        .total {
            background: linear-gradient(135deg, #8b7560 0%, #7a6550 100%);
            color: #ffffff;
            padding: 25px;
            border-radius: 2px;
            text-align: center;
            font-size: 20px;
            font-weight: 500;
            margin: 30px 0;
            letter-spacing: 0.5px;
        }

        .status {
            display: inline-block;
            padding: 10px 18px;
            border-radius: 25px;
            font-weight: 500;
            text-transform: uppercase;
            font-size: 11px;
            letter-spacing: 1px;
        }

        .status.en_attente {
            background-color: #e8dcc0;
            color: #8b7560;
        }

        .status.confirmee {
            background-color: #d4c4a8;
            color: #5a4a3a;
        }

        .status.expediee {
            background-color: #c8b896;
            color: #5a4a3a;
        }

        .status.livree {
            background-color: #8b7560;
            color: #ffffff;
        }

        .footer {
            margin-top: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #faf9f6 0%, #f5f3ee 100%);
            text-align: center;
            color: #8b7560;
            font-size: 13px;
            line-height: 1.6;
            border-top: 1px solid #f0ede6;
        }

        .footer strong {
            color: #5a4a3a;
            font-weight: 500;
        }

        .next-steps {
            background: linear-gradient(135deg, #faf9f6 0%, #f5f3ee 100%);
            padding: 30px;
            border-radius: 2px;
            margin: 30px 0;
            border-left: 3px solid #d4c4a8;
        }

        .next-steps h3 {
            color: #5a4a3a;
            margin-top: 0;
            font-family: 'Playfair Display', serif;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .next-steps ul {
            margin: 0;
            padding-left: 20px;
            color: #6b5b4b;
        }

        .next-steps li {
            margin-bottom: 8px;
            line-height: 1.6;
        }

        .products-list {
            margin-top: 15px;
        }

        .product-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0ede6;
        }

        .product-item:last-child {
            border-bottom: none;
        }

        .product-item strong {
            color: #5a4a3a;
            font-weight: 500;
        }

        .product-item small {
            color: #8b7560;
            font-size: 13px;
        }

        /* Mobile responsiveness */
        @media only screen and (max-width: 600px) {
            .container {
                margin: 10px;
            }

            .header, .content {
                padding: 30px 20px;
            }

            .order-info, .order-details, .next-steps {
                padding: 20px;
            }

            .field {
                padding: 15px;
            }

            .header h1 {
                font-size: 24px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>Commande Confirmée</h1>
            <p>Lack parisien - Votre commande a été reçue avec succès</p>
        </div>

        <div class="content">
            <div class="order-info">
                <h2>Merci pour votre confiance</h2>
                <p>Votre commande a été confirmée et est en cours de traitement. Nous vous tiendrons informé(e) de chaque étape jusqu'à la livraison.</p>
            </div>

            <div class="order-details">
                <h3>Détails de la commande</h3>

                <div class="field">
                    <div class="field-label">Numéro de commande</div>
                    <div class="field-value">#{{ $orderNumber }}</div>
                </div>

                <div class="field">
                    <div class="field-label">Date de commande</div>
                    <div class="field-value">{{ $createdAt }}</div>
                </div>

                <div class="field">
                    <div class="field-label">Statut</div>
                    <div class="field-value">
                        <span class="status {{ $orderStatus }}">{{ ucfirst(str_replace('_', ' ', $orderStatus)) }}</span>
                    </div>
                </div>

                @if($commande->produits && $commande->produits->count() > 0)
                <div class="field">
                    <div class="field-label">Produits commandés</div>
                    <div class="products-list">
                        @foreach($commande->produits as $produit)
                        <div class="product-item">
                            <div>
                                <strong>{{ $produit->nom }}</strong>
                                <br>
                                <small>Quantité: {{ $produit->pivot->quantite ?? 1 }}</small>
                            </div>
                            <div>
                                <strong>{{ number_format($produit->prix * ($produit->pivot->quantite ?? 1), 2) }}€</strong>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>

            <div class="total">
                Total: {{ number_format($orderTotal, 2) }}€
            </div>

            <div class="next-steps">
                <h3>Prochaines étapes</h3>
                <ul>
                    <li>Préparation de votre commande dans nos ateliers</li>
                    <li>Notification d'expédition avec numéro de suivi</li>
                    <li>Livraison à l'adresse indiquée</li>
                    <li>Service client disponible pour toute question</li>
                </ul>
            </div>
        </div>

        <div class="footer">
            <p><strong>Lack parisien</strong> - L'art de vivre à la française</p>
            <p>Cet email a été généré automatiquement pour la commande #{{ $orderNumber }}</p>
            <p>Pour toute question, n'hésitez pas à nous contacter</p>
        </div>
    </div>
</body>

</html>
