<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;

class MigrateUserProfiles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:migrate-profiles';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate users from type_client to profil_remise';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Migrating user profiles from type_client to profil_remise...');

        $users = User::whereNull('profil_remise')->get();
        $count = 0;

        foreach ($users as $user) {
            // This will trigger the accessor which maps type_client to profil_remise
            $profilRemise = $user->profil_remise;

            // Save the new value to the database
            $user->profil_remise = $profilRemise;
            $user->save();

            $count++;
        }

        $this->info("Migrated {$count} users.");

        return Command::SUCCESS;
    }
}
