<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AjusterStockRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'quantite' => 'required|integer|min:0',
            'commentaire' => 'nullable|string|max:1000',
        ];
    }

    public function messages()
    {
        return [
            'quantite.required' => 'La quantité est obligatoire.',
            'quantite.integer' => 'La quantité doit être un nombre entier.',
            'quantite.min' => 'La quantité doit être au moins 0.',
            'commentaire.max' => 'Le commentaire ne peut pas dépasser 1000 caractères.',
        ];
    }
} 