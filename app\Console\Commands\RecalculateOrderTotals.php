<?php

namespace App\Console\Commands;

use App\Models\Commande;
use Illuminate\Console\Command;

class RecalculateOrderTotals extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'orders:recalculate-totals {--dry-run : Show what would be updated without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Recalculate order totals for all existing orders';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $isDryRun = $this->option('dry-run');

        $this->info('Starting order total recalculation...');

        $orders = Commande::with('produits')->get();

        if ($orders->isEmpty()) {
            $this->info('No orders found.');
            return 0;
        }

        $updated = 0;
        $bar = $this->output->createProgressBar($orders->count());
        $bar->start();

        foreach ($orders as $order) {
            $oldTotal = $order->total_commande;

            if ($order->produits->isNotEmpty()) {
                $newTotal = $order->calculateTotal();

                if ($oldTotal != $newTotal) {
                    if (!$isDryRun) {
                        $order->updateQuietly(['total_commande' => $newTotal]);
                    }

                    $this->newLine();
                    $this->info("Order ID {$order->id}: {$oldTotal} -> {$newTotal}" . ($isDryRun ? ' (DRY RUN)' : ''));
                    $updated++;
                }
            } else {
                $this->newLine();
                $this->warn("Order ID {$order->id}: No products found, skipping");
            }

            $bar->advance();
        }

        $bar->finish();
        $this->newLine(2);

        if ($isDryRun) {
            $this->info("DRY RUN: Would update {$updated} orders out of {$orders->count()} total orders.");
            $this->info("Run without --dry-run to apply changes.");
        } else {
            $this->info("Updated {$updated} orders out of {$orders->count()} total orders.");
        }

        return 0;
    }
}
