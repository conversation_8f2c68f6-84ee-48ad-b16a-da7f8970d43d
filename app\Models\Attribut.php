<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Attribut extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'nom',
        'description',
        'type_valeur',
        'groupe_id',
        'obligatoire',
        'filtrable',
        'comparable'
    ];

    protected $casts = [
        'obligatoire' => 'boolean',
        'filtrable' => 'boolean',
        'comparable' => 'boolean'
    ];

    /**
     * Le groupe auquel appartient cet attribut
     */
    public function groupe()
    {
        return $this->belongsTo(GroupeAttribut::class, 'groupe_id');
    }

    /**
     * Les sous-catégories associées à cet attribut
     */
    public function sousCategories()
    {
        return $this->belongsToMany(SousCategorie::class, 'attribut_categorie', 'attribut_id', 'sous_categorie_id')
            ->withPivot('obligatoire')
            ->withTimestamps();
    }

    /**
     * Les valeurs de cet attribut pour les produits
     */
    public function produitValeurs()
    {
        return $this->hasMany(ProduitValeur::class, 'attribut_id');
    }

    /**
     * Les valeurs de cet attribut pour les variantes de produits
     */
    public function varianteValeurs()
    {
        return $this->hasMany(VarianteValeur::class, 'attribut_id');
    }
}
