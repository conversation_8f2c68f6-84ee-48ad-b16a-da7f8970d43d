<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Exceptions\ThrottleRequestsException;
use Symfony\Component\HttpFoundation\Response;

class EnhancedRateLimitMiddleware
{
    /**
     * Handle an incoming request with enhanced rate limiting.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if rate limiting is disabled via environment variable
        if (env('RATE_LIMITING_ENABLED', true) === false || env('RATE_LIMITING_ENABLED', true) === 'false') {
            return $next($request);
        }

        $limits = config('rate_limiting.endpoints', [
            'auth' => ['attempts' => 999999, 'decay' => 60],
            'api' => ['attempts' => 999999, 'decay' => 60],
            'search' => ['attempts' => 999999, 'decay' => 60],
            'upload' => ['attempts' => 999999, 'decay' => 60],
            'checkout' => ['attempts' => 999999, 'decay' => 60],
            'admin' => ['attempts' => 999999, 'decay' => 60],
        ]);

        $routeGroup = $this->getRouteGroup($request);
        $limit = $limits[$routeGroup] ?? $limits['api'];

        $key = $this->resolveRequestSignature($request, $routeGroup);

        // Check if we're exceeding the limit
        if (RateLimiter::tooManyAttempts($key, $limit['attempts'])) {
            Log::warning('Rate limit exceeded', [
                'ip' => $request->ip(),
                'user_id' => auth()->id(),
                'route' => $request->route()?->getName(),
                'route_group' => $routeGroup,
                'attempts' => RateLimiter::attempts($key),
                'max_attempts' => $limit['attempts'],
                'user_agent' => $request->userAgent(),
            ]);

            $retryAfter = RateLimiter::availableIn($key);

            throw new ThrottleRequestsException(
                'Too Many Attempts.',
                null,
                [],
                $retryAfter
            );
        }

        // Hit the rate limiter
        RateLimiter::hit($key, $limit['decay']);

        $response = $next($request);

        // Add rate limit headers
        $response->headers->set('X-RateLimit-Limit', $limit['attempts']);
        $response->headers->set('X-RateLimit-Remaining', $limit['attempts'] - RateLimiter::attempts($key));
        $response->headers->set('X-RateLimit-Reset', RateLimiter::availableIn($key) + time());

        return $response;
    }

    /**
     * Determine the route group for rate limiting.
     */
    private function getRouteGroup(Request $request): string
    {
        $path = $request->path();
        $route = $request->route();

        // Check for authentication routes
        if (str_contains($path, 'auth') || str_contains($path, 'login') || str_contains($path, 'register')) {
            return 'auth';
        }

        // Check for search routes
        if (str_contains($path, 'search') || $request->has('search')) {
            return 'search';
        }

        // Check for upload routes
        if (str_contains($path, 'upload') || $request->hasFile('image') || $request->hasFile('file')) {
            return 'upload';
        }

        // Check for checkout/payment routes
        if (str_contains($path, 'checkout') || str_contains($path, 'payment') || str_contains($path, 'commande')) {
            return 'checkout';
        }

        // Check for admin routes
        if (str_contains($path, 'admin') || ($route && str_contains($route->getName() ?? '', 'admin'))) {
            return 'admin';
        }

        return 'api';
    }

    /**
     * Resolve the request signature for rate limiting.
     */
    private function resolveRequestSignature(Request $request, string $routeGroup): string
    {
        $identifier = $request->ip();

        // For authenticated users, also include user ID
        if (auth()->check()) {
            $identifier .= '|user:' . auth()->id();
        }

        // For specific route groups, add additional context
        if ($routeGroup === 'auth') {
            // For auth routes, limit by IP only to prevent abuse
            return 'rate_limit:auth:' . $request->ip();
        }

        if ($routeGroup === 'search') {
            // For search, include search term hash for more granular limiting
            $searchTerm = $request->input('search', '');
            return 'rate_limit:search:' . $identifier . ':' . md5($searchTerm);
        }

        return 'rate_limit:' . $routeGroup . ':' . $identifier;
    }
}
