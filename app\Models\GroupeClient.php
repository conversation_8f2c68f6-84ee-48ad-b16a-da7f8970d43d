<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class GroupeClient extends Model
{
    protected $table = 'groupes_clients';
    
    protected $fillable = [
        'nom',
        'description',
        'remise',
        'statut'
    ];
    
    protected $casts = [
        'remise' => 'decimal:2'
    ];
    
    /**
     * Get the users (clients) associated with this group
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }
    
    /**
     * Get all orders from clients of this group
     */
    public function commandes()
    {
        return $this->hasManyThrough(Commande::class, User::class);
    }
}
