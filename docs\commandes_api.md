# API des Commandes

Ce document décrit les endpoints API pour la gestion des commandes dans le système.

## Format de Réponse Standardisé

Toutes les réponses suivent le format standardisé suivant :

### Réponse de Succès

```json
{
  "status": "success",
  "message": "Message de succès",
  "data": {
    // Données de la réponse
  }
}
```

### Réponse d'Erreur

```json
{
  "status": "error",
  "message": "Message d'erreur",
  "errors": {
    // Détails des erreurs (validation, etc.)
  }
}
```

## Endpoints

### Récupérer la liste des commandes

```
GET /api/commandes
```

Récupère une liste paginée des commandes.

#### Paramètres de Requête

| Paramètre | Type | Description |
|-----------|------|-------------|
| user_id | integer | Filtrer par ID d'utilisateur |
| status | string | Filtrer par statut (en_attente, confirmee, en_preparation, expediee, livree, annulee, remboursee) |
| date_from | date | Filtrer par date de début (format YYYY-MM-DD) |
| date_to | date | Filtrer par date de fin (format YYYY-MM-DD) |
| with | string | Relations à inclure (user,produits,paiement) séparées par des virgules |
| sort_by | string | Champ de tri (id, created_at, total_commande, status) |
| sort_direction | string | Direction de tri (asc, desc) |
| per_page | integer | Nombre d'éléments par page (défaut: 15, max: 100) |
| page | integer | Numéro de page (défaut: 1) |

#### Exemple de Réponse

```json
{
  "status": "success",
  "message": "Commandes récupérées avec succès",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "user_id": 5,
        "adresse_commande": "123 Rue Exemple",
        "ville_commande": "Casablanca",
        "code_postal_commande": "20000",
        "telephone_commande": "+212612345678",
        "email_commande": "<EMAIL>",
        "total_commande": "1500.00",
        "remise_commande": "10.00",
        "status": "confirmee",
        "created_at": "2025-04-15T10:30:00.000000Z",
        "updated_at": "2025-04-15T10:35:00.000000Z",
        "adresse": "123 Rue Exemple",
        "ville": "Casablanca",
        "code_postal": "20000",
        "telephone": "+212612345678",
        "email": "<EMAIL>",
        "total": "1500.00",
        "remise": "10.00",
        "status_label": "Confirmée"
      }
    ],
    "first_page_url": "http://example.com/api/commandes?page=1",
    "from": 1,
    "last_page": 5,
    "last_page_url": "http://example.com/api/commandes?page=5",
    "links": [
      {
        "url": null,
        "label": "&laquo; Précédent",
        "active": false
      },
      {
        "url": "http://example.com/api/commandes?page=1",
        "label": "1",
        "active": true
      },
      {
        "url": "http://example.com/api/commandes?page=2",
        "label": "2",
        "active": false
      },
      {
        "url": "http://example.com/api/commandes?page=2",
        "label": "Suivant &raquo;",
        "active": false
      }
    ],
    "next_page_url": "http://example.com/api/commandes?page=2",
    "path": "http://example.com/api/commandes",
    "per_page": 15,
    "prev_page_url": null,
    "to": 15,
    "total": 75
  }
}
```

### Récupérer une commande spécifique

```
GET /api/commandes/{id}
```

Récupère les détails d'une commande spécifique.

#### Paramètres de Requête

| Paramètre | Type | Description |
|-----------|------|-------------|
| with | string | Relations à inclure (user,produits,paiement) séparées par des virgules |

#### Exemple de Réponse

```json
{
  "status": "success",
  "message": "Commande récupérée avec succès",
  "data": {
    "id": 1,
    "user_id": 5,
    "adresse_commande": "123 Rue Exemple",
    "ville_commande": "Casablanca",
    "code_postal_commande": "20000",
    "telephone_commande": "+212612345678",
    "email_commande": "<EMAIL>",
    "total_commande": "1500.00",
    "remise_commande": "10.00",
    "status": "confirmee",
    "created_at": "2025-04-15T10:30:00.000000Z",
    "updated_at": "2025-04-15T10:35:00.000000Z",
    "adresse": "123 Rue Exemple",
    "ville": "Casablanca",
    "code_postal": "20000",
    "telephone": "+212612345678",
    "email": "<EMAIL>",
    "total": "1500.00",
    "remise": "10.00",
    "status_label": "Confirmée",
    "client_remise": 15,
    "produits": [
      {
        "id": 10,
        "nom_produit": "Produit Exemple",
        "prix_produit": "200.00",
        "description_produit": "Description du produit",
        "pivot": {
          "commande_id": 1,
          "produit_id": 10,
          "quantite": 3,
          "prix_unitaire": "200.00"
        },
        "quantite": 3,
        "prix_unitaire": "200.00",
        "sous_total": 600,
        "nom": "Produit Exemple",
        "prix": "200.00",
        "description": "Description du produit"
      }
    ],
    "paiement": {
      "id": 1,
      "commande_id": 1,
      "montant": "1500.00",
      "methode": "carte",
      "statut": "complete",
      "created_at": "2025-04-15T10:40:00.000000Z",
      "updated_at": "2025-04-15T10:40:00.000000Z"
    }
  }
}
```

### Créer une nouvelle commande

```
POST /api/commandes
```

Crée une nouvelle commande.

#### Corps de la Requête

```json
{
  "user_id": 5,
  "adresse_commande": "123 Rue Exemple",
  "ville_commande": "Casablanca",
  "code_postal_commande": "20000",
  "telephone_commande": "+212612345678",
  "email_commande": "<EMAIL>",
  "remise_commande": 10,
  "produits": [
    {
      "id": 10,
      "quantite": 3,
      "prix_unitaire": 200
    },
    {
      "id": 15,
      "quantite": 1,
      "prix_unitaire": 500
    }
  ]
}
```

### Mettre à jour le statut d'une commande

```
PATCH /api/commandes/{id}/status
```

Met à jour le statut d'une commande existante.

#### Corps de la Requête

```json
{
  "status": "confirmee"
}
```

#### Exemple de Réponse

```json
{
  "status": "success",
  "message": "Statut de la commande mis à jour avec succès",
  "data": {
    "id": 1,
    "user_id": 5,
    "adresse_commande": "123 Rue Exemple",
    "ville_commande": "Casablanca",
    "code_postal_commande": "20000",
    "telephone_commande": "+212612345678",
    "email_commande": "<EMAIL>",
    "total_commande": "1500.00",
    "remise_commande": "10.00",
    "status": "confirmee",
    "created_at": "2025-04-15T10:30:00.000000Z",
    "updated_at": "2025-04-15T10:45:00.000000Z",
    "adresse": "123 Rue Exemple",
    "ville": "Casablanca",
    "code_postal": "20000",
    "telephone": "+212612345678",
    "email": "<EMAIL>",
    "total": "1500.00",
    "remise": "10.00",
    "status_label": "Confirmée"
  }
}
```

### Récupérer la dernière commande d'un client

```
GET /api/clients/{id}/derniere-commande
```

Récupère la dernière commande d'un client spécifique.

#### Exemple de Réponse

```json
{
  "status": "success",
  "message": "Dernière commande récupérée avec succès",
  "data": {
    "id": 1,
    "user_id": 5,
    "adresse_commande": "123 Rue Exemple",
    "ville_commande": "Casablanca",
    "code_postal_commande": "20000",
    "telephone_commande": "+212612345678",
    "email_commande": "<EMAIL>",
    "total_commande": "1500.00",
    "remise_commande": "10.00",
    "status": "confirmee",
    "created_at": "2025-04-15T10:30:00.000000Z",
    "updated_at": "2025-04-15T10:35:00.000000Z",
    "adresse": "123 Rue Exemple",
    "ville": "Casablanca",
    "code_postal": "20000",
    "telephone": "+212612345678",
    "email": "<EMAIL>",
    "total": "1500.00",
    "remise": "10.00",
    "status_label": "Confirmée",
    "client_remise": 15,
    "produits": [
      {
        "id": 10,
        "nom_produit": "Produit Exemple",
        "prix_produit": "200.00",
        "description_produit": "Description du produit",
        "pivot": {
          "commande_id": 1,
          "produit_id": 10,
          "quantite": 3,
          "prix_unitaire": "200.00"
        },
        "quantite": 3,
        "prix_unitaire": "200.00",
        "sous_total": 600,
        "nom": "Produit Exemple",
        "prix": "200.00",
        "description": "Description du produit"
      }
    ]
  }
}
```

### Récupérer toutes les commandes d'un client

```
GET /api/clients/{id}/commandes
```

Récupère toutes les commandes d'un client spécifique.

#### Paramètres de Requête

| Paramètre | Type | Description |
|-----------|------|-------------|
| status | string | Filtrer par statut (en_attente, confirmee, en_preparation, expediee, livree, annulee, remboursee) |
| date_from | date | Filtrer par date de début (format YYYY-MM-DD) |
| date_to | date | Filtrer par date de fin (format YYYY-MM-DD) |
| with | string | Relations à inclure (produits,paiement) séparées par des virgules |
| sort_by | string | Champ de tri (id, created_at, total_commande, status) |
| sort_direction | string | Direction de tri (asc, desc) |
| per_page | integer | Nombre d'éléments par page (défaut: 15, max: 100) |
| page | integer | Numéro de page (défaut: 1) |

#### Exemple de Réponse

Similaire à la réponse de l'endpoint `GET /api/commandes` mais filtré pour un client spécifique.
