# API Documentation: Ordering Workflow

This document outlines the API endpoints involved in the product ordering workflow.

## Base URL

All endpoints are prefixed with `/api`.

## Authentication

Most order-related endpoints require authentication. Ensure a valid JWT token is provided in the `Authorization` header as a Bearer token.

## Endpoints

### 1. Create Order

*   **Endpoint:** `POST /commandes`
*   **Named Route:** `commandes.store`
*   **Description:** Creates a new order from the items in a specified shopping cart.
*   **Permissions:** Authenticated user.
*   **Request Body:**
    *   `cart_id` (string, required): The ID of the shopping cart (`paniers.id`) to convert into an order.
    *   `shipping_address` (object, required): Shipping address details.
        *   `street` (string, required): Street address.
        *   `city` (string, required): City.
        *   `postal_code` (string, required): Postal code.
        *   `country` (string, required): Country.
    *   `billing_address` (object, required): Billing address details. (Similar structure to `shipping_address`)
    *   `methode_paiement` (string, required): The chosen payment method (e.g., "stripe", "paypal_express").
    *   `notes` (string, optional): Additional notes for the order.
    *   `code_promo` (string, optional): A promotional code to apply to the order.
    *   `payment_details` (object, optional): Contains details specific to the payment method, which might be needed upfront by some gateways or for pre-authorization. Structure depends on the payment gateway.
    *   `user_id` (integer, optional): ID of the user placing the order (if admin is creating on behalf of a user).
    *   `client_id` (integer, optional): ID of the client profile (if applicable and different from user).
    *   `guest_details` (object, optional): Required if `user_id` and `client_id` are not provided (for guest checkouts).
        *   `email` (string, required): Guest's email address.
        *   `nom` (string, required): Guest's name.
        *   `telephone` (string, optional): Guest's phone number.
*   **Success Response (201 Created):**
    ```json
    {
        "status": "success",
        "message": "Commande créée avec succès",
        "data": {
            "id": 123,
            "numero_commande": "CMD-ABC123XYZ",
            "status": "en_attente", // or other initial status
            "payment_status": "pending",
            "total_commande": 150.75,
            "shipping_address": { /* ... */ },
            "billing_address": { /* ... */ },
            "methode_paiement": "stripe",
            "notes": "Please deliver in the afternoon.",
            "user_id": 1,
            "client_id": 1,
            "produits": [
                {
                    "id": 10,
                    "nom_produit": "Product Name",
                    "pivot": {
                        "quantite": 2,
                        "prix_unitaire": 50.00,
                        "total_ligne": 100.00
                    }
                    // ... other product details
                }
            ],
            "created_at": "2023-10-27T10:00:00.000000Z",
            "updated_at": "2023-10-27T10:00:00.000000Z"
            // ... other Commande model attributes
        }
    }
    ```
*   **Error Responses:**
    *   `400 Bad Request`: If the cart is empty or other validation issues.
    *   `401 Unauthorized`: If not authenticated.
    *   `404 Not Found`: If `cart_id`, `user_id`, or `client_id` does not exist.
    *   `422 Unprocessable Entity`: Validation errors (e.g., missing required fields).

### 2. Get Order Details

*   **Endpoint:** `GET /commandes/{commande}`
*   **Named Route:** `commandes.show`
*   **Description:** Retrieves the details of a specific order.
*   **Permissions:** Authenticated user (must be their own order or have admin rights).
*   **URL Parameters:**
    *   `commande` (integer, required): The ID of the order.
*   **Query Parameters:**
    *   `with` (string, optional): Comma-separated list of relations to eager load (e.g., `user,produits,paiement,client`).
*   **Success Response (200 OK):**
    ```json
    {
        "status": "success",
        "message": "Commande récupérée avec succès",
        "data": {
            "id": 123,
            "numero_commande": "CMD-ABC123XYZ",
            "status": "en_preparation",
            "payment_status": "paid",
            "total_commande": 150.75,
            // ... all other Commande attributes and requested relations
            "paiement": {
                "id": 45,
                "methode_paiement": "stripe",
                "status": "completed",
                "transaction_id": "pi_abcdef123456",
                // ... other Paiement attributes
            }
        }
    }
    ```
*   **Error Responses:**
    *   `401 Unauthorized`: If not authenticated.
    *   `403 Forbidden`: If trying to access an order not belonging to the user (and not admin).
    *   `404 Not Found`: If the order ID does not exist.

### 3. Process Order Payment

*   **Endpoint:** `POST /commandes/{commande}/pay`
*   **Named Route:** `commandes.processPayment`
*   **Description:** Processes the payment for an order that is typically in an `en_attente` status.
*   **Permissions:** Authenticated user (must be their own order or have admin rights).
*   **URL Parameters:**
    *   `commande` (integer, required): The ID of the order.
*   **Request Body:**
    *   `payment_details` (object, required): Contains information required by the payment gateway. The structure is gateway-dependent. For example, for Stripe, it might include a `payment_method_id`.
        *   Example (Stripe): `{"payment_method_id": "pm_1L..."}`
        *   Example (Simulation): `{"simulate": "success"}` or `{"simulate": "failure", "simulate_fail_reason": "Insufficient funds"}`
*   **Success Response (200 OK):**
    ```json
    {
        "status": "success",
        "message": "Paiement traité avec succès",
        "data": {
            "commande": {
                "id": 123,
                "status": "confirmee", // Or other status post-payment
                "payment_status": "paid",
                // ... other Commande attributes
            },
            "paiement": {
                "id": 46,
                "commande_id": 123,
                "montant": 150.75,
                "methode_paiement": "stripe",
                "status": "completed",
                "transaction_id": "pi_xyz789012",
                "gateway_response": { /* Gateway specific response */ },
                "processed_at": "2023-10-27T10:05:00.000000Z"
                // ... other Paiement attributes
            }
        }
    }
    ```
*   **Error Responses:**
    *   `401 Unauthorized`.
    *   `403 Forbidden`.
    *   `404 Not Found`: If the order ID does not exist.
    *   `422 Unprocessable Entity`: If payment cannot be processed (e.g., order already paid, order not in a payable state, validation errors for `payment_details`).
    *   `500 Internal Server Error`: If the payment gateway interaction fails.

### 4. Update Order Status

*   **Endpoint:** `PATCH /commandes/{commande}/status`
*   **Named Route:** `commandes.updateStatus`
*   **Description:** Updates the status of an order. Typically used by admins or automated processes.
*   **Permissions:** Admin users.
*   **URL Parameters:**
    *   `commande` (integer, required): The ID of the order.
*   **Request Body:**
    *   `status` (string, required): The new status for the order. Must be a valid `CommandeStatus` enum value (e.g., `en_preparation`, `expediee`, `livree`, `annulee`).
    *   `notes` (string, optional): Reason or notes for the status change.
    *   `send_notification` (boolean, optional, default: `true`): Whether to send a notification to the customer about the status update.
*   **Success Response (200 OK):**
    ```json
    {
        "status": "success",
        "message": "Statut de la commande mis à jour avec succès",
        "data": {
            "id": 123,
            "status": "expediee", // New status
            // ... other Commande attributes
        }
    }
    ```
*   **Error Responses:**
    *   `401 Unauthorized`.
    *   `403 Forbidden`.
    *   `404 Not Found`: If the order ID does not exist.
    *   `422 Unprocessable Entity`: If the status transition is invalid or `status` field is missing/invalid.

### 5. Cancel Order

*   **Endpoint:** `POST /commandes/{commande}/cancel`
*   **Named Route:** `commandes.cancelOrder`
*   **Description:** Cancels an order. Depending on the order's current status and payment status, this might trigger stock restoration and/or a payment refund.
*   **Permissions:** Authenticated user (their own order, if cancellable) or Admin users.
*   **URL Parameters:**
    *   `commande` (integer, required): The ID of the order.
*   **Request Body:**
    *   `reason` (string, optional): The reason for cancellation.
*   **Success Response (200 OK):**
    ```json
    {
        "status": "success",
        "message": "Commande annulée avec succès",
        "data": {
            "id": 123,
            "status": "annulee", // New status
            "payment_status": "refunded", // If applicable
            // ... other Commande attributes
        }
    }
    ```
*   **Error Responses:**
    *   `401 Unauthorized`.
    *   `403 Forbidden`.
    *   `404 Not Found`: If the order ID does not exist.
    *   `422 Unprocessable Entity`: If the order cannot be cancelled in its current state.

### 6. List Orders

*   **Endpoint:** `GET /commandes`
*   **Named Route:** `commandes.index`
*   **Description:** Retrieves a paginated list of orders. Admins can see all orders; regular users see only their own.
*   **Permissions:** Authenticated user.
*   **Query Parameters:**
    *   `page` (integer, optional): Page number for pagination.
    *   `per_page` (integer, optional, default: 15, max: 100): Number of items per page.
    *   `user_id` (integer, optional, admin-only): Filter orders by a specific user ID.
    *   `status` (string, optional): Filter by order status (e.g., `en_attente`).
    *   `payment_status` (string, optional): Filter by payment status (e.g., `paid`).
    *   `date_from` (date, optional, format: YYYY-MM-DD): Filter orders created on or after this date.
    *   `date_to` (date, optional, format: YYYY-MM-DD): Filter orders created on or before this date.
    *   `sort_by` (string, optional, default: `created_at`): Field to sort by (e.g., `id`, `total_commande`, `status`).
    *   `sort_direction` (string, optional, default: `desc`): Sort direction (`asc` or `desc`).
    *   `with` (string, optional): Comma-separated list of relations to eager load.
*   **Success Response (200 OK):**
    ```json
    {
        "status": "success",
        "message": "Commandes récupérées avec succès",
        "data": {
            "current_page": 1,
            "data": [
                {
                    "id": 123,
                    "numero_commande": "CMD-ABC123XYZ",
                    "status": "livree",
                    // ... other Commande attributes
                },
                {
                    "id": 124,
                    "numero_commande": "CMD-DEF456UVW",
                    "status": "en_preparation",
                    // ... other Commande attributes
                }
            ],
            "first_page_url": "/api/commandes?page=1",
            "from": 1,
            "last_page": 5,
            "last_page_url": "/api/commandes?page=5",
            // ... other pagination fields
            "to": 15,
            "total": 70
        }
    }
    ```
*   **Error Responses:**
    *   `401 Unauthorized`.
    *   `500 Internal Server Error`.

### 7. Delete Order

*   **Endpoint:** `DELETE /commandes/{commande}`
*   **Named Route:** `commandes.destroy`
*   **Description:** Deletes an order. This is likely a soft delete.
*   **Permissions:** Admin users.
*   **URL Parameters:**
    *   `commande` (integer, required): The ID of the order to delete.
*   **Success Response (200 OK or 204 No Content):**
    ```json
    {
        "status": "success",
        "message": "Commande supprimée avec succès"
    }
    ```
*   **Error Responses:**
    *   `401 Unauthorized`.
    *   `403 Forbidden`.
    *   `404 Not Found`: If the order ID does not exist.

## Core Data Models Involved

### Commande Model Attributes (Illustrative)

*   `id` (integer, primary key)
*   `user_id` (integer, nullable, foreign key to `users`)
*   `client_id` (integer, nullable, foreign key to `clients`)
*   `numero_commande` (string, unique)
*   `status` (enum: `en_attente`, `confirmee`, `en_preparation`, `expediee`, `livree`, `annulee`, `remboursee`, `retournee`)
*   `payment_status` (string: `pending`, `paid`, `failed`, `refunded`, `cancelled`)
*   `total_commande` (decimal)
*   `subtotal` (decimal)
*   `tax_amount` (decimal)
*   `shipping_cost` (decimal)
*   `discount_amount` (decimal)
*   `shipping_address` (JSON): `{ "street": "", "city": "", "postal_code": "", "country": "" }`
*   `billing_address` (JSON): (Similar to `shipping_address`)
*   `methode_paiement` (string)
*   `notes` (text, nullable)
*   `code_promo` (string, nullable)
*   `date_commande` (datetime)
*   `confirmed_at` (datetime, nullable)
*   `preparation_started_at` (datetime, nullable)
*   `shipped_at` (datetime, nullable)
*   `delivered_at` (datetime, nullable)
*   `cancelled_at` (datetime, nullable)
*   `refunded_at` (datetime, nullable)
*   `created_at` (timestamp)
*   `updated_at` (timestamp)

**Relations:** `user`, `client`, `produits` (many-to-many through `commande_produit`), `paiement` (one-to-one or one-to-many).

### LigneCommande (Pivot: `commande_produit`) Attributes

*   `commande_id` (integer, foreign key)
*   `produit_id` (integer, foreign key)
*   `quantite` (integer)
*   `prix_unitaire` (decimal): Price of the product at the time of order.
*   `total_ligne` (decimal): `quantite * prix_unitaire`.
*   `created_at` (timestamp)
*   `updated_at` (timestamp)

### Panier Model Attributes (Illustrative)

*   `id` (string/UUID, primary key - was `guest_id`, now primary for cart identification)
*   `client_id` (integer, nullable, foreign key to `users` or `clients`): Associates cart with a logged-in user.
*   `guest_id` (uuid, nullable, unique): Identifier for guest carts (may be deprecated if `id` serves this purpose).
*   `created_at` (timestamp)
*   `updated_at` (timestamp)

**Relations:** `items` (one-to-many to `PanierItem`), `client`.

### PanierItem Model Attributes (Illustrative)

*   `id` (integer, primary key)
*   `panier_id` (string/UUID, foreign key to `paniers`)
*   `produit_id` (integer, foreign key to `produits`)
*   `variante_id` (integer, nullable, foreign key to `produit_variantes`)
*   `quantite` (integer)
*   `prix_unitaire` (decimal): Price at the time of adding to cart.
*   `created_at` (timestamp)
*   `updated_at` (timestamp)

**Relations:** `panier`, `produit`, `variante`.

### Paiement Model Attributes (Illustrative)

*   `id` (integer, primary key)
*   `commande_id` (integer, foreign key to `commandes`)
*   `montant` (decimal)
*   `methode_paiement` (string)
*   `status` (enum: `pending`, `completed`, `failed`, `refunded`, `cancelled`)
*   `transaction_id` (string, nullable): Gateway transaction ID.
*   `gateway_response` (JSON, nullable): Raw response or details from the payment gateway.
*   `processed_at` (datetime, nullable)
*   `refunded_at` (datetime, nullable)
*   `created_at` (timestamp)
*   `updated_at` (timestamp)

**Relations:** `commande`.

## Status Enums

### CommandeStatus

*   `en_attente`: Order placed, awaiting payment or confirmation.
*   `confirmee`: Order confirmed (e.g., payment successful or manual confirmation).
*   `en_preparation`: Order is being prepared for shipment.
*   `expediee`: Order has been shipped.
*   `livree`: Order has been delivered.
*   `annulee`: Order has been cancelled.
*   `remboursee`: Order has been refunded.
*   `retournee`: Order has been returned by the customer.

### PaymentStatus (on Commande model)

*   `pending`: Payment is pending or not yet attempted.
*   `paid`: Payment has been successfully completed.
*   `failed`: Payment attempt failed.
*   `refunded`: Payment has been refunded.
*   `cancelled`: Payment was cancelled (e.g. if order is cancelled before payment).

### PaiementStatus (on Paiement model)

*   `pending`: Payment initiated but not yet confirmed by gateway.
*   `completed`: Payment successfully processed by gateway.
*   `failed`: Payment failed at the gateway.
*   `refunded`: Payment has been refunded via the gateway.
*   `cancelled`: Payment was cancelled.
