<?php

namespace App\Services;

use App\Models\Produit;
use App\Models\Categorie;
use App\Models\Marque;
use App\Models\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ProductCacheService
{
    /**
     * Cache duration in minutes
     */
    const CACHE_DURATION = 60; // 1 hour
    const FEATURED_PRODUCTS_CACHE_DURATION = 30; // 30 minutes
    const CATEGORIES_CACHE_DURATION = 120; // 2 hours

    /**
     * Get featured products with caching
     */
    public function getFeaturedProducts(int $limit = 8): array
    {
        return Cache::remember('featured_products_' . $limit, self::FEATURED_PRODUCTS_CACHE_DURATION, function () use ($limit) {
            Log::info('Caching featured products', ['limit' => $limit]);

            return Produit::with([
                'marque:id,nom_marque',
                'images' => function ($query) {
                    $query->select('id', 'imageable_id', 'imageable_type', 'path', 'order')
                        ->orderBy('order')
                        ->limit(1);
                },
                'promotions' => function ($query) {
                    $now = now();
                    $query->where('statut', 'active')
                        ->where(function ($q) use ($now) {
                            $q->whereNull('date_debut')->orWhere('date_debut', '<=', $now);
                        })
                        ->where(function ($q) use ($now) {
                            $q->whereNull('date_fin')->orWhere('date_fin', '>=', $now);
                        });
                }
            ])
                ->where('featured', true)
                ->where('quantite_produit', '>', 0)
                ->orderBy('created_at', 'desc')
                ->limit($limit)
                ->get()
                ->toArray();
        });
    }

    /**
     * Get categories with product counts
     */
    public function getCategoriesWithCounts(): array
    {
        return Cache::remember('categories_with_counts', self::CATEGORIES_CACHE_DURATION, function () {
            Log::info('Caching categories with product counts');

            return Categorie::withCount([
                'produits' => function ($query) {
                    $query->where('quantite_produit', '>', 0);
                }
            ])
                ->where('featured', true)
                ->orderBy('nom')
                ->get()
                ->toArray();
        });
    }

    /**
     * Get brands with product counts
     */
    public function getBrandsWithCounts(): array
    {
        return Cache::remember('brands_with_counts', self::CATEGORIES_CACHE_DURATION, function () {
            Log::info('Caching brands with product counts');

            return Marque::withCount([
                'produits' => function ($query) {
                    $query->where('quantite_produit', '>', 0);
                }
            ])
                ->having('produits_count', '>', 0)
                ->orderBy('nom_marque')
                ->get()
                ->toArray();
        });
    }

    /**
     * Get collections with product counts
     */
    public function getCollectionsWithCounts(): array
    {
        return Cache::remember('collections_with_counts', self::CATEGORIES_CACHE_DURATION, function () {
            Log::info('Caching collections with product counts');

            return Collection::withCount([
                'produits' => function ($query) {
                    $query->where('quantite_produit', '>', 0);
                }
            ])
                ->having('produits_count', '>', 0)
                ->orderBy('nom')
                ->get()
                ->toArray();
        });
    }

    /**
     * Get product by ID with caching
     */
    public function getProductById(int $id): ?array
    {
        return Cache::remember('product_' . $id, self::CACHE_DURATION, function () use ($id) {
            Log::info('Caching product by ID', ['product_id' => $id]);

            $product = Produit::with([
                'marque:id,nom_marque',
                'sousSousCategorie:id,nom_sous_sous_categorie,sous_categorie_id',
                'sousSousCategorie.sousCategorie:id,nom_sous_categorie,categorie_id',
                'sousSousCategorie.sousCategorie.categorie:id,nom_categorie',
                'collections:id,nom',
                'images' => function ($query) {
                    $query->select('id', 'imageable_id', 'imageable_type', 'path', 'order')
                        ->orderBy('order');
                },
                'variantes:id,produit_parent_id,sku,prix_supplement,stock,actif',
                'caracteristiques.attribut:id,nom,type',
                'promotions' => function ($query) {
                    $now = now();
                    $query->where('statut', 'active')
                        ->where(function ($q) use ($now) {
                            $q->whereNull('date_debut')->orWhere('date_debut', '<=', $now);
                        })
                        ->where(function ($q) use ($now) {
                            $q->whereNull('date_fin')->orWhere('date_fin', '>=', $now);
                        });
                }
            ])->find($id);

            return $product ? $product->toArray() : null;
        });
    }

    /**
     * Get related products by category
     */
    public function getRelatedProducts(int $productId, int $categoryId, int $limit = 4): array
    {
        return Cache::remember('related_products_' . $productId . '_' . $categoryId . '_' . $limit, self::CACHE_DURATION, function () use ($productId, $categoryId, $limit) {
            Log::info('Caching related products', ['product_id' => $productId, 'category_id' => $categoryId, 'limit' => $limit]);

            return Produit::with([
                'marque:id,nom_marque',
                'images' => function ($query) {
                    $query->select('id', 'imageable_id', 'imageable_type', 'path', 'order')
                        ->orderBy('order')
                        ->limit(1);
                }
            ])
                ->whereHas('sousSousCategorie.sousCategorie', function ($query) use ($categoryId) {
                    $query->where('categorie_id', $categoryId);
                })
                ->where('id', '!=', $productId)
                ->where('quantite_produit', '>', 0)
                ->inRandomOrder()
                ->limit($limit)
                ->get()
                ->toArray();
        });
    }

    /**
     * Clear product-related cache
     */
    public function clearProductCache(int $productId): void
    {
        $tags = [
            'product_' . $productId,
            'featured_products_*',
            'categories_with_counts',
            'brands_with_counts',
            'collections_with_counts',
            'related_products_' . $productId . '_*'
        ];

        foreach ($tags as $tag) {
            if (str_contains($tag, '*')) {
                // For wildcard patterns, we need to implement a more sophisticated cache clearing
                // For now, we'll clear the main caches
                Cache::forget('featured_products_8');
                Cache::forget('categories_with_counts');
                Cache::forget('brands_with_counts');
                Cache::forget('collections_with_counts');
            } else {
                Cache::forget($tag);
            }
        }

        Log::info('Cleared product cache', ['product_id' => $productId]);
    }

    /**
     * Clear all product-related caches
     */
    public function clearAllProductCaches(): void
    {
        $cacheKeys = [
            'featured_products_4',
            'featured_products_8',
            'featured_products_12',
            'categories_with_counts',
            'brands_with_counts',
            'collections_with_counts'
        ];

        foreach ($cacheKeys as $key) {
            Cache::forget($key);
        }

        Log::info('Cleared all product caches');
    }

    /**
     * Warm up frequently accessed caches
     */
    public function warmUpCaches(): void
    {
        Log::info('Warming up product caches');

        // Warm up featured products
        $this->getFeaturedProducts(8);

        // Warm up categories
        $this->getCategoriesWithCounts();

        // Warm up brands
        $this->getBrandsWithCounts();

        // Warm up collections
        $this->getCollectionsWithCounts();

        Log::info('Product caches warmed up successfully');
    }
}
