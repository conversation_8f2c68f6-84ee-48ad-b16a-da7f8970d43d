<?php

namespace App\Http\Controllers;

use App\Http\Requests\ContactFormRequest;
use App\Mail\ContactFormMail;
use App\Services\ApiResponseService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class ContactController extends Controller
{
    protected $apiResponse;

    public function __construct(ApiResponseService $apiResponse)
    {
        $this->apiResponse = $apiResponse;
    }

    /**
     * Handle contact form submission
     */
    public function submit(ContactFormRequest $request): JsonResponse
    {
        try {
            // Get validated data
            $contactData = $request->validated();

            // Add IP address and user agent for security tracking
            $contactData['ip_address'] = $request->ip();
            $contactData['user_agent'] = $request->userAgent();
            $contactData['submitted_at'] = now();

            // Log the contact form submission
            Log::info('Contact form submitted', [
                'name' => $contactData['name'],
                'email' => $contactData['email'],
                'ip' => $contactData['ip_address'],
                'message_length' => strlen($contactData['message'])
            ]);

            // Send <NAME_EMAIL>
            Mail::to('<EMAIL>')->send(new ContactFormMail($contactData));

            Log::info('Contact form email sent successfully', [
                'to' => '<EMAIL>',
                'from' => $contactData['email'],
                'name' => $contactData['name']
            ]);

            return $this->apiResponse->success([
                'message' => 'Votre message a été envoyé avec succès. Nous vous répondrons dans les plus brefs délais.',
                'submitted_at' => $contactData['submitted_at']->format('d/m/Y à H:i')
            ], 'Message envoyé avec succès');

        } catch (\Exception $e) {
            Log::error('Contact form submission failed', [
                'error' => $e->getMessage(),
                'email' => $request->email ?? 'unknown',
                'name' => $request->name ?? 'unknown',
                'ip' => $request->ip(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->apiResponse->error(
                'Une erreur est survenue lors de l\'envoi de votre message. Veuillez réessayer plus tard.',
                500
            );
        }
    }

    /**
     * Get contact form information (for frontend)
     */
    public function info(): JsonResponse
    {
        return $this->apiResponse->success([
            'fields' => [
                'name' => [
                    'required' => true,
                    'min_length' => 2,
                    'max_length' => 255,
                    'type' => 'string'
                ],
                'email' => [
                    'required' => true,
                    'max_length' => 255,
                    'type' => 'email'
                ],
                'message' => [
                    'required' => true,
                    'min_length' => 10,
                    'max_length' => 5000,
                    'type' => 'text'
                ]
            ],
            'contact_email' => '<EMAIL>'
        ], 'Informations du formulaire de contact');
    }
}
