<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class StockHistorique extends Model
{
    use HasFactory;

    protected $fillable = [
        'produit_id',
        'quantite_avant',
        'quantite_apres',
        'quantite_modifiee',
        'type_mouvement',
        'reference_mouvement',
        'user_id',
        'commentaire'
    ];

    /**
     * Les types de mouvements de stock possibles
     */
    public const TYPE_ENTREE = 'entree';
    public const TYPE_SORTIE = 'sortie';
    public const TYPE_AJUSTEMENT = 'ajustement';
    public const TYPE_COMMANDE = 'commande';
    public const TYPE_RETOUR = 'retour';

    /**
     * Le produit associé à ce mouvement de stock
     */
    public function produit()
    {
        return $this->belongsTo(Produit::class);
    }

    /**
     * L'utilisateur qui a effectué ce mouvement de stock
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
