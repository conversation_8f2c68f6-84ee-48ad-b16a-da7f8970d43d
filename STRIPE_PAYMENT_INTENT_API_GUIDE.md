# Stripe Payment Intent API Guide

## Overview

This guide covers the implementation of Stripe Payment Intent endpoints for secure payment processing in the Laravel API. The Payment Intent API provides a comprehensive solution for handling payments with strong customer authentication (SCA) compliance.

## Table of Contents

1. [Setup and Configuration](#setup-and-configuration)
2. [API Endpoints](#api-endpoints)
3. [Request/Response Examples](#requestresponse-examples)
4. [Frontend Integration](#frontend-integration)
5. [Error Handling](#error-handling)
6. [Testing](#testing)
7. [Security Best Practices](#security-best-practices)

## Setup and Configuration

### Environment Variables

Ensure your `.env` file contains the Stripe configuration:

```env
STRIPE_KEY=pk_test_51RTgI3D1WIE8Jd79dqtCV1q2iaMc1sAaJL6inuoAFCuvOUZoUePpIEX4Lctc9tAx70KS8Nsl23HuPzse52Y38s5y00chgUqWoN
STRIPE_SECRET=sk_test_51RTgI3D1WIE8Jd79EoP9uLRbPpCvvlU0s3CWV6Et2qgS2SGUf0TwijKdEGiZC8atwI35cTWCRVO1AGfH9YWXkFEI00HvKE5md9
```

### Stripe SDK

The Stripe PHP SDK is installed via Composer:

```bash
composer require stripe/stripe-php
```

## API Endpoints

### Base URL
```
POST /api/stripe/create-payment-intent
POST /api/stripe/confirm-payment
GET  /api/stripe/payment-intent/{paymentIntentId}
```

## Request/Response Examples

### 1. Create Payment Intent

Creates a new Stripe Payment Intent for processing a payment.

**Endpoint:** `POST /api/stripe/create-payment-intent`

**Request Headers:**
```
Content-Type: application/json
Accept: application/json
```

**Request Body:**
```json
{
    "amount": 29.99,
    "currency": "eur",
    "commande_id": 123,
    "customer_email": "<EMAIL>",
    "metadata": {
        "order_number": "ORD-001",
        "customer_name": "John Doe"
    }
}
```

**Response (Success - 201):**
```json
{
    "success": true,
    "client_secret": "pi_1234567890_secret_abcdefghijk",
    "payment_intent_id": "pi_1234567890",
    "amount": 29.99,
    "currency": "eur",
    "paiement_id": 456
}
```

**Response (Error - 400):**
```json
{
    "error": "Validation failed",
    "details": {
        "amount": ["The amount field is required."],
        "commande_id": ["The commande id field is required."]
    }
}
```

### 2. Confirm Payment

Confirms a Payment Intent with a payment method.

**Endpoint:** `POST /api/stripe/confirm-payment`

**Request Body:**
```json
{
    "payment_intent_id": "pi_1234567890",
    "payment_method_id": "pm_1234567890"
}
```

**Response (Success - 200):**
```json
{
    "success": true,
    "status": "succeeded",
    "payment_intent": {
        "id": "pi_1234567890",
        "status": "succeeded",
        "amount": 29.99,
        "currency": "eur"
    },
    "requires_action": false,
    "client_secret": "pi_1234567890_secret_abcdefghijk"
}
```

**Response (Requires Action - 200):**
```json
{
    "success": true,
    "status": "requires_action",
    "payment_intent": {
        "id": "pi_1234567890",
        "status": "requires_action",
        "amount": 29.99,
        "currency": "eur"
    },
    "requires_action": true,
    "client_secret": "pi_1234567890_secret_abcdefghijk"
}
```

### 3. Get Payment Intent Status

Retrieves the current status of a Payment Intent.

**Endpoint:** `GET /api/stripe/payment-intent/{paymentIntentId}`

**Response (Success - 200):**
```json
{
    "success": true,
    "payment_intent": {
        "id": "pi_1234567890",
        "status": "succeeded",
        "amount": 29.99,
        "currency": "eur",
        "created": 1640995200,
        "metadata": {
            "commande_id": "123",
            "customer_email": "<EMAIL>"
        }
    },
    "local_payment": {
        "id": 456,
        "status": "completed",
        "processed_at": "2024-01-01T12:00:00Z",
        "commande_id": 123,
        "commande_status": "paid"
    }
}
```

## Frontend Integration

### JavaScript/Vanilla JS

```javascript
// 1. Create Payment Intent
async function createPaymentIntent(orderData) {
    try {
        const response = await fetch('/api/stripe/create-payment-intent', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                amount: orderData.total,
                currency: 'eur',
                commande_id: orderData.commandeId,
                customer_email: orderData.email
            })
        });

        const data = await response.json();
        
        if (data.success) {
            return {
                clientSecret: data.client_secret,
                paymentIntentId: data.payment_intent_id
            };
        } else {
            throw new Error(data.error || 'Failed to create payment intent');
        }
    } catch (error) {
        console.error('Error creating payment intent:', error);
        throw error;
    }
}

// 2. Initialize Stripe Elements
function initializeStripeElements(clientSecret) {
    const stripe = Stripe('pk_test_51RTgI3D1WIE8Jd79dqtCV1q2iaMc1sAaJL6inuoAFCuvOUZoUePpIEX4Lctc9tAx70KS8Nsl23HuPzse52Y38s5y00chgUqWoN');
    
    const elements = stripe.elements({
        clientSecret: clientSecret
    });

    const paymentElement = elements.create('payment');
    paymentElement.mount('#payment-element');

    return { stripe, elements };
}

// 3. Handle Payment Submission
async function handlePaymentSubmission(stripe, elements) {
    const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
            return_url: window.location.origin + '/payment-success'
        },
        redirect: 'if_required'
    });

    if (error) {
        console.error('Payment failed:', error);
        return { success: false, error: error.message };
    }

    return { success: true, paymentIntent };
}

// 4. Complete Payment Flow
async function processPayment(orderData) {
    try {
        // Step 1: Create Payment Intent
        const { clientSecret, paymentIntentId } = await createPaymentIntent(orderData);
        
        // Step 2: Initialize Stripe Elements
        const { stripe, elements } = initializeStripeElements(clientSecret);
        
        // Step 3: Handle form submission
        document.getElementById('payment-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const result = await handlePaymentSubmission(stripe, elements);
            
            if (result.success) {
                // Payment succeeded, redirect to success page
                window.location.href = '/payment-success?payment_intent=' + paymentIntentId;
            } else {
                // Show error message to user
                document.getElementById('error-message').textContent = result.error;
            }
        });
        
    } catch (error) {
        console.error('Payment process error:', error);
        document.getElementById('error-message').textContent = 'Payment initialization failed';
    }
}
```

### React Integration

```jsx
import { useStripe, useElements, PaymentElement } from '@stripe/react-stripe-js';
import { useState, useEffect } from 'react';

const CheckoutForm = ({ orderData }) => {
    const stripe = useStripe();
    const elements = useElements();
    const [clientSecret, setClientSecret] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [errorMessage, setErrorMessage] = useState('');

    useEffect(() => {
        // Create Payment Intent when component mounts
        const createPaymentIntent = async () => {
            try {
                const response = await fetch('/api/stripe/create-payment-intent', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        amount: orderData.total,
                        currency: 'eur',
                        commande_id: orderData.commandeId,
                        customer_email: orderData.email
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    setClientSecret(data.client_secret);
                } else {
                    setErrorMessage(data.error || 'Failed to initialize payment');
                }
            } catch (error) {
                setErrorMessage('Network error occurred');
            }
        };

        createPaymentIntent();
    }, [orderData]);

    const handleSubmit = async (event) => {
        event.preventDefault();
        setIsLoading(true);
        setErrorMessage('');

        if (!stripe || !elements) {
            return;
        }

        const { error, paymentIntent } = await stripe.confirmPayment({
            elements,
            confirmParams: {
                return_url: `${window.location.origin}/payment-success`,
            },
            redirect: 'if_required'
        });

        if (error) {
            setErrorMessage(error.message);
        } else if (paymentIntent && paymentIntent.status === 'succeeded') {
            // Payment succeeded
            window.location.href = `/payment-success?payment_intent=${paymentIntent.id}`;
        }

        setIsLoading(false);
    };

    if (!clientSecret) {
        return <div>Loading payment form...</div>;
    }

    return (
        <form onSubmit={handleSubmit}>
            <PaymentElement />
            {errorMessage && (
                <div className="error-message">{errorMessage}</div>
            )}
            <button 
                type="submit" 
                disabled={!stripe || isLoading}
                className="payment-button"
            >
                {isLoading ? 'Processing...' : `Pay €${orderData.total}`}
            </button>
        </form>
    );
};

export default CheckoutForm;
```

### Vue.js Integration

```vue
<template>
  <div class="payment-form">
    <div v-if="!clientSecret" class="loading">
      Loading payment form...
    </div>
    
    <form v-else @submit.prevent="handleSubmit">
      <div id="payment-element"></div>
      
      <div v-if="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>
      
      <button 
        type="submit" 
        :disabled="!stripe || isLoading"
        class="payment-button"
      >
        {{ isLoading ? 'Processing...' : `Pay €${orderData.total}` }}
      </button>
    </form>
  </div>
</template>

<script>
import { loadStripe } from '@stripe/stripe-js';

export default {
  name: 'PaymentForm',
  props: {
    orderData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      stripe: null,
      elements: null,
      clientSecret: '',
      isLoading: false,
      errorMessage: ''
    };
  },
  async mounted() {
    await this.initializePayment();
  },
  methods: {
    async initializePayment() {
      try {
        // Load Stripe
        this.stripe = await loadStripe('pk_test_51RTgI3D1WIE8Jd79dqtCV1q2iaMc1sAaJL6inuoAFCuvOUZoUePpIEX4Lctc9tAx70KS8Nsl23HuPzse52Y38s5y00chgUqWoN');
        
        // Create Payment Intent
        const response = await fetch('/api/stripe/create-payment-intent', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            amount: this.orderData.total,
            currency: 'eur',
            commande_id: this.orderData.commandeId,
            customer_email: this.orderData.email
          })
        });

        const data = await response.json();
        
        if (data.success) {
          this.clientSecret = data.client_secret;
          
          // Initialize Elements
          this.elements = this.stripe.elements({
            clientSecret: this.clientSecret
          });
          
          const paymentElement = this.elements.create('payment');
          paymentElement.mount('#payment-element');
        } else {
          this.errorMessage = data.error || 'Failed to initialize payment';
        }
      } catch (error) {
        this.errorMessage = 'Network error occurred';
      }
    },
    
    async handleSubmit() {
      this.isLoading = true;
      this.errorMessage = '';

      const { error, paymentIntent } = await this.stripe.confirmPayment({
        elements: this.elements,
        confirmParams: {
          return_url: `${window.location.origin}/payment-success`,
        },
        redirect: 'if_required'
      });

      if (error) {
        this.errorMessage = error.message;
      } else if (paymentIntent && paymentIntent.status === 'succeeded') {
        this.$router.push(`/payment-success?payment_intent=${paymentIntent.id}`);
      }

      this.isLoading = false;
    }
  }
};
</script>

<style scoped>
.payment-form {
  max-width: 500px;
  margin: 0 auto;
  padding: 20px;
}

.payment-button {
  width: 100%;
  padding: 12px;
  background-color: #007cba;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  margin-top: 20px;
  cursor: pointer;
}

.payment-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.error-message {
  color: #dc3545;
  margin-top: 10px;
  padding: 10px;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
}

.loading {
  text-align: center;
  padding: 20px;
}
</style>
```

## Error Handling

### Common Error Responses

| Error Code | Description | Resolution |
|------------|-------------|------------|
| 400 | Validation failed | Check request parameters |
| 400 | Stripe API error | Review payment details |
| 404 | Payment Intent not found | Verify payment_intent_id |
| 500 | Internal server error | Check server logs |

### Error Response Format

```json
{
    "error": "Error type",
    "message": "Detailed error message",
    "details": {
        "field": ["Validation error details"]
    }
}
```

## Testing

### Test Script

Create a test file `test_stripe_payments.php`:

```php
<?php

$baseUrl = 'http://localhost:8000/api';

// Test data
$testOrder = [
    'amount' => 25.50,
    'currency' => 'eur',
    'commande_id' => 1, // Make sure this order exists
    'customer_email' => '<EMAIL>',
    'metadata' => [
        'test' => true,
        'order_number' => 'TEST-001'
    ]
];

// 1. Test Create Payment Intent
echo "Testing Create Payment Intent...\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, "$baseUrl/stripe/create-payment-intent");
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testOrder));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "Response: $response\n\n";

$data = json_decode($response, true);

if ($data && isset($data['payment_intent_id'])) {
    $paymentIntentId = $data['payment_intent_id'];
    
    // 2. Test Get Payment Intent
    echo "Testing Get Payment Intent...\n";
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "$baseUrl/stripe/payment-intent/$paymentIntentId");
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "HTTP Code: $httpCode\n";
    echo "Response: $response\n\n";
}

echo "Testing completed!\n";
```

### Running Tests

```bash
php test_stripe_payments.php
```

## Security Best Practices

### 1. Environment Security
- Never expose secret keys in frontend code
- Use environment variables for all sensitive data
- Rotate API keys regularly

### 2. Validation
- Always validate amounts on the server side
- Verify order ownership before creating payment intents
- Implement rate limiting for payment endpoints

### 3. Webhook Security
- Verify webhook signatures
- Handle idempotency for webhook events
- Log all payment events for audit trails

### 4. Error Handling
- Don't expose sensitive error details to clients
- Log detailed errors for debugging
- Implement proper error monitoring

## Webhook Integration (Optional)

For production environments, implement Stripe webhooks to handle payment status updates:

```php
// In routes/api.php
Route::post('stripe/webhook', [PaiementController::class, 'handleWebhook']);

// In PaiementController.php
public function handleWebhook(Request $request)
{
    $payload = $request->getContent();
    $sigHeader = $request->header('Stripe-Signature');
    $endpointSecret = config('services.stripe.webhook_secret');

    try {
        $event = \Stripe\Webhook::constructEvent(
            $payload, $sigHeader, $endpointSecret
        );

        switch ($event['type']) {
            case 'payment_intent.succeeded':
                $this->handlePaymentSucceeded($event['data']['object']);
                break;
            case 'payment_intent.payment_failed':
                $this->handlePaymentFailed($event['data']['object']);
                break;
        }

        return response()->json(['status' => 'success']);
    } catch (\Exception $e) {
        Log::error('Webhook error: ' . $e->getMessage());
        return response()->json(['error' => 'Webhook handling failed'], 400);
    }
}
```

## Production Deployment

### 1. Update Environment Variables
```env
STRIPE_KEY=pk_live_your_live_publishable_key
STRIPE_SECRET=sk_live_your_live_secret_key
```

### 2. Enable HTTPS
Ensure all payment processing happens over HTTPS in production.

### 3. Database Backups
Implement regular backups of payment data.

### 4. Monitoring
Set up monitoring for payment failures and errors.

---

This completes the Stripe Payment Intent integration guide. The implementation provides a secure, scalable payment processing solution with comprehensive error handling and frontend integration examples.
