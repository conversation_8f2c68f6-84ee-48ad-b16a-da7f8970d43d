<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Facades\Storage;

class Image extends Model
{
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'path',
        'filename',
        'disk',
        'mime_type',
        'size',
        'alt_text',
        'title',
        'is_primary',
        'order',
        'metadata',
        'imageable_type',
        'imageable_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_primary' => 'boolean',
        'order' => 'integer',
        'size' => 'integer',
        'metadata' => 'array',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<int, string>
     */
    protected $appends = [
        'url',
        'direct_url',
        'thumbnail_small',
        'thumbnail_medium',
        'thumbnail_large',
    ];

    /**
     * Get the parent imageable model.
     */
    public function imageable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the full URL to the image.
     *
     * @return string
     */
    public function getUrlAttribute(): string
    {
        // Utiliser le proxy d'images
        return url("/api/images/serve/{$this->id}");
    }

    /**
     * Get the direct URL to the image (without proxy).
     *
     * @return string
     */
    public function getDirectUrlAttribute(): string
    {
        return url("/api/images/file/{$this->path}");
    }

    /**
     * Get the thumbnail URL for the image.
     *
     * @param string $size
     * @return string
     */
    public function getThumbnailUrl(string $size = 'small'): string
    {
        // Utiliser le proxy d'images pour les miniatures
        return url("/api/images/thumbnail/{$this->id}/{$size}");
    }

    /**
     * Get the small thumbnail URL attribute.
     *
     * @return string
     */
    public function getThumbnailSmallAttribute(): string
    {
        return $this->getThumbnailUrl('small');
    }

    /**
     * Get the medium thumbnail URL attribute.
     *
     * @return string
     */
    public function getThumbnailMediumAttribute(): string
    {
        return $this->getThumbnailUrl('medium');
    }

    /**
     * Get the large thumbnail URL attribute.
     *
     * @return string
     */
    public function getThumbnailLargeAttribute(): string
    {
        return $this->getThumbnailUrl('large');
    }

    /**
     * Delete the image file from storage when the model is deleted.
     *
     * @return void
     */
    protected static function boot()
    {
        parent::boot();

        static::deleting(function (Image $image) {
            if ($image->isForceDeleting()) {
                Storage::disk($image->disk)->delete($image->path);

                // Delete thumbnails if they exist
                $path = pathinfo($image->path);
                $sizes = ['small', 'medium', 'large'];

                foreach ($sizes as $size) {
                    $thumbnailPath = $path['dirname'] . '/' . $path['filename'] . "_{$size}." . $path['extension'];
                    if (Storage::disk($image->disk)->exists($thumbnailPath)) {
                        Storage::disk($image->disk)->delete($thumbnailPath);
                    }
                }
            }
        });
    }
}
