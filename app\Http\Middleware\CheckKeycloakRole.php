<?php

namespace App\Http\Middleware;

use Closure;
use Exception;
use Illuminate\Http\Request;

class CheckKeycloakRole
{
    /**
     * Handle an incoming request based on user roles.
     *
     * @param Request $request
     * @param Closure $next
     * @param  string|array  $roles  Comma separated list of roles
     * @return mixed
     */
    public function handle(Request $request, Closure $next, ...$roles): mixed
    {
        if (!$request->cookie('access_token')) {
            return response()->json(['error' => 'Unauthenticated'], 401);
        }

        // Decode the access token
        $token = $request->cookie('access_token');
        $tokenParts = explode('.', $token);

        if (count($tokenParts) !== 3) {
            return response()->json(['error' => 'Invalid token format'], 401);
        }

        try {
            $tokenPayload = json_decode(base64_decode($tokenParts[1]), true);
            $userRoles = $tokenPayload['realm_access']['roles'] ?? [];

            $authorized = false;
            $missingRoles = [];

            foreach ($roles as $role) {
                if (in_array($role, $userRoles)) {
                    $authorized = true;
                    break;
                }
                $missingRoles[] = $role;
            }

            if (!$authorized) {
                return response()->json([
                    'error' => 'Forbidden: insufficient permissions',
                    'required_roles' => $roles,
                    'user_roles' => $userRoles,
                    'missing_roles' => $missingRoles
                ], 403);
            }

            // Add decoded token data to the request for controllers
            $request->attributes->add(['token_payload' => $tokenPayload]);

            return $next($request);
        } catch (Exception $e) {
            report($e);
            return response()->json([
                'error' => 'Failed to process authentication token',
                'details' => $e->getMessage()
            ], 500);
        }
    }
}
