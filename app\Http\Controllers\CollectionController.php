<?php

namespace App\Http\Controllers;

use App\Models\Collection;
use App\Models\Produit;
use App\Models\Promotion;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class CollectionController extends Controller
{
    /**
     * Afficher la liste des collections
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $query = Collection::query();

            // Filtrer par statut actif si demandé
            if ($request->has('active') && $request->active === 'true') {
                $query->active();
            }

            // Inclure les produits si demandé
            if ($request->has('with_produits') && $request->with_produits === 'true') {
                $query->with('produits');
            }

            $collections = $query->get();

            return response()->json($collections);
        } catch (\Exception $e) {
            return response()->json([
                "error" => "Problème de récupération des collections",
                "message" => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Créer une nouvelle collection
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'nom' => 'required|string|max:255',
                'description' => 'nullable|string',
                'image' => 'nullable|string',
                'active' => 'nullable|boolean',
                'date_debut' => 'nullable|date',
                'date_fin' => 'nullable|date|after_or_equal:date_debut',
                'produits' => 'nullable|array',
                'produits.*.id' => 'required|exists:produits,id',
                'produits.*.ordre' => 'nullable|integer|min:0',
                'produits.*.featured' => 'nullable|boolean'
            ]);

            // Commencer une transaction
            DB::beginTransaction();

            // Créer la collection
            $collection = Collection::create([
                'nom' => $validatedData['nom'],
                'description' => $validatedData['description'] ?? null,
                'image' => $validatedData['image'] ?? null,
                'active' => $validatedData['active'] ?? true,
                'date_debut' => $validatedData['date_debut'] ?? null,
                'date_fin' => $validatedData['date_fin'] ?? null,
            ]);

            // Ajouter les produits si fournis
            if (isset($validatedData['produits']) && is_array($validatedData['produits'])) {
                foreach ($validatedData['produits'] as $produitData) {
                    $collection->produits()->attach($produitData['id'], [
                        'ordre' => $produitData['ordre'] ?? 0,
                        'featured' => $produitData['featured'] ?? false
                    ]);
                }
            }

            // Valider la transaction
            DB::commit();

            // Charger les produits pour la réponse
            $collection->load('produits');

            return response()->json($collection, 201);
        } catch (\Exception $e) {
            // Annuler la transaction en cas d'erreur
            DB::rollBack();

            return response()->json([
                "error" => "Problème lors de la création de la collection",
                "message" => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Afficher une collection spécifique
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $collection = Collection::with('produits')->findOrFail($id);
            return response()->json($collection);
        } catch (\Exception $e) {
            return response()->json([
                "error" => "Problème de récupération de la collection",
                "message" => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mettre à jour une collection existante
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        try {
            $validatedData = $request->validate([
                'nom' => 'sometimes|required|string|max:255',
                'description' => 'nullable|string',
                'image' => 'nullable|string',
                'active' => 'nullable|boolean',
                'date_debut' => 'nullable|date',
                'date_fin' => 'nullable|date|after_or_equal:date_debut',
            ]);

            $collection = Collection::findOrFail($id);
            $collection->update($validatedData);

            return response()->json($collection);
        } catch (\Exception $e) {
            return response()->json([
                "error" => "Problème lors de la mise à jour de la collection",
                "message" => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Supprimer une collection
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            $collection = Collection::findOrFail($id);
            $collection->delete();

            return response()->json([
                "message" => "Collection supprimée avec succès"
            ]);
        } catch (\Exception $e) {
            return response()->json([
                "error" => "Problème lors de la suppression de la collection",
                "message" => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Ajouter des produits à une collection
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function addProduits(Request $request, $id)
    {
        try {
            $validatedData = $request->validate([
                'produits' => 'required|array',
                'produits.*.id' => 'required|exists:produits,id',
                'produits.*.ordre' => 'nullable|integer|min:0',
                'produits.*.featured' => 'nullable|boolean'
            ]);

            // Commencer une transaction
            DB::beginTransaction();

            $collection = Collection::findOrFail($id);

            foreach ($validatedData['produits'] as $produitData) {
                // Vérifier si le produit existe déjà dans la collection
                $exists = $collection->produits()->where('produit_id', $produitData['id'])->exists();

                if (!$exists) {
                    // Ajouter le produit s'il n'existe pas déjà
                    $collection->produits()->attach($produitData['id'], [
                        'ordre' => $produitData['ordre'] ?? 0,
                        'featured' => $produitData['featured'] ?? false
                    ]);
                } else {
                    // Mettre à jour les attributs du pivot si le produit existe déjà
                    $collection->produits()->updateExistingPivot($produitData['id'], [
                        'ordre' => $produitData['ordre'] ?? 0,
                        'featured' => $produitData['featured'] ?? false
                    ]);
                }
            }

            // Valider la transaction
            DB::commit();

            // Charger les produits pour la réponse
            $collection->load('produits');

            return response()->json($collection);
        } catch (\Exception $e) {
            // Annuler la transaction en cas d'erreur
            DB::rollBack();

            return response()->json([
                "error" => "Problème lors de l'ajout des produits à la collection",
                "message" => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Supprimer un produit d'une collection
     *
     * @param int $id
     * @param int $produitId
     * @return \Illuminate\Http\JsonResponse
     */
    public function removeProduit($id, $produitId)
    {
        try {
            $collection = Collection::findOrFail($id);
            $collection->produits()->detach($produitId);

            return response()->json([
                "message" => "Produit retiré de la collection avec succès"
            ]);
        } catch (\Exception $e) {
            return response()->json([
                "error" => "Problème lors du retrait du produit de la collection",
                "message" => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Récupérer les produits d'une collection
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProduits($id)
    {
        try {
            $collection = Collection::findOrFail($id);
            $produits = $collection->produits;

            return response()->json($produits);
        } catch (\Exception $e) {
            return response()->json([
                "error" => "Problème de récupération des produits de la collection",
                "message" => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Récupérer les produits mis en avant d'une collection
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProduitsFeatured($id)
    {
        try {
            $collection = Collection::findOrFail($id);
            $produits = $collection->produitsFeatured;

            return response()->json($produits);
        } catch (\Exception $e) {
            return response()->json([
                "error" => "Problème de récupération des produits mis en avant de la collection",
                "message" => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Associer une promotion à une collection
     *
     * @param Request $request
     * @param int $collection
     * @return \Illuminate\Http\JsonResponse
     */
    public function attachPromotion(Request $request, $collection)
    {
        try {
            $validator = Validator::make($request->all(), [
                'promotion_id' => 'required|exists:promotions,id',
                'date_debut' => 'nullable|date',
                'date_fin' => 'nullable|date|after_or_equal:date_debut',
            ]);

            if ($validator->fails()) {
                return response()->json(['errors' => $validator->errors()], 422);
            }

            $collection = Collection::findOrFail($collection);
            $promotion = Promotion::findOrFail($request->input('promotion_id'));

            // Vérifier si la promotion est déjà associée à la collection
            if ($collection->promotions()->where('promotion_id', $promotion->id)->exists()) {
                return response()->json([
                    'message' => 'Cette promotion est déjà associée à cette collection'
                ], 422);
            }

            // Associer la promotion à la collection avec les dates si fournies
            $pivotData = [
                'date_debut' => $request->input('date_debut'),
                'date_fin' => $request->input('date_fin')
            ];

            $collection->promotions()->attach($promotion->id, $pivotData);

            return response()->json([
                'message' => 'Promotion associée à la collection avec succès',
                'collection' => $collection->load('promotions')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erreur lors de l\'association de la promotion',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Détacher une promotion d'une collection
     *
     * @param int $collection
     * @param int $promotion
     * @return \Illuminate\Http\JsonResponse
     */
    public function detachPromotion($collection, $promotion)
    {
        try {
            $collection = Collection::findOrFail($collection);
            $promotion = Promotion::findOrFail($promotion);

            // Vérifier si la promotion est associée à la collection
            if (!$collection->promotions()->where('promotion_id', $promotion->id)->exists()) {
                return response()->json([
                    'message' => 'Cette promotion n\'est pas associée à cette collection'
                ], 422);
            }

            // Détacher la promotion de la collection
            $collection->promotions()->detach($promotion->id);

            return response()->json([
                'message' => 'Promotion détachée de la collection avec succès',
                'collection' => $collection->load('promotions')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erreur lors du détachement de la promotion',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
