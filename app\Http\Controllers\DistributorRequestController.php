<?php

namespace App\Http\Controllers;

use App\Models\DistributorRequest;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Http\Requests\StoreDistributorRequestRequest;
use App\Http\Requests\EmailDistributorRequest;

class DistributorRequestController extends Controller
{
    /**
     * Display distributor requests
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        if ($user) {
            // For authenticated users, get their requests
            $requests = $user->distributorRequests()->latest()->get();

            return response()->json([
                'status' => 'success',
                'data' => $requests
            ]);
        } else {
            // For unauthenticated users
            if ($request->has('email')) {
                // Validate email using Form Request
                $emailRequest = app(EmailDistributorRequest::class);
                $emailRequest->setContainer(app())->setRedirector(app('redirect'));
                $emailRequest->validateResolved();
                $validated = $emailRequest->validated();
                $requests = DistributorRequest::where('email', $validated['email'])
                    ->latest()
                    ->get();
            } else {
                // If no email is provided, return all requests
                $requests = DistributorRequest::latest()->get();
            }

            return response()->json([
                'status' => 'success',
                'data' => $requests
            ]);
        }
    }

    /**
     * Submit a new distributor request
     */
    public function store(StoreDistributorRequestRequest $request)
    {
        $user = Auth::user();
        $validated = $request->validated();

        // Check for pending request or distributor role if authenticated
        if ($user) {
            $pendingRequest = $user->distributorRequests()->pending()->first();
            if ($pendingRequest) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Vous avez déjà une demande de distributeur en attente'
                ], 400);
            }
            if ($user->hasRole('point_de_vente')) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Vous êtes déjà un distributeur'
                ], 400);
            }
        }

        // Create distributor request
        $distributorRequest = new DistributorRequest($validated);
        if ($user) {
            $distributorRequest->user_id = $user->id;
        } else {
            $distributorRequest->user_id = null;
        }
        $distributorRequest->status = 'pending';
        $distributorRequest->save();

        return response()->json([
            'status' => 'success',
            'message' => 'Demande de distributeur soumise avec succès',
            'data' => $distributorRequest
        ], 201);
    }

    /**
     * Display the specified distributor request
     */
    public function show(Request $request, string $id)
    {
        $distributorRequest = DistributorRequest::find($id);

        if (!$distributorRequest) {
            return response()->json([
                'status' => 'error',
                'message' => 'Demande de distributeur non trouvée'
            ], 404);
        }

        $user = Auth::user();

        // If user is authenticated, check if the request belongs to them
        if ($user && $distributorRequest->user_id) {
            if ($distributorRequest->user_id !== $user->id) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Vous n\'êtes pas autorisé à voir cette demande'
                ], 403);
            }
        } elseif (!$user && $distributorRequest->email) {
            // Validate email using Form Request
            $emailRequest = app(EmailDistributorRequest::class);
            $emailRequest->setContainer(app())->setRedirector(app('redirect'));
            $emailRequest->validateResolved();
            $validated = $emailRequest->validated();
            if ($validated['email'] !== $distributorRequest->email) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Vous n\'êtes pas autorisé à voir cette demande'
                ], 403);
            }
        }

        return response()->json([
            'status' => 'success',
            'data' => $distributorRequest
        ]);
    }

    /**
     * Get the status of the latest distributor request
     */
    public function status(Request $request)
    {
        $user = Auth::user();

        if ($user) {
            $latestRequest = $user->distributorRequests()->latest()->first();
            if (!$latestRequest) {
                return response()->json([
                    'status' => 'success',
                    'data' => [
                        'has_request' => false,
                        'is_distributor' => $user->hasRole('point_de_vente')
                    ]
                ]);
            }
            return response()->json([
                'status' => 'success',
                'data' => [
                    'has_request' => true,
                    'is_distributor' => $user->hasRole('point_de_vente'),
                    'request' => [
                        'id' => $latestRequest->id,
                        'status' => $latestRequest->status,
                        'created_at' => $latestRequest->created_at,
                        'processed_at' => $latestRequest->processed_at
                    ]
                ]
            ]);
        } else {
            // Validate email using Form Request
            $emailRequest = app(EmailDistributorRequest::class);
            $emailRequest->setContainer(app())->setRedirector(app('redirect'));
            $emailRequest->validateResolved();
            $validated = $emailRequest->validated();
            $latestRequest = DistributorRequest::where('email', $validated['email'])
                ->latest()
                ->first();
            if (!$latestRequest) {
                return response()->json([
                    'status' => 'success',
                    'data' => [
                        'has_request' => false,
                        'is_distributor' => false
                    ]
                ]);
            }
            return response()->json([
                'status' => 'success',
                'data' => [
                    'has_request' => true,
                    'is_distributor' => false,
                    'request' => [
                        'id' => $latestRequest->id,
                        'status' => $latestRequest->status,
                        'created_at' => $latestRequest->created_at,
                        'processed_at' => $latestRequest->processed_at
                    ]
                ]
            ]);
        }
    }
}
