<?php

namespace App\Http\Controllers;

use App\Models\PointDeVente;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use App\Http\Requests\StorePointDeVenteRequest;
use App\Http\Requests\UpdatePointDeVenteRequest;
use App\Http\Requests\AddUserToPointDeVenteRequest;

class PointDeVenteController extends Controller
{
    /**
     * Display a listing of all points of sale.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        try {
            $pointsDeVente = PointDeVente::withCount('users')->get();
            return response()->json($pointsDeVente);
        } catch (\Exception $e) {
            return response()->json([
                "error" => "Problème de récupération des points de vente",
                "message" => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created point of sale in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(StorePointDeVenteRequest $request)
    {
        try {
            $validatedData = $request->validated();
            $pointDeVente = PointDeVente::create($validatedData);
            return response()->json($pointDeVente, 201);
        } catch (\Exception $e) {
            return response()->json([
                "error" => "Problème lors de la création du point de vente",
                "message" => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified point of sale.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $pointDeVente = PointDeVente::with('users')->findOrFail($id);
            return response()->json($pointDeVente);
        } catch (\Exception $e) {
            return response()->json([
                "error" => "Problème de récupération du point de vente",
                "message" => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified point of sale in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(UpdatePointDeVenteRequest $request, $id)
    {
        try {
            $validatedData = $request->validated();
            $pointDeVente = PointDeVente::findOrFail($id);
            $pointDeVente->update($validatedData);
            return response()->json($pointDeVente);
        } catch (\Exception $e) {
            return response()->json([
                "error" => "Problème lors de la mise à jour du point de vente",
                "message" => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified point of sale from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            // Start a transaction
            DB::beginTransaction();
            
            $pointDeVente = PointDeVente::findOrFail($id);
            
            // Update all users associated with this point of sale
            User::where('point_de_vente_id', $id)
                ->update([
                    'point_de_vente_id' => null,
                    'type_client' => 'normal'
                ]);
            
            // Delete the point of sale
            $pointDeVente->delete();
            
            // Commit the transaction
            DB::commit();
            
            return response()->json([
                "message" => "Point de vente supprimé avec succès"
            ]);
        } catch (\Exception $e) {
            // Rollback the transaction
            DB::rollBack();
            
            return response()->json([
                "error" => "Problème lors de la suppression du point de vente",
                "message" => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Add a user to a point of sale.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function addUser(AddUserToPointDeVenteRequest $request, $id)
    {
        try {
            $validatedData = $request->validated();
            // Start a transaction
            DB::beginTransaction();
            // Find the point of sale
            $pointDeVente = PointDeVente::findOrFail($id);
            // Find the user
            $user = User::findOrFail($validatedData['user_id']);
            // Update user
            $user->point_de_vente_id = $id;
            $user->type_client = 'point_de_vente';
            // Add client role if not already present
            $roles = $user->roles ?? [];
            if (!in_array('client', $roles)) {
                $roles[] = 'client';
                $user->roles = $roles;
            }
            $user->save();
            // Commit the transaction
            DB::commit();
            return response()->json([
                "message" => "Utilisateur ajouté au point de vente avec succès",
                "user" => $user
            ]);
        } catch (\Exception $e) {
            // Rollback the transaction
            DB::rollBack();
            return response()->json([
                "error" => "Problème lors de l'ajout de l'utilisateur au point de vente",
                "message" => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Remove a user from a point of sale.
     *
     * @param  int  $id
     * @param  int  $userId
     * @return \Illuminate\Http\JsonResponse
     */
    public function removeUser($id, $userId)
    {
        try {
            // Start a transaction
            DB::beginTransaction();
            
            // Find the point of sale
            $pointDeVente = PointDeVente::findOrFail($id);
            
            // Find the user
            $user = User::where('id', $userId)
                ->where('point_de_vente_id', $id)
                ->firstOrFail();
            
            // Update user
            $user->point_de_vente_id = null;
            $user->type_client = 'normal';
            $user->save();
            
            // Commit the transaction
            DB::commit();
            
            return response()->json([
                "message" => "Utilisateur retiré du point de vente avec succès"
            ]);
        } catch (\Exception $e) {
            // Rollback the transaction
            DB::rollBack();
            
            return response()->json([
                "error" => "Problème lors du retrait de l'utilisateur du point de vente",
                "message" => $e->getMessage()
            ], 500);
        }
    }
}
