<?php

namespace App\Http\Controllers;

use App\Models\Panier;
use App\Models\PanierItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Cookie;
use App\Http\Requests\AddCartItemRequest;
use App\Http\Requests\UpdateCartItemRequest;

/**
 * @group Panier
 *
 * API pour gérer le panier d'achat
 */

class PanierController extends Controller
{
    /**
     * Vérifie si l'utilisateur est authentifié
     *
     * @param Request $request
     * @param string $errorMessage Message d'erreur à afficher si l'utilisateur n'est pas authentifié
     * @return \Illuminate\Http\JsonResponse|null Retourne une réponse d'erreur si l'utilisateur n'est pas authentifié, null sinon
     */
    protected function checkAuthentication(Request $request, $errorMessage = 'Vous devez être connecté pour accéder à cette ressource')
    {
        // Essayer d'abord de récupérer le token depuis le cookie
        $token = $request->cookie('access_token');

        // Si pas de token dans le cookie, essayer l'en-tête Authorization
        if (!$token && $request->hasHeader('Authorization')) {
            $authHeader = $request->header('Authorization');
            if (strpos($authHeader, 'Bearer ') === 0) {
                $token = substr($authHeader, 7);
            }
        }

        // Vérifier l'authentification
        if (!Auth::check() && !$token) {
            return response()->json([
                'status' => 'error',
                'message' => $errorMessage
            ], 401);
        }

        return null;
    }
    /**
     * Récupère le panier de l'utilisateur actuel
     */
    protected function getCurrentCart(Request $request)
    {
        // Récupérer l'ID invité du panier depuis l'attribut de requête (défini par le middleware)
        $guestId = $request->attributes->get('cart_guest_id');

        // Récupérer les informations d'authentification depuis les attributs de requête
        $isAuthenticated = $request->attributes->get('is_authenticated', Auth::check());
        $userId = $request->attributes->get('user_id', Auth::id());

        // Vérifier l'authentification avec le token si nécessaire
        if (!$isAuthenticated || !$userId) {
            // Essayer d'authentifier avec le token
            $token = $request->cookie('access_token');

            if (!$token && $request->hasHeader('Authorization')) {
                $authHeader = $request->header('Authorization');
                if (strpos($authHeader, 'Bearer ') === 0) {
                    $token = substr($authHeader, 7);
                }
            }

            if ($token) {
                try {
                    // Valider le token
                    $keycloakService = app(\App\Services\KeycloakService::class);
                    $decoded = $keycloakService->validateToken($token);

                    // Trouver l'utilisateur par Keycloak ID
                    $user = \App\Models\User::where('keycloak_id', $decoded->sub)->first();

                    if ($user) {
                        $userId = $user->id;
                        $isAuthenticated = true;

                        // Mettre à jour les attributs de la requête
                        $request->attributes->set('is_authenticated', true);
                        $request->attributes->set('user_id', $userId);

                        // Connecter l'utilisateur si ce n'est pas déjà fait
                        if (!Auth::check()) {
                            Auth::login($user);
                        }
                    }
                } catch (\Exception $e) {
                    // Échec de validation du token, journaliser l'erreur
                    Log::warning('PanierController::getCurrentCart - Échec de validation du token', [
                        'error' => $e->getMessage()
                    ]);
                }
            }
        }

        // Journaliser les informations de la requête
        Log::debug('PanierController::getCurrentCart - Début', [
            'guest_id' => $guestId,
            'is_authenticated' => $isAuthenticated,
            'user_id' => $userId,
            'auth_check' => Auth::check(),
            'auth_id' => Auth::id(),
            'cookies' => $request->cookies->all(),
            'headers' => $request->headers->all(),
            'path' => $request->path(),
            'method' => $request->method()
        ]);

        $cart = null;

        if ($isAuthenticated && $userId) {
            // Utilisateur connecté - TOUJOURS prioritaire sur la session
            Log::debug('PanierController::getCurrentCart - Utilisateur connecté, recherche du panier par user_id', ['user_id' => $userId]);
            $cart = Panier::where('client_id', $userId)->first();

            if (!$cart && $guestId) {
                Log::debug('PanierController::getCurrentCart - Aucun panier trouvé pour user_id, recherche par guest_id', ['guest_id' => $guestId]);
                // Vérifier s'il existe un panier pour cet invité
                $guestCart = Panier::where('guest_id', $guestId)->first();

                if ($guestCart) {
                    Log::debug('PanierController::getCurrentCart - Panier invité trouvé, association au client', [
                        'guest_cart_id' => $guestCart->id,
                        'user_id' => $userId
                    ]);
                    // Associer le panier invité à l'utilisateur
                    $guestCart->update(['client_id' => $userId, 'guest_id' => null]);
                    $cart = $guestCart;
                }
            }

            // Si aucun panier n'a été trouvé, en créer un nouveau pour l'utilisateur
            if (!$cart) {
                Log::debug('PanierController::getCurrentCart - Aucun panier trouvé, création d\'un nouveau panier pour l\'utilisateur', ['user_id' => $userId]);
                $cart = Panier::create(['client_id' => $userId, 'session_id' => null]);
            } else {
                Log::debug('PanierController::getCurrentCart - Panier existant trouvé pour l\'utilisateur', ['cart_id' => $cart->id]);

                // S'assurer que le panier est bien associé à l'utilisateur (et pas à une session)
                if ($cart->client_id != $userId || $cart->session_id !== null) {
                    $cart->update(['client_id' => $userId, 'session_id' => null]);
                    Log::debug('PanierController::getCurrentCart - Panier mis à jour pour l\'utilisateur', ['cart_id' => $cart->id]);
                }
            }
        } else {
            // Utilisateur non connecté - Utiliser l'ID invité
            if (!$guestId) {
                Log::warning('PanierController::getCurrentCart - Aucun ID invité fourni pour un utilisateur non connecté');
                // Générer un nouvel ID invité si nécessaire
                $guestId = Panier::generateGuestId();
                $request->attributes->set('cart_guest_id', $guestId);
            }

            Log::debug('PanierController::getCurrentCart - Utilisateur non connecté, recherche du panier par guest_id', ['guest_id' => $guestId]);
            $cart = Panier::where('guest_id', $guestId)->first();

            if (!$cart) {
                Log::debug('PanierController::getCurrentCart - Aucun panier trouvé, création d\'un nouveau panier invité', ['guest_id' => $guestId]);
                // Créer un nouveau panier
                $cart = Panier::create(['guest_id' => $guestId, 'client_id' => null]);
            } else {
                Log::debug('PanierController::getCurrentCart - Panier existant trouvé pour l\'invité', ['cart_id' => $cart->id]);
            }
        }

        Log::debug('PanierController::getCurrentCart - Fin', [
            'cart_id' => $cart->id,
            'cart_client_id' => $cart->client_id,
            'cart_guest_id' => $cart->guest_id,
            'request_guest_id' => $guestId,
            'user_id' => $userId,
            'is_authenticated' => $isAuthenticated
        ]);

        return $cart;
    }

    /**
     * Affiche le contenu du panier
     *
     * Récupère le contenu du panier actuel de l'utilisateur, identifié soit par son cookie de session, soit par son authentification Keycloak.
     *
     * @response {
     *  "panier": {
     *    "id": 1,
     *    "items": [
     *      {
     *        "id": 1,
     *        "produit": {
     *          "id": 5,
     *          "nom": "Smartphone XYZ",
     *          "reference": "2-5",
     *          "image": "smartphone_xyz.jpg"
     *        },
     *        "variante": {
     *          "id": 3,
     *          "sku": "XYZ-RED-128",
     *          "attributs": [
     *            {
     *              "nom": "Couleur",
     *              "valeur": "Rouge"
     *            },
     *            {
     *              "nom": "Stockage",
     *              "valeur": "128 Go"
     *            }
     *          ]
     *        },
     *        "quantite": 1,
     *        "prix_unitaire": 599.99,
     *        "sous_total": 599.99
     *      }
     *    ],
     *    "total": 599.99
     *  }
     * }
     */
    public function index(Request $request)
    {
        Log::debug('PanierController::index - Début', [
            'cart_guest_id' => $request->attributes->get('cart_guest_id'),
            'cookies' => $request->cookies->all(),
            'path' => $request->path(),
            'method' => $request->method()
        ]);

        $cart = $this->getCurrentCart($request);

        // Charger les relations pour éviter les requêtes N+1
        $cart->load('items.produit', 'items.variante');

        // Create a response with the cart data in the standardized format
        $response = response()->json([
            'status' => 'success',
            'data' => [
                'id' => $cart->id,
                'guest_id' => $cart->guest_id,
                'client_id' => $cart->client_id,
                'items' => $cart->items->map(function ($item) {
                    $produit = $item->produit;
                    $variante = $item->variante;

                    return [
                        'id' => $item->id,
                        'produit' => [
                            'id' => $produit->id,
                            'nom' => $produit->nom_produit,
                            'description' => $produit->description_produit,
                            'reference' => $produit->reference ?? null,
                            'image' => $produit->image_produit ?? $produit->primary_image_url ?? null,
                            'prix' => $produit->prix_produit,
                            'en_stock' => $produit->quantite_produit > 0,
                            'stock_disponible' => $produit->quantite_produit
                        ],
                        'variante' => $variante ? [
                            'id' => $variante->id,
                            'nom' => $variante->nom ?? null,
                            'sku' => $variante->sku ?? null,
                            'prix_supplement' => $variante->prix_supplement ?? 0,
                            'attributs' => $variante->valeurs ? $variante->valeurs->map(function ($valeur) {
                                return [
                                    'nom' => $valeur->attribut->nom,
                                    'valeur' => $valeur->valeur
                                ];
                            }) : []
                        ] : null,
                        'quantite' => $item->quantite,
                        'prix_unitaire' => $item->prix_unitaire,
                        'prix_total' => $item->getSubtotal()
                    ];
                }),
                'nombre_items' => $cart->items->sum('quantite'),
                'sous_total' => $cart->getTotal(),
                'total' => $cart->getTotal(), // À remplacer par le calcul incluant les taxes, frais de livraison, etc.
                'date_creation' => $cart->created_at,
                'date_modification' => $cart->updated_at
            ]
        ]);

        // Set the cart guest cookie
        $guestId = $request->attributes->get('cart_guest_id');

        // Déterminer si nous sommes en environnement sécurisé (HTTPS)
        $isSecure = app()->environment('production') || request()->secure();

        // En production ou en HTTPS, utiliser SameSite=None avec Secure=true
        // En développement local sans HTTPS, utiliser SameSite=Lax
        $sameSite = $isSecure ? 'none' : 'lax';

        Log::debug('PanierController::index - Configuration du cookie', [
            'environment' => app()->environment(),
            'is_secure' => $isSecure,
            'same_site' => $sameSite
        ]);

        $cookie = cookie(
            'cart_guest_id',
            $guestId,
            43200,          // 30 days
            '/',            // Path
            null,           // Domain (null = current domain)
            $isSecure,      // Secure (true en production/HTTPS, false en dev local)
            true,           // HttpOnly (true pour plus de sécurité)
            false,          // Raw
            $sameSite       // SameSite policy (none pour cross-domain, lax pour dev local)
        );

        $response->headers->setCookie($cookie);

        // Add debug header
        $response->headers->set('X-Cart-Guest-ID', $guestId);

        Log::debug('PanierController::index - Fin', [
            'cart_id' => $cart->id,
            'cart_guest_id' => $guestId,
            'cookie_params' => [
                'name' => 'cart_guest_id',
                'value' => $guestId,
                'expires' => 43200,
                'path' => '/',
                'domain' => null,
                'secure' => $isSecure,
                'httpOnly' => true,
                'sameSite' => $sameSite
            ]
        ]);

        return $response;
    }

    /**
     * Ajoute un produit au panier
     *
     * Ajoute un produit au panier avec la quantité spécifiée. Si le produit existe déjà dans le panier, la quantité est augmentée.
     * Vérifie également la disponibilité du stock avant l'ajout.
     *
     * @bodyParam produit_id integer required ID du produit à ajouter. Example: 5
     * @bodyParam variante_id integer ID de la variante du produit (optionnel). Example: 3
     * @bodyParam quantite integer required Quantité à ajouter (minimum 1). Example: 1
     *
     * @response status=422 {
     *   "errors": {
     *     "produit_id": ["Le champ produit_id est obligatoire."],
     *     "quantite": ["Le champ quantite est obligatoire."]
     *   }
     * }
     *
     * @response status=400 {
     *   "error": "Stock insuffisant pour ce produit"
     * }
     */
    public function addItem(AddCartItemRequest $request)
    {
        Log::debug('PanierController::addItem - Début', [
            'cart_session_id' => $request->attributes->get('cart_session_id'),
            'cookies' => $request->cookies->all(),
            'request_data' => $request->all(),
            'path' => $request->path(),
            'method' => $request->method()
        ]);

        $cart = $this->getCurrentCart($request);

        Log::debug('PanierController::addItem - Panier récupéré', [
            'cart_id' => $cart->id,
            'produit_id' => $request->produit_id,
            'variante_id' => $request->variante_id,
            'quantite' => $request->quantite
        ]);

        try {
            $cart->addItem(
                $request->produit_id,
                $request->quantite,
                $request->variante_id
            );

            Log::debug('PanierController::addItem - Produit ajouté avec succès', [
                'cart_id' => $cart->id,
                'produit_id' => $request->produit_id
            ]);

            return $this->index($request);
        } catch (\Exception $e) {
            Log::error('PanierController::addItem - Erreur lors de l\'ajout du produit', [
                'cart_id' => $cart->id,
                'produit_id' => $request->produit_id,
                'error' => $e->getMessage()
            ]);
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Met à jour la quantité d'un item
     *
     * Met à jour la quantité d'un produit dans le panier. Si la quantité est 0, l'item est supprimé du panier.
     * Vérifie également la disponibilité du stock avant la mise à jour.
     *
     * @urlParam itemId integer required ID de l'item du panier à mettre à jour. Example: 1
     * @bodyParam quantite integer required Nouvelle quantité (0 pour supprimer). Example: 3
     *
     * @response status=422 {
     *   "errors": {
     *     "quantite": ["Le champ quantite est obligatoire."]
     *   }
     * }
     *
     * @response status=400 {
     *   "error": "Stock insuffisant pour ce produit"
     * }
     */
    public function updateItem(UpdateCartItemRequest $request, $itemId)
    {
        $cart = $this->getCurrentCart($request);

        try {
            // Vérifier si l'item existe dans le panier actuel
            $itemExists = $cart->items()->where('id', $itemId)->exists();

            if (!$itemExists) {
                // Si l'item n'existe pas dans ce panier, essayer de le trouver dans la base de données
                $item = PanierItem::find($itemId);

                if (!$item) {
                    return response()->json([
                        'status' => 'error',
                        'message' => "L'item #$itemId n'existe pas"
                    ], 404);
                } else {
                    return response()->json([
                        'status' => 'error',
                        'message' => "L'item #$itemId appartient au panier #{$item->panier_id}, mais vous utilisez le panier #{$cart->id}",
                        'suggestion' => "Essayez de rafraîchir votre panier"
                    ], 400);
                }
            }

            $cart->updateItem($itemId, $request->quantite);

            return $this->index($request);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Supprime un item du panier
     *
     * Supprime un produit spécifique du panier, identifié par son ID d'item.
     *
     * @urlParam itemId integer required ID de l'item du panier à supprimer. Example: 1
     *
     * @response status=400 {
     *   "error": "Item non trouvé dans le panier"
     * }
     */
    public function removeItem(Request $request, $itemId)
    {
        $cart = $this->getCurrentCart($request);

        try {
            // Vérifier si l'item existe dans le panier actuel
            $itemExists = $cart->items()->where('id', $itemId)->exists();

            if (!$itemExists) {
                // Si l'item n'existe pas dans ce panier, essayer de le trouver dans la base de données
                $item = PanierItem::find($itemId);

                if (!$item) {
                    return response()->json([
                        'status' => 'error',
                        'message' => "L'item #$itemId n'existe pas"
                    ], 404);
                } else {
                    return response()->json([
                        'status' => 'error',
                        'message' => "L'item #$itemId appartient au panier #{$item->panier_id}, mais vous utilisez le panier #{$cart->id}",
                        'suggestion' => "Essayez de rafraîchir votre panier"
                    ], 400);
                }
            }

            $cart->removeItem($itemId);

            return $this->index($request);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Vide le panier
     *
     * Supprime tous les produits du panier actuel.
     *
     * @response {
     *   "message": "Panier vidé avec succès"
     * }
     */
    public function clear(Request $request)
    {
        $cart = $this->getCurrentCart($request);
        $cart->clear();

        return response()->json([
            'status' => 'success',
            'message' => 'Panier vidé avec succès',
            'data' => [
                'id' => $cart->id,
                'nombre_items' => 0,
                'sous_total' => 0,
                'total' => 0,
                'items' => []
            ]
        ]);
    }

    /**
     * Fusionner un panier invité avec le panier de l'utilisateur connecté
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function merge(Request $request)
    {
        // Vérifier que l'utilisateur est connecté
        $authError = $this->checkAuthentication($request, 'Vous devez être connecté pour fusionner les paniers');
        if ($authError) {
            return $authError;
        }

        $validator = Validator::make($request->all(), [
            'guest_id' => 'required|exists:paniers,guest_id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation échouée',
                'errors' => $validator->errors()
            ], 422);
        }

        $userId = Auth::id();
        $guestId = $request->input('guest_id');

        // Récupérer le panier invité
        $guestCart = Panier::where('guest_id', $guestId)->first();

        if (!$guestCart) {
            return response()->json([
                'status' => 'error',
                'message' => 'Panier invité non trouvé'
            ], 404);
        }

        // Vérifier que le panier invité n'est pas associé à un utilisateur
        if ($guestCart->client_id !== null) {
            return response()->json([
                'status' => 'error',
                'message' => 'Le panier invité est déjà associé à un utilisateur'
            ], 400);
        }

        // Récupérer ou créer le panier de l'utilisateur
        $userCart = Panier::where('client_id', $userId)->first();

        if (!$userCart) {
            $userCart = Panier::create([
                'client_id' => $userId,
                'guest_id' => null
            ]);
        }

        // Fusionner les paniers
        $guestCart->load('items.produit', 'items.variante');

        foreach ($guestCart->items as $item) {
            try {
                // Vérifier si l'item existe déjà dans le panier de l'utilisateur
                $existingItem = $userCart->items()
                    ->where('produit_id', $item->produit_id)
                    ->where(function ($query) use ($item) {
                        if ($item->variante_id) {
                            $query->where('variante_id', $item->variante_id);
                        } else {
                            $query->whereNull('variante_id');
                        }
                    })
                    ->first();

                if ($existingItem) {
                    // Mettre à jour la quantité
                    $userCart->updateItem($existingItem->id, $existingItem->quantite + $item->quantite);
                } else {
                    // Ajouter l'item au panier de l'utilisateur
                    $userCart->addItem($item->produit_id, $item->quantite, $item->variante_id);
                }
            } catch (\Exception $e) {
                // Ignorer les erreurs (stock insuffisant, etc.) et continuer avec les autres items
                Log::warning('PanierController::merge - Erreur lors de la fusion d\'un item', [
                    'guest_id' => $guestId,
                    'guest_cart_id' => $guestCart->id,
                    'user_cart_id' => $userCart->id,
                    'item_id' => $item->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        // Supprimer le panier invité
        $guestCart->delete();

        // Recharger le panier de l'utilisateur
        $userCart->load('items.produit', 'items.variante');

        return response()->json([
            'status' => 'success',
            'message' => 'Paniers fusionnés avec succès',
            'data' => [
                'id' => $userCart->id,
                'nombre_items' => $userCart->items->sum('quantite'),
                'sous_total' => $userCart->getTotal(),
                'total' => $userCart->getTotal(),
                'items' => $userCart->items->map(function ($item) {
                    $produit = $item->produit;
                    $variante = $item->variante;

                    return [
                        'id' => $item->id,
                        'produit' => [
                            'id' => $produit->id,
                            'nom' => $produit->nom_produit,
                            'description' => $produit->description_produit,
                            'reference' => $produit->reference ?? null,
                            'image' => $produit->image_produit ?? $produit->primary_image_url ?? null,
                            'prix' => $produit->prix_produit,
                            'en_stock' => $produit->quantite_produit > 0,
                            'stock_disponible' => $produit->quantite_produit
                        ],
                        'variante' => $variante ? [
                            'id' => $variante->id,
                            'nom' => $variante->nom ?? null,
                            'sku' => $variante->sku ?? null,
                            'prix_supplement' => $variante->prix_supplement ?? 0,
                            'attributs' => $variante->valeurs ? $variante->valeurs->map(function ($valeur) {
                                return [
                                    'nom' => $valeur->attribut->nom,
                                    'valeur' => $valeur->valeur
                                ];
                            }) : []
                        ] : null,
                        'quantite' => $item->quantite,
                        'prix_unitaire' => $item->prix_unitaire,
                        'prix_total' => $item->getSubtotal()
                    ];
                })
            ]
        ]);
    }
}
