<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paiement confirmé - <PERSON>k parisien</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600&family=Inter:wght@300;400;500&display=swap');

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            line-height: 1.7;
            color: #5a4a3a;
            max-width: 600px;
            margin: 0 auto;
            padding: 0;
            background: linear-gradient(135deg, #f8f6f0 0%, #f1ede4 100%);
            font-weight: 300;
        }

        .container {
            background-color: #ffffff;
            margin: 20px;
            border-radius: 0;
            box-shadow: 0 8px 32px rgba(139, 117, 95, 0.12);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #d4c4a8 0%, #c8b896 100%);
            color: #5a4a3a;
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="0.5" fill="%23ffffff" opacity="0.1"/><circle cx="75" cy="75" r="0.3" fill="%23ffffff" opacity="0.08"/><circle cx="50" cy="10" r="0.4" fill="%23ffffff" opacity="0.06"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
            opacity: 0.3;
        }

        .header h1 {
            margin: 0;
            font-family: 'Playfair Display', serif;
            font-size: 28px;
            font-weight: 600;
            letter-spacing: -0.5px;
            position: relative;
            z-index: 1;
        }

        .header p {
            margin: 8px 0 0 0;
            font-size: 14px;
            opacity: 0.8;
            font-weight: 400;
            position: relative;
            z-index: 1;
        }

        .content {
            padding: 40px 30px;
        }

        .success-info {
            background: linear-gradient(135deg, #f0f8f0 0%, #e8f5e8 100%);
            padding: 30px;
            border-radius: 2px;
            margin-bottom: 30px;
            border-left: 3px solid #8b7560;
            position: relative;
        }

        .success-info h2 {
            margin-top: 0;
            color: #5a4a3a;
            font-family: 'Playfair Display', serif;
            font-size: 22px;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .success-info p {
            margin: 0;
            color: #6b5b4b;
            line-height: 1.6;
        }

        .payment-details {
            background-color: #fbfaf8;
            padding: 30px;
            border-radius: 2px;
            margin-bottom: 30px;
            border: 1px solid #f0ede6;
        }

        .payment-details h3 {
            margin-top: 0;
            color: #5a4a3a;
            font-family: 'Playfair Display', serif;
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 25px;
        }

        .field {
            margin-bottom: 20px;
            padding: 18px 20px;
            background-color: #ffffff;
            border-radius: 1px;
            border: 1px solid #f0ede6;
            transition: all 0.2s ease;
        }

        .field:hover {
            border-color: #d4c4a8;
            box-shadow: 0 2px 8px rgba(139, 117, 95, 0.08);
        }

        .field-label {
            font-weight: 500;
            color: #8b7560;
            margin-bottom: 8px;
            text-transform: uppercase;
            font-size: 11px;
            letter-spacing: 1.2px;
        }

        .field-value {
            font-size: 16px;
            color: #5a4a3a;
            font-weight: 400;
        }

        .amount {
            background: linear-gradient(135deg, #8b7560 0%, #7a6550 100%);
            color: #ffffff;
            padding: 25px;
            border-radius: 2px;
            text-align: center;
            font-size: 20px;
            font-weight: 500;
            margin: 30px 0;
            letter-spacing: 0.5px;
        }

        .transaction-id {
            background: linear-gradient(135deg, #faf9f6 0%, #f5f3ee 100%);
            padding: 15px;
            border-radius: 2px;
            font-family: 'Inter', monospace;
            font-size: 13px;
            word-break: break-all;
            border: 1px solid #f0ede6;
            color: #8b7560;
        }

        .footer {
            margin-top: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #faf9f6 0%, #f5f3ee 100%);
            text-align: center;
            color: #8b7560;
            font-size: 13px;
            line-height: 1.6;
            border-top: 1px solid #f0ede6;
        }

        .footer strong {
            color: #5a4a3a;
            font-weight: 500;
        }

        .next-steps {
            background: linear-gradient(135deg, #faf9f6 0%, #f5f3ee 100%);
            padding: 30px;
            border-radius: 2px;
            margin: 30px 0;
            border-left: 3px solid #d4c4a8;
        }

        .next-steps h3 {
            color: #5a4a3a;
            margin-top: 0;
            font-family: 'Playfair Display', serif;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .next-steps ul {
            margin: 0;
            padding-left: 20px;
            color: #6b5b4b;
        }

        .next-steps li {
            margin-bottom: 8px;
            line-height: 1.6;
        }

        .next-steps strong {
            color: #8b7560;
            font-weight: 500;
        }

        .security-note {
            background: linear-gradient(135deg, #fdf8e8 0%, #faf5e0 100%);
            padding: 25px;
            border-radius: 2px;
            margin: 30px 0;
            border-left: 3px solid #d4c4a8;
        }

        .security-note h4 {
            color: #8b7560;
            margin-top: 0;
            font-family: 'Playfair Display', serif;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .security-note p {
            color: #6b5b4b;
            line-height: 1.6;
        }

        /* Mobile responsiveness */
        @media only screen and (max-width: 600px) {
            .container {
                margin: 10px;
            }

            .header, .content {
                padding: 30px 20px;
            }

            .success-info, .payment-details, .next-steps, .security-note {
                padding: 20px;
            }

            .field {
                padding: 15px;
            }

            .header h1 {
                font-size: 24px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>Paiement Confirmé</h1>
            <p>Lack parisien - Votre paiement a été traité avec succès</p>
        </div>

        <div class="content">
            <div class="success-info">
                <h2>Paiement réussi</h2>
                <p>Votre paiement a été traité avec succès. Votre commande est maintenant confirmée et sera préparée avec le plus grand soin.</p>
            </div>

            <div class="payment-details">
                <h3>Détails du paiement</h3>

                <div class="field">
                    <div class="field-label">Commande</div>
                    <div class="field-value">#{{ $orderNumber }}</div>
                </div>

                <div class="field">
                    <div class="field-label">Méthode de paiement</div>
                    <div class="field-value">{{ ucfirst(str_replace('_', ' ', $paymentMethod)) }}</div>
                </div>

                <div class="field">
                    <div class="field-label">ID de transaction</div>
                    <div class="field-value">
                        <div class="transaction-id">{{ $transactionId }}</div>
                    </div>
                </div>

                <div class="field">
                    <div class="field-label">Date du paiement</div>
                    <div class="field-value">{{ $paidAt }}</div>
                </div>
            </div>

            <div class="amount">
                Montant payé: {{ number_format($paymentAmount, 2) }}€
            </div>

            <div class="next-steps">
                <h3>Prochaines étapes</h3>
                <ul>
                    <li><strong>Préparation :</strong> Votre commande est en cours de préparation dans nos ateliers</li>
                    <li><strong>Expédition :</strong> Notification avec numéro de suivi</li>
                    <li><strong>Livraison :</strong> Réception à l'adresse indiquée</li>
                    <li><strong>Support :</strong> Notre équipe reste à votre disposition</li>
                </ul>
            </div>

            <div class="security-note">
                <h4>Note de sécurité</h4>
                <p>Conservez cet email comme preuve de paiement. L'ID de transaction peut être utilisé pour toute réclamation ou question concernant votre paiement.</p>
            </div>
        </div>

        <div class="footer">
            <p><strong>Lack parisien</strong> - L'art de vivre à la française</p>
            <p>Paiement traité le {{ $paidAt }}</p>
            <p>ID de transaction: {{ $transactionId }}</p>
        </div>
    </div>
</body>

</html>
