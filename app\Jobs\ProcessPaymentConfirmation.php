<?php

namespace App\Jobs;

use App\Models\Commande;
use App\Services\ProductCacheService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessPaymentConfirmation implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 300; // 5 minutes
    public $tries = 3;

    protected Commande $order;

    /**
     * Create a new job instance.
     */
    public function __construct(Commande $order)
    {
        $this->order = $order;
        $this->onQueue('payments');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('Processing payment confirmation', [
                'order_id' => $this->order->id,
                'amount' => $this->order->total_ttc
            ]);

            // Update product stock
            $this->updateProductStock();

            // Clear relevant caches
            $this->clearCaches();

            // Generate invoice
            $this->generateInvoice();

            // Update inventory
            $this->updateInventory();

            // Send internal notifications
            $this->sendInternalNotifications();

            Log::info('Payment confirmation processed successfully', [
                'order_id' => $this->order->id
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to process payment confirmation', [
                'order_id' => $this->order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * Update product stock quantities
     */
    private function updateProductStock(): void
    {
        foreach ($this->order->items as $item) {
            $product = $item->produit;
            if ($product) {
                $newQuantity = max(0, $product->quantite_produit - $item->quantite);
                $product->update(['quantite_produit' => $newQuantity]);

                // Log stock update
                Log::info('Product stock updated', [
                    'product_id' => $product->id,
                    'old_quantity' => $product->quantite_produit,
                    'new_quantity' => $newQuantity,
                    'ordered_quantity' => $item->quantite
                ]);

                // Check for low stock alert
                if ($newQuantity <= ($product->stock_alert_threshold ?? 5)) {
                    Log::warning('Low stock alert', [
                        'product_id' => $product->id,
                        'product_name' => $product->nom_produit,
                        'current_stock' => $newQuantity,
                        'threshold' => $product->stock_alert_threshold ?? 5
                    ]);
                }
            }
        }
    }

    /**
     * Clear relevant caches
     */
    private function clearCaches(): void
    {
        try {
            $cacheService = app(ProductCacheService::class);

            foreach ($this->order->items as $item) {
                if ($item->produit) {
                    $cacheService->clearProductCache($item->produit->id);
                }
            }

            // Clear category caches if needed
            $cacheService->clearCategoryCache();

        } catch (\Exception $e) {
            Log::warning('Failed to clear caches after payment', [
                'order_id' => $this->order->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Generate invoice for the order
     */
    private function generateInvoice(): void
    {
        try {
            // Generate invoice number if not exists
            if (!$this->order->invoice_number) {
                $invoiceNumber = $this->generateInvoiceNumber();
                $this->order->update(['invoice_number' => $invoiceNumber]);
            }

            // Generate PDF invoice (if PDF service exists)
            if (class_exists('App\Services\InvoiceService')) {
                $invoiceService = app('App\Services\InvoiceService');
                $invoiceService->generateInvoice($this->order);
            }

        } catch (\Exception $e) {
            Log::error('Failed to generate invoice', [
                'order_id' => $this->order->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Generate unique invoice number
     */
    private function generateInvoiceNumber(): string
    {
        $year = date('Y');
        $month = date('m');

        // Get last invoice number for this month
        $lastOrder = Commande::whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->whereNotNull('invoice_number')
            ->orderBy('id', 'desc')
            ->first();

        $sequence = 1;
        if ($lastOrder && $lastOrder->invoice_number) {
            // Extract sequence from last invoice number
            $parts = explode('-', $lastOrder->invoice_number);
            if (count($parts) >= 3) {
                $sequence = intval($parts[2]) + 1;
            }
        }

        return sprintf('INV-%s%s-%04d', $year, $month, $sequence);
    }

    /**
     * Update inventory records
     */
    private function updateInventory(): void
    {
        foreach ($this->order->items as $item) {
            // Create inventory movement record if table exists
            if (\Schema::hasTable('inventory_movements')) {
                \DB::table('inventory_movements')->insert([
                    'produit_id' => $item->produit_id,
                    'type' => 'sale',
                    'quantity' => -$item->quantite,
                    'reference' => $this->order->numero_commande,
                    'notes' => 'Vente - Commande #' . $this->order->numero_commande,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }
        }
    }

    /**
     * Send internal notifications
     */
    private function sendInternalNotifications(): void
    {
        try {
            // Notify inventory management
            if (config('notifications.inventory_alerts')) {
                // Send low stock alerts
                $this->checkAndSendLowStockAlerts();
            }

            // Notify fulfillment team
            if (config('notifications.order_fulfillment')) {
                // Could send to fulfillment system
                Log::info('Order ready for fulfillment', [
                    'order_id' => $this->order->id,
                    'items_count' => $this->order->items->count()
                ]);
            }

        } catch (\Exception $e) {
            Log::warning('Failed to send internal notifications', [
                'order_id' => $this->order->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Check and send low stock alerts
     */
    private function checkAndSendLowStockAlerts(): void
    {
        foreach ($this->order->items as $item) {
            $product = $item->produit;
            if ($product && $product->quantite_produit <= ($product->stock_alert_threshold ?? 5)) {
                // Send alert (could be email, Slack, etc.)
                Log::alert('Product low stock alert', [
                    'product_id' => $product->id,
                    'product_name' => $product->nom_produit,
                    'current_stock' => $product->quantite_produit,
                    'threshold' => $product->stock_alert_threshold ?? 5,
                    'triggered_by_order' => $this->order->id
                ]);
            }
        }
    }

    /**
     * Handle job failure
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('ProcessPaymentConfirmation job failed', [
            'order_id' => $this->order->id,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts()
        ]);

        // Could send alert to administrators
        // Or mark order for manual review
        $this->order->update([
            'processing_status' => 'failed',
            'processing_error' => $exception->getMessage()
        ]);
    }
}
