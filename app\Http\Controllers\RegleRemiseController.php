<?php

namespace App\Http\Controllers;

use App\Models\RegleRemise;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Http\Requests\StoreRegleRemiseRequest;
use App\Http\Requests\UpdateRegleRemiseRequest;

class RegleRemiseController extends Controller
{
    /**
     * Afficher la liste des règles de remise
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = RegleRemise::query();

        // Filtrage par type de client
        if ($request->has('type_client')) {
            $query->where('type_client', $request->input('type_client'));
        }

        // Filtrage par statut (active/inactive)
        if ($request->has('active')) {
            $query->where('active', $request->boolean('active'));
        }

        // Pagination
        $regles = $query->orderBy('priorité', 'desc')
            ->paginate($request->input('par_page', 15));

        return response()->json($regles);
    }

    /**
     * Créer une nouvelle règle de remise
     *
     * @param StoreRegleRemiseRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(StoreRegleRemiseRequest $request)
    {
        $regleRemise = RegleRemise::create($request->validated());
        return response()->json($regleRemise, 201);
    }

    /**
     * Afficher une règle de remise spécifique
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $regleRemise = RegleRemise::findOrFail($id);

        return response()->json($regleRemise);
    }

    /**
     * Mettre à jour une règle de remise
     *
     * @param UpdateRegleRemiseRequest $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(UpdateRegleRemiseRequest $request, $id)
    {
        $regleRemise = RegleRemise::findOrFail($id);
        $regleRemise->update($request->validated());
        return response()->json($regleRemise);
    }

    /**
     * Supprimer une règle de remise
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $regleRemise = RegleRemise::findOrFail($id);
        $regleRemise->delete();

        return response()->json(null, 204);
    }
}
