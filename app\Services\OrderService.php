<?php

namespace App\Services;

use App\Models\Commande;
use App\Models\Produit;
use App\Models\Paiement;
use App\Models\User;
use App\Models\Client;
use App\Models\Panier;
use App\Enums\CommandeStatus;
use App\Mail\OrderConfirmationMail;
use App\Mail\OrderStatusUpdateMail;
use App\Mail\PaymentSuccessMail;
use App\Mail\PaymentFailedMail;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Exception;
use Carbon\Carbon;
use Stripe\Stripe; // Example: Add Stripe namespace
use Stripe\Charge; // Example: Add Stripe Charge class
use Stripe\Refund; // Example: Add Stripe Refund class
// use Stripe\Exception\ApiErrorException; // Example: Add Stripe API error

class OrderService
{
    /**
     * Create order from cart with comprehensive validation
     */
    public function createOrderFromCart(string $cartId, ?User $user = null, ?Client $client = null, array $orderData = []): Commande
    {
        Log::info('OrderService:createOrderFromCart called', ['cartId' => $cartId, 'userId' => $user ? $user->id : null]);
        $cartItems = $this->getCartItemsFromPanier($cartId);
        Log::info('OrderService: Cart items retrieved', ['cartItems' => $cartItems]);

        if (empty($cartItems)) {
            throw new Exception("Cart is empty or not found.");
        }

        if (!$user && !$client && !($orderData['is_guest_checkout'] ?? false)) {
            if (empty($orderData['email']) || empty($orderData['nom'])) {
                throw new Exception("User or Client context is required for non-guest orders, or guest details (email, nom) are missing.");
            }
        }

        return DB::transaction(function () use ($cartId, $cartItems, $user, $client, $orderData) {
            foreach ($cartItems as $item) {
                Log::info('OrderService: Processing cart item for stock check', ['item' => $item]);
                $produit = Produit::find($item['produit_id']);
                Log::info('OrderService: Product lookup result for stock check', ['produit_id' => $item['produit_id'], 'produit_exists' => !is_null($produit)]);
                if (!$produit) {
                    Log::error('OrderService: Product not found during stock check.', ['produit_id' => $item['produit_id']]);
                    throw new Exception("Produit introuvable avec ID: " . $item['produit_id']);
                }
                if ($produit->quantite_produit < $item['quantite']) { // Changed from $produit->stock
                    Log::error('OrderService: Insufficient stock.', ['produit_id' => $produit->id, 'stock' => $produit->quantite_produit, 'requested' => $item['quantite']]); // Changed from $produit->stock
                    throw new Exception("Stock insuffisant pour le produit: " . ($produit ? $produit->nom_produit : 'ID inconnu ' . $item['produit_id']));
                }
            }

            $subtotal = 0;
            foreach ($cartItems as $item) {
                $produit = Produit::find($item['produit_id']);
                $subtotal += $produit->prix_produit * $item['quantite']; // Changed from $produit->prix
            }

            $taxRate = config('cart.tax', 0.1);
            $taxAmount = $subtotal * $taxRate;
            $shippingCost = $orderData['shipping_cost'] ?? 5.00;

            $promoCode = $orderData['code_promo'] ?? null;
            $discountDetails = $this->applyPromoCode($promoCode, $subtotal);
            $discountAmount = $discountDetails['discount_amount'];
            $finalPromoCode = $discountDetails['applied_code'];

            $totalAmount = $subtotal + $taxAmount + $shippingCost - $discountAmount;

            $commande = Commande::create([
                'user_id' => $user ? $user->id : null,
                'client_id' => $client ? $client->id : null,
                'numero_commande' => 'CMD-' . strtoupper(Str::random(8)),
                'status' => CommandeStatus::EN_ATTENTE,
                'payment_status' => 'pending',
                'methode_paiement' => $orderData['methode_paiement'] ?? 'stripe',
                'notes' => $orderData['notes'] ?? null,
                'code_promo' => $finalPromoCode,
                'subtotal' => $subtotal,
                'tax_amount' => $taxAmount,
                'shipping_cost' => $shippingCost,
                'discount_amount' => $discountAmount,
                'total_commande' => $totalAmount, // Fixed: use correct field name
                'remise_commande' => 0, // Will be calculated later if needed
                'shipping_address' => $orderData['shipping_address'] ?? null,
                'billing_address' => $orderData['billing_address'] ?? null,
                // Legacy fields for backward compatibility
                'shipping_street' => $orderData['shipping_address']['street'] ?? '',
                'shipping_city' => $orderData['shipping_address']['city'] ?? '',
                'shipping_postal_code' => $orderData['shipping_address']['postal_code'] ?? '',
                'telephone_commande' => $orderData['telephone'] ?? ($user->telephone ?? ($client->telephone ?? null)),
                'email_commande' => $orderData['email'] ?? ($user->email ?? ($client->email ?? null)),
                'date_commande' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            foreach ($cartItems as $item) {
                $produit = Produit::find($item['produit_id']);
                Log::info('OrderService: Attaching product to order', ['produit_id' => $produit->id, 'prix_produit' => $produit->prix_produit, 'quantite' => $item['quantite']]);
                $commande->produits()->attach($produit->id, [
                    'quantite' => $item['quantite'],
                    'prix_unitaire' => $produit->prix_produit, // Changed from $produit->prix
                    'total_ligne' => $produit->prix_produit * $item['quantite'] // Changed from $produit->prix
                ]);

                $produit->decrement('quantite_produit', $item['quantite']); // Changed from 'stock'
            }

            // Recalculate total after products are attached
            $commande->recalculateTotal();

            if (isset($orderData['payment_details'])) {
                $this->processPayment($commande, $orderData['payment_details']);
            }

            $this->sendOrderConfirmationEmail($commande);

            Log::info('Order created successfully', ['order_id' => $commande->id, 'cart_id' => $cartId]);
            return $commande;
        });
    }

    /**
     * Helper method to fetch cart items from Panier model
     */
    private function getCartItemsFromPanier(string $cartId): array
    {
        Log::info('OrderService:getCartItemsFromPanier called', ['cartId' => $cartId]);
        $panier = Panier::with('items.produit')->find($cartId);

        if (!$panier || $panier->items->isEmpty()) {
            Log::warning('Panier not found or empty for ID: ' . $cartId);
            return [];
        }

        $items = [];
        foreach ($panier->items as $panierItem) {
            Log::info('OrderService: Processing panierItem in getCartItemsFromPanier', ['panierItem_id' => $panierItem->id, 'produit_id' => $panierItem->produit_id, 'produit_exists_on_relation' => !is_null($panierItem->produit)]);
            if ($panierItem->produit) {
                $items[] = [
                    'produit_id' => $panierItem->produit_id,
                    'quantite' => $panierItem->quantite,
                ];
            } else {
                Log::warning('Panier item references a non-existent product.', ['panier_item_id' => $panierItem->id, 'produit_id' => $panierItem->produit_id]);
            }
        }
        return $items;
    }

    /**
     * Process payment for an order
     */
    public function processPayment(Commande $commande, array $paymentDetails): Paiement
    {
        Log::info('Processing payment for order', ['order_id' => $commande->id, 'details' => $paymentDetails]);

        // Initialize Payment Gateway (e.g., Stripe)
        // Stripe::setApiKey(config('services.stripe.secret'));
        // $paymentMethodId = $paymentDetails['payment_method_id'] ?? null; // e.g., from Stripe Elements

        $isSuccessful = false;
        $gatewayTransactionId = null;
        $gatewayResponse = [];

        try {
            // Example: Create a charge with Stripe
            // if (!$paymentMethodId) {
            //     throw new Exception("Payment method ID is required.");
            // }
            // $charge = Charge::create([
            //     'amount' => (int) ($commande->total_amount * 100), // Amount in cents
            //     'currency' => 'eur', // Or your desired currency
            //     'source' => $paymentMethodId, // or 'tok_visa' for testing
            //     'description' => 'Order ' . $commande->numero_commande,
            //     'metadata' => ['order_id' => $commande->id]
            // ]);
            //
            // if ($charge->paid) {
            //     $isSuccessful = true;
            //     $gatewayTransactionId = $charge->id;
            //     $gatewayResponse = $charge->toArray();
            //     Log::info('Stripe charge successful', ['order_id' => $commande->id, 'charge_id' => $charge->id]);
            // } else {
            //     $gatewayResponse = $charge->toArray();
            //     Log::error('Stripe charge not paid', ['order_id' => $commande->id, 'charge_status' => $charge->status, 'response' => $gatewayResponse]);
            //     throw new Exception("Stripe charge was not successful: " . $charge->status);
            // }

            // --- SIMULATION (remove or replace with actual gateway logic) ---
            if (($paymentDetails['simulate'] ?? 'success') === 'success') {
                $isSuccessful = true;
                $gatewayTransactionId = 'TRANS-' . strtoupper(Str::random(10));
                $gatewayResponse = ['status' => 'simulated_success', 'id' => $gatewayTransactionId];
                Log::info('Simulated payment successful', ['order_id' => $commande->id, 'transaction_id' => $gatewayTransactionId]);
            } else {
                $gatewayResponse = ['status' => 'simulated_failure', 'reason' => $paymentDetails['simulate_fail_reason'] ?? 'Unknown reason'];
                Log::error('Simulated payment failed', ['order_id' => $commande->id, 'response' => $gatewayResponse]);
                throw new Exception("Simulated payment failure.");
            }
            // --- END SIMULATION ---

        } catch (Exception $e) {
            Log::error('Error during payment processing', ['order_id' => $commande->id, 'error' => $e->getMessage()]);
            // $gatewayResponse = array_merge($gatewayResponse, ['error' => $e->getMessage(), 'type' => 'generic_error']);
            if (empty($gatewayResponse))
                $gatewayResponse = ['error' => $e->getMessage(), 'type' => 'generic_error_simulation']; // Keep for simulation

            // Send payment failed email
            $this->sendPaymentFailedEmail($commande, $e->getMessage());

            throw $e; // Re-throw the exception to be handled by the controller or transaction rollback
        }

        // This part remains largely the same, but driven by $isSuccessful from the gateway
        if ($isSuccessful) {
            $paiement = Paiement::create([
                'commande_id' => $commande->id,
                'montant' => $commande->total_commande,
                'methode_paiement' => $commande->methode_paiement,
                'status' => 'completed', // 'paid' or 'completed' depending on your status enum for Paiement
                'transaction_id' => $gatewayTransactionId,
                'gateway_response' => $gatewayResponse,
                'processed_at' => now(),
            ]);

            $commande->update([
                'payment_status' => 'paid',
                'status' => CommandeStatus::CONFIRMEE,
                'confirmed_at' => now(),
            ]);

            // Send payment success email
            $this->sendPaymentSuccessEmail($commande, $paiement);

            Log::info('Payment successful and order confirmed', ['order_id' => $commande->id, 'payment_id' => $paiement->id]);
            return $paiement;
        } else {
            // This block might be redundant if exceptions are always thrown on failure.
            // However, it can handle cases where the gateway returns a failure without an exception.
            $paiement = Paiement::create([
                'commande_id' => $commande->id,
                'montant' => $commande->total_commande,
                'methode_paiement' => $commande->methode_paiement,
                'status' => 'failed',
                'transaction_id' => $gatewayTransactionId, // Might be null
                'gateway_response' => $gatewayResponse,
                'processed_at' => now(),
            ]);
            $commande->update(['payment_status' => 'failed']);
            Log::error('Payment failed for order after gateway interaction (or simulation)', ['order_id' => $commande->id, 'payment_id' => $paiement->id]);
            // It's generally better to throw an exception on failure to ensure the transaction rolls back.
            // If not already thrown, throw one here.
            if (!isset($e)) { // If no exception was caught and thrown already
                throw new Exception("Payment processing failed after gateway interaction.");
            }
            return $paiement; // Should not be reached if exception is thrown
        }
    }

    /**
     * Initiate refund for a payment
     */
    public function initiateRefund(Paiement $paiement, string $reason = ''): bool
    {
        Log::info('Initiating refund for payment', ['payment_id' => $paiement->id, 'reason' => $reason]);

        // Initialize Payment Gateway (e.g., Stripe)
        // Stripe::setApiKey(config('services.stripe.secret'));

        $isRefundSuccessful = false;
        $refundGatewayResponse = (array) $paiement->gateway_response;

        try {
            // Example: Create a refund with Stripe
            // if (!$paiement->transaction_id) {
            //     throw new Exception("Cannot refund payment without a transaction ID.");
            // }
            // $refund = Refund::create([
            //     'charge' => $paiement->transaction_id, // or 'payment_intent' depending on your Stripe integration
            //     'amount' => (int) ($paiement->montant * 100), // Optional: for partial refunds
            //     'reason' => $reason ?: 'requested_by_customer',
            //     'metadata' => ['payment_id' => $paiement->id, 'order_id' => $paiement->commande_id]
            // ]);
            //
            // if ($refund->status === 'succeeded') {
            //     $isRefundSuccessful = true;
            //     $refundGatewayResponse['refund_details'] = $refund->toArray();
            //     Log::info('Stripe refund successful', ['payment_id' => $paiement->id, 'refund_id' => $refund->id]);
            // } else {
            //     $refundGatewayResponse['refund_details'] = $refund->toArray();
            //     Log::error('Stripe refund not successful', ['payment_id' => $paiement->id, 'refund_status' => $refund->status, 'response' => $refundGatewayResponse]);
            //     throw new Exception("Stripe refund was not successful: " . $refund->status);
            // }

            // --- SIMULATION (remove or replace with actual gateway logic) ---
            if (($paiement->commande->methode_paiement ?? 'stripe') !== 'cash_on_delivery') { // Example condition
                $isRefundSuccessful = true; // Simulate success for non-COD
                $refundGatewayResponse = array_merge($refundGatewayResponse, ['refund_message' => 'Refund successful (simulated)', 'refund_reason' => $reason, 'simulation' => true]);
            } else {
                // For COD, refund might be manual or not applicable via gateway
                Log::info('Refund for COD order, manual process assumed.', ['payment_id' => $paiement->id]);
                $isRefundSuccessful = true; // Or false if it should fail
                $refundGatewayResponse = array_merge($refundGatewayResponse, ['refund_message' => 'Refund for COD (simulated as manual/successful)', 'refund_reason' => $reason, 'simulation' => true]);
            }
            // --- END SIMULATION ---

        } catch (Exception $e) {
            Log::error('Error during refund initiation', ['payment_id' => $paiement->id, 'error' => $e->getMessage()]);
            $refundGatewayResponse['refund_error'] = $e->getMessage();
            $refundGatewayResponse['refund_error_type'] = 'generic_error_simulation'; // Keep for simulation
            // Update gateway response on paiement even if refund fails before returning
            $paiement->update(['gateway_response' => $refundGatewayResponse]);
            return false; // Or throw an exception
        }

        if ($isRefundSuccessful) {
            $paiement->update([
                'status' => 'refunded',
                'refunded_at' => now(),
                'gateway_response' => $refundGatewayResponse
            ]);
            Log::info('Refund successful and payment record updated', ['payment_id' => $paiement->id]);
            return true;
        } else {
            $paiement->update([
                'gateway_response' => $refundGatewayResponse // Ensure error details are saved
            ]);
            Log::error('Refund failed for payment after gateway interaction (or simulation)', ['payment_id' => $paiement->id]);
            return false;
        }
    }

    /**
     * Update order status with validation
     */
    public function updateOrderStatus(Commande $commande, CommandeStatus $newStatus, ?string $notes = null, bool $sendNotification = true): Commande
    {
        $oldStatus = $commande->status;

        if ($oldStatus === $newStatus) {
            Log::info('Order status is already the same.', ['order_id' => $commande->id, 'status' => $newStatus->value]);
            return $commande;
        }

        if ($oldStatus === CommandeStatus::LIVREE && $newStatus !== CommandeStatus::REMBOURSEE && $newStatus !== CommandeStatus::RETOURNEE) {
            throw new Exception("Cannot change status from LIVREE to {$newStatus->value}.");
        }
        if ($oldStatus === CommandeStatus::ANNULEE && $newStatus !== CommandeStatus::REMBOURSEE) {
            throw new Exception("Cannot change status from ANNULEE to {$newStatus->value} unless it's for a refund.");
        }

        return DB::transaction(function () use ($commande, $newStatus, $notes, $oldStatus, $sendNotification) {
            $updateData = ['status' => $newStatus];
            if ($notes) {
                $updateData['notes'] = $commande->notes ? $commande->notes . "\n--- Status Update ---\n" . $notes : $notes;
            }

            switch ($newStatus) {
                case CommandeStatus::CONFIRMEE:
                    $updateData['confirmed_at'] = now();
                    $updateData['payment_status'] = $commande->payment_status === 'pending' ? 'paid' : $commande->payment_status;
                    break;
                case CommandeStatus::EN_PREPARATION:
                    $updateData['preparation_started_at'] = now();
                    break;
                case CommandeStatus::EXPEDIEE:
                    $updateData['shipped_at'] = now();
                    break;
                case CommandeStatus::LIVREE:
                    $updateData['delivered_at'] = now();
                    $updateData['payment_status'] = 'paid';
                    break;
                case CommandeStatus::ANNULEE:
                    $updateData['cancelled_at'] = now();
                    $this->handleOrderCancellation($commande, $oldStatus);
                    $commande->refresh();
                    $updateData['payment_status'] = $commande->payment_status;
                    break;
                case CommandeStatus::REMBOURSEE:
                    $updateData['refunded_at'] = now();
                    $this->handleOrderRefund($commande);
                    $updateData['payment_status'] = 'refunded';
                    break;
                case CommandeStatus::RETOURNEE:
                    $updateData['returned_at'] = now();
                    break;
            }
            $updateData['updated_at'] = now();
            $commande->update($updateData);
            $commande->refresh();

            if ($sendNotification) {
                $this->sendOrderStatusUpdateEmail($commande, $oldStatus->value, $newStatus->value);
            }

            Log::info('Order status updated', [
                'order_id' => $commande->id,
                'old_status' => $oldStatus->value,
                'new_status' => $newStatus->value
            ]);
            return $commande;
        });
    }

    /**
     * Cancel order and restore stock
     */
    public function cancelOrder(Commande $commande, string $reason = ''): Commande
    {
        if (
            !in_array($commande->status, [
                CommandeStatus::EN_ATTENTE,
                CommandeStatus::CONFIRMEE,
                CommandeStatus::EN_PREPARATION
            ])
        ) {
            throw new Exception('Order cannot be cancelled in current status: ' . $commande->status->value);
        }

        $notes = 'Order cancelled by system/user.';
        if (!empty($reason)) {
            $notes .= ' Reason: ' . $reason;
        }

        return $this->updateOrderStatus($commande, CommandeStatus::ANNULEE, $notes);
    }

    /**
     * Handle order cancellation (stock restoration, payment void/refund)
     */
    private function handleOrderCancellation(Commande $commande, CommandeStatus $oldStatus): void
    {
        if (in_array($oldStatus, [CommandeStatus::CONFIRMEE, CommandeStatus::EN_PREPARATION])) {
            $this->restoreProductStock($commande);
        }

        if ($commande->paiement) {
            $payment = $commande->paiement->fresh();
            if ($payment->status === 'completed' || $payment->status === 'paid') {
                $this->initiateRefund($payment, 'Order cancelled');
                $commande->update(['payment_status' => 'refunded']);
            } elseif ($payment->status === 'pending') {
                $payment->update(['status' => 'cancelled']);
                $commande->update(['payment_status' => 'cancelled']);
                Log::info('Pending payment cancelled for order.', ['order_id' => $commande->id, 'payment_id' => $payment->id]);
            }
        } else {
            $commande->update(['payment_status' => 'cancelled']);
        }
        Log::info('Order cancellation processed', ['order_id' => $commande->id, 'old_status' => $oldStatus->value]);
    }

    /**
     * Handle order refund (full refund scenario)
     */
    private function handleOrderRefund(Commande $commande): void
    {
        if (!in_array($commande->status, [CommandeStatus::ANNULEE])) {
            if (in_array($commande->status, [CommandeStatus::EXPEDIEE, CommandeStatus::LIVREE, CommandeStatus::RETOURNEE])) {
                $this->restoreProductStock($commande);
            }
        }

        if ($commande->paiement) {
            $payment = $commande->paiement->fresh();
            if ($payment->status === 'completed' || $payment->status === 'paid') {
                $this->initiateRefund($payment, 'Order refunded');
            } else {
                Log::warning('Attempted to refund an order whose payment was not completed.', ['order_id' => $commande->id, 'payment_status' => $payment->status]);
            }
        } else {
            Log::warning('No payment record found for refund.', ['order_id' => $commande->id]);
        }
        $commande->update(['payment_status' => 'refunded']);
        Log::info('Order refund processed', ['order_id' => $commande->id]);
    }

    /**
     * Restore product stock for a given order
     */
    private function restoreProductStock(Commande $commande): void
    {
        foreach ($commande->produits as $produitPivot) {
            $produit = Produit::find($produitPivot->id);
            if ($produit) {
                $produit->increment('stock', $produitPivot->pivot->quantite);
                Log::info('Stock restored for product', ['order_id' => $commande->id, 'produit_id' => $produit->id, 'quantite' => $produitPivot->pivot->quantite]);
            }
        }
    }

    /**
     * Send order confirmation email
     */
    public function sendOrderConfirmationEmail(Commande $commande): void
    {
        $recipientEmail = $commande->email_client ?? ($commande->user->email ?? ($commande->client->email ?? null));

        if ($recipientEmail) {
            try {
                Mail::to($recipientEmail)->send(new OrderConfirmationMail($commande));
                Log::info('Order confirmation email sent', ['order_id' => $commande->id, 'recipient' => $recipientEmail]);
            } catch (Exception $e) {
                Log::error('Failed to send order confirmation email', [
                    'order_id' => $commande->id,
                    'recipient' => $recipientEmail,
                    'error' => $e->getMessage()
                ]);
            }
        } else {
            Log::warning('No recipient email for order confirmation', ['order_id' => $commande->id]);
        }
    }

    /**
     * Send order status update email
     */
    public function sendOrderStatusUpdateEmail(Commande $commande, string $oldStatusValue, string $newStatusValue): void
    {
        if ($oldStatusValue === $newStatusValue)
            return;

        $recipientEmail = $commande->email_client ?? ($commande->user->email ?? ($commande->client->email ?? null));

        if ($recipientEmail) {
            try {
                Mail::to($recipientEmail)->send(new OrderStatusUpdateMail($commande, $newStatusValue, $oldStatusValue));
                Log::info('Order status update email sent', [
                    'order_id' => $commande->id,
                    'recipient' => $recipientEmail,
                    'old_status' => $oldStatusValue,
                    'new_status' => $newStatusValue
                ]);
            } catch (Exception $e) {
                Log::error('Failed to send order status update email', [
                    'order_id' => $commande->id,
                    'recipient' => $recipientEmail,
                    'error' => $e->getMessage()
                ]);
            }
        } else {
            Log::warning('No recipient email for order status update', ['order_id' => $commande->id]);
        }
    }

    /**
     * Apply a promotional code to an order amount.
     *
     * @param string|null $code The promotional code.
     * @param float $subtotal The subtotal amount before discount.
     * @return array ['discount_amount' => float, 'applied_code' => string|null]
     */
    public function applyPromoCode(?string $code, float $subtotal): array
    {
        if (!$code) {
            return ['discount_amount' => 0, 'applied_code' => null];
        }

        $discounts = [];
        $discountFilePath = base_path('discount.json');
        if (file_exists($discountFilePath)) {
            $discounts = json_decode(file_get_contents($discountFilePath), true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('Error decoding discount.json: ' . json_last_error_msg());
                $discounts = [];
            }
        } else {
            Log::warning('discount.json not found.');
        }

        if (isset($discounts[$code])) {
            $discount = $discounts[$code];
            $now = Carbon::now();

            if (isset($discount['valid_from']) && $now->lt(Carbon::parse($discount['valid_from']))) {
                return ['discount_amount' => 0, 'applied_code' => null, 'error' => 'Promo code not yet active.'];
            }
            if (isset($discount['valid_to']) && $now->gt(Carbon::parse($discount['valid_to']))) {
                return ['discount_amount' => 0, 'applied_code' => null, 'error' => 'Promo code expired.'];
            }

            if (isset($discount['max_uses']) && ($discount['times_used'] ?? 0) >= $discount['max_uses']) {
                return ['discount_amount' => 0, 'applied_code' => null, 'error' => 'Promo code usage limit reached.'];
            }

            if (isset($discount['min_purchase']) && $subtotal < $discount['min_purchase']) {
                return ['discount_amount' => 0, 'applied_code' => null, 'error' => "Minimum purchase of {$discount['min_purchase']} not met."];
            }

            $discountAmount = 0;
            if ($discount['type'] === 'percentage') {
                $discountAmount = ($subtotal * $discount['value']) / 100;
            } elseif ($discount['type'] === 'fixed') {
                $discountAmount = $discount['value'];
            }

            $discountAmount = min($discountAmount, $subtotal);

            Log::info('Promo code applied', ['code' => $code, 'discount_amount' => $discountAmount]);

            return ['discount_amount' => $discountAmount, 'applied_code' => $code];
        }

        return ['discount_amount' => 0, 'applied_code' => null, 'error' => 'Invalid promo code.'];
    }

    /**
     * Send payment success email
     */
    public function sendPaymentSuccessEmail(Commande $commande, Paiement $paiement): void
    {
        $recipientEmail = $commande->email_commande ?? ($commande->user->email ?? ($commande->client->email ?? null));

        if ($recipientEmail) {
            try {
                Mail::to($recipientEmail)->send(new PaymentSuccessMail($commande, $paiement));
                Log::info('Payment success email sent', ['order_id' => $commande->id, 'payment_id' => $paiement->id, 'recipient' => $recipientEmail]);
            } catch (Exception $e) {
                Log::error('Failed to send payment success email', [
                    'order_id' => $commande->id,
                    'payment_id' => $paiement->id,
                    'recipient' => $recipientEmail,
                    'error' => $e->getMessage()
                ]);
            }
        } else {
            Log::warning('No recipient email for payment success notification', ['order_id' => $commande->id, 'payment_id' => $paiement->id]);
        }
    }

    /**
     * Send payment failed email
     */
    public function sendPaymentFailedEmail(Commande $commande, string $failureReason = 'Erreur de paiement'): void
    {
        $recipientEmail = $commande->email_commande ?? ($commande->user->email ?? ($commande->client->email ?? null));

        if ($recipientEmail) {
            try {
                Mail::to($recipientEmail)->send(new PaymentFailedMail($commande, $failureReason));
                Log::info('Payment failed email sent', ['order_id' => $commande->id, 'recipient' => $recipientEmail, 'reason' => $failureReason]);
            } catch (Exception $e) {
                Log::error('Failed to send payment failed email', [
                    'order_id' => $commande->id,
                    'recipient' => $recipientEmail,
                    'error' => $e->getMessage()
                ]);
            }
        } else {
            Log::warning('No recipient email for payment failure notification', ['order_id' => $commande->id]);
        }
    }
}
