<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;

class UpdateUsersToClients extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:update-to-clients';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update existing users to have the client role and normal client type';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Updating users to have client role...');

        $users = User::all();
        $updatedCount = 0;

        foreach ($users as $user) {
            $updated = false;

            // Add client role if not present
            $roles = $user->roles ?? [];
            if (!in_array('client', $roles)) {
                $roles[] = 'client';
                $user->roles = $roles;
                $updated = true;
            }

            // Set type_client to normal if not set
            if (empty($user->type_client)) {
                $user->type_client = 'normal';
                $updated = true;
            }

            if ($updated) {
                $user->save();
                $updatedCount++;
            }
        }

        $this->info("Updated $updatedCount users.");

        return Command::SUCCESS;
    }
}

