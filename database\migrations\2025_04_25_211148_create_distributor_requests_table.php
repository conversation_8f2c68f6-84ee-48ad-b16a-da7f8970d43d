<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('distributor_requests', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('company_name');
            $table->string('business_type')->nullable();
            $table->text('motivation')->nullable();
            $table->string('website')->nullable();
            $table->string('phone');
            $table->string('address');
            $table->string('city');
            $table->string('postal_code')->nullable();
            $table->string('country');
            $table->string('tax_id')->nullable();
            $table->string('registration_number')->nullable();
            $table->boolean('has_physical_store')->default(false);
            $table->integer('years_in_business')->nullable();
            $table->text('product_categories_interested')->nullable();
            $table->enum('status', ['pending', 'approved', 'rejected'])->default('pending');
            $table->text('admin_notes')->nullable();
            $table->timestamp('processed_at')->nullable();
            $table->foreignId('processed_by')->nullable()->constrained('users');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('distributor_requests');
    }
};
