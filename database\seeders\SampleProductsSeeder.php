<?php

namespace Database\Seeders;

use App\Models\Attribut;
use App\Models\Marque;
use App\Models\Produit;
use App\Models\ProduitValeur;
use App\Models\sous_sousCategorie;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SampleProductsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::beginTransaction();

        try {
            // Create sample products
            $this->createLingeProducts();
            $this->createMeubleProducts();
            
            DB::commit();
            $this->command->info('Sample products created successfully!');
        } catch (\Exception $e) {
            DB::rollBack();
            $this->command->error('Error creating sample products: ' . $e->getMessage());
        }
    }

    /**
     * Create products for the "Linge de lit" category
     */
    private function createLingeProducts(): void
    {
        // Get the sous-sous-categorie
        $sousSousCategorie = sous_sousCategorie::where('nom_sous_sous_categorie', 'Parrure de lit')->first();
        if (!$sousSousCategorie) {
            $this->command->error('Sous-sous-categorie "Parrure de lit" not found');
            return;
        }

        // Get the marque
        $marque = Marque::where('nom_marque', 'Carré Blanc')->first();
        if (!$marque) {
            $this->command->error('Marque "Carré Blanc" not found');
            return;
        }

        // Get attributes
        $couleurAttribut = Attribut::where('nom', 'Couleur')->first();
        $tailleAttribut = Attribut::where('nom', 'Taille')->first();
        $materiauAttribut = Attribut::where('nom', 'Materiau')->first();
        $dimensionsAttribut = Attribut::where('nom', 'Dimensions')->first();

        // Create products
        $products = [
            [
                'nom_produit' => 'Parure de lit Élégance',
                'description_produit' => 'Parure de lit en coton égyptien, douce et élégante pour un sommeil réparateur',
                'image_produit' => 'https://www.carreblanc.com/media/catalog/product/cache/c687aa7517cf01e65c009f6943c2b1e9/2/0/2023-parure-housse-de-couette-percale-coton-blanc-brode-elegance_1.jpg',
                'prix_produit' => 129.99,
                'quantite_produit' => 50,
                'attributs' => [
                    ['attribut' => $couleurAttribut, 'valeur' => 'Blanc'],
                    ['attribut' => $tailleAttribut, 'valeur' => '240x220 cm'],
                    ['attribut' => $materiauAttribut, 'valeur' => 'Coton égyptien'],
                    ['attribut' => $dimensionsAttribut, 'valeur' => '240x220 cm']
                ]
            ],
            [
                'nom_produit' => 'Parure de lit Satin Luxe',
                'description_produit' => 'Parure de lit en satin de coton, luxueuse et brillante pour un confort optimal',
                'image_produit' => 'https://www.carreblanc.com/media/catalog/product/cache/c687aa7517cf01e65c009f6943c2b1e9/2/0/2023-parure-housse-de-couette-satin-coton-blanc-satin-luxe_1.jpg',
                'prix_produit' => 159.99,
                'quantite_produit' => 30,
                'attributs' => [
                    ['attribut' => $couleurAttribut, 'valeur' => 'Blanc cassé'],
                    ['attribut' => $tailleAttribut, 'valeur' => '240x220 cm'],
                    ['attribut' => $materiauAttribut, 'valeur' => 'Satin de coton'],
                    ['attribut' => $dimensionsAttribut, 'valeur' => '240x220 cm']
                ]
            ],
            [
                'nom_produit' => 'Parure de lit Cosy',
                'description_produit' => 'Parure de lit en flanelle, chaude et douce pour les nuits d\'hiver',
                'image_produit' => 'https://www.carreblanc.com/media/catalog/product/cache/c687aa7517cf01e65c009f6943c2b1e9/2/0/2023-parure-housse-de-couette-flanelle-gris-cosy_1.jpg',
                'prix_produit' => 99.99,
                'quantite_produit' => 40,
                'attributs' => [
                    ['attribut' => $couleurAttribut, 'valeur' => 'Gris'],
                    ['attribut' => $tailleAttribut, 'valeur' => '240x220 cm'],
                    ['attribut' => $materiauAttribut, 'valeur' => 'Flanelle'],
                    ['attribut' => $dimensionsAttribut, 'valeur' => '240x220 cm']
                ]
            ]
        ];

        foreach ($products as $productData) {
            $product = new Produit([
                'nom_produit' => $productData['nom_produit'],
                'description_produit' => $productData['description_produit'],
                'image_produit' => $productData['image_produit'],
                'prix_produit' => $productData['prix_produit'],
                'quantite_produit' => $productData['quantite_produit'],
                'marque_id' => $marque->id,
                'sous_sous_categorie_id' => $sousSousCategorie->id
            ]);
            $product->save();

            // Add attributes
            foreach ($productData['attributs'] as $attributData) {
                if ($attributData['attribut']) {
                    $colonne = 'valeur_' . $attributData['attribut']->type_valeur;
                    ProduitValeur::create([
                        'produit_id' => $product->id,
                        'attribut_id' => $attributData['attribut']->id,
                        $colonne => $attributData['valeur']
                    ]);
                }
            }
        }
    }

    /**
     * Create products for the "Meubles" category
     */
    private function createMeubleProducts(): void
    {
        // Get the sous-sous-categorie (we'll use a different one if available)
        $sousSousCategorie = sous_sousCategorie::where('nom_sous_sous_categorie', 'Parrure-wire')->first();
        if (!$sousSousCategorie) {
            $this->command->error('Sous-sous-categorie "Parrure-wire" not found');
            return;
        }

        // Get the marque
        $marque = Marque::where('nom_marque', 'J-Line')->first();
        if (!$marque) {
            $this->command->error('Marque "J-Line" not found');
            return;
        }

        // Get attributes
        $couleurAttribut = Attribut::where('nom', 'Couleur')->first();
        $tailleAttribut = Attribut::where('nom', 'Taille')->first();
        $materiauAttribut = Attribut::where('nom', 'Materiau')->first();
        $dimensionsAttribut = Attribut::where('nom', 'Dimensions')->first();

        // Create products
        $products = [
            [
                'nom_produit' => 'Table basse scandinave',
                'description_produit' => 'Table basse en bois de chêne massif, design scandinave épuré',
                'image_produit' => 'https://www.j-line.be/media/images/HiRes/52511.jpg',
                'prix_produit' => 299.99,
                'quantite_produit' => 15,
                'attributs' => [
                    ['attribut' => $couleurAttribut, 'valeur' => 'Chêne naturel'],
                    ['attribut' => $tailleAttribut, 'valeur' => 'Standard'],
                    ['attribut' => $materiauAttribut, 'valeur' => 'Chêne massif'],
                    ['attribut' => $dimensionsAttribut, 'valeur' => '120x60x45 cm']
                ]
            ],
            [
                'nom_produit' => 'Étagère murale industrielle',
                'description_produit' => 'Étagère murale en métal et bois, style industriel',
                'image_produit' => 'https://www.j-line.be/media/images/HiRes/50802.jpg',
                'prix_produit' => 149.99,
                'quantite_produit' => 25,
                'attributs' => [
                    ['attribut' => $couleurAttribut, 'valeur' => 'Noir et bois'],
                    ['attribut' => $tailleAttribut, 'valeur' => 'Large'],
                    ['attribut' => $materiauAttribut, 'valeur' => 'Métal et bois'],
                    ['attribut' => $dimensionsAttribut, 'valeur' => '180x30x90 cm']
                ]
            ],
            [
                'nom_produit' => 'Fauteuil velours',
                'description_produit' => 'Fauteuil confortable en velours, design contemporain',
                'image_produit' => 'https://www.j-line.be/media/categories/wall-decoration-9296.jpeg',
                'prix_produit' => 349.99,
                'quantite_produit' => 10,
                'attributs' => [
                    ['attribut' => $couleurAttribut, 'valeur' => 'Bleu canard'],
                    ['attribut' => $tailleAttribut, 'valeur' => 'Standard'],
                    ['attribut' => $materiauAttribut, 'valeur' => 'Velours'],
                    ['attribut' => $dimensionsAttribut, 'valeur' => '75x85x95 cm']
                ]
            ]
        ];

        foreach ($products as $productData) {
            $product = new Produit([
                'nom_produit' => $productData['nom_produit'],
                'description_produit' => $productData['description_produit'],
                'image_produit' => $productData['image_produit'],
                'prix_produit' => $productData['prix_produit'],
                'quantite_produit' => $productData['quantite_produit'],
                'marque_id' => $marque->id,
                'sous_sous_categorie_id' => $sousSousCategorie->id
            ]);
            $product->save();

            // Add attributes
            foreach ($productData['attributs'] as $attributData) {
                if ($attributData['attribut']) {
                    $colonne = 'valeur_' . $attributData['attribut']->type_valeur;
                    ProduitValeur::create([
                        'produit_id' => $product->id,
                        'attribut_id' => $attributData['attribut']->id,
                        $colonne => $attributData['valeur']
                    ]);
                }
            }
        }
    }
}
