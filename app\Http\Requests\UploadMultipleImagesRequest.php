<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UploadMultipleImagesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'model_type' => 'required|string|in:produit,categorie,sous_categorie,sous_sous_categorie,collection,marque,produit_variante,carousel_slide',
            'model_id' => 'required|integer',
            'images' => 'required|array',
            'images.*' => 'required|image|max:10240', // 10MB max per image
            'alt_text' => 'nullable|string|max:255',
            'title' => 'nullable|string|max:255',
        ];
    }

    public function messages(): array
    {
        return [
            'model_type.required' => 'Le type de modèle est obligatoire.',
            'model_type.in' => 'Le type de modèle est invalide.',
            'model_id.required' => "L'identifiant du modèle est obligatoire.",
            'model_id.integer' => "L'identifiant du modèle doit être un entier.",
            'images.required' => 'Les images sont obligatoires.',
            'images.array' => 'Le champ images doit être un tableau.',
            'images.*.required' => 'Chaque image est obligatoire.',
            'images.*.image' => 'Chaque fichier doit être une image.',
            'images.*.max' => 'Chaque image ne doit pas dépasser 10 Mo.',
            'alt_text.max' => 'Le texte alternatif ne doit pas dépasser 255 caractères.',
            'title.max' => 'Le titre ne doit pas dépasser 255 caractères.',
        ];
    }
}
