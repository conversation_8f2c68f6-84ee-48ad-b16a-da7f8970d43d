<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RegleRemise extends Model
{
    use HasFactory;

    protected $fillable = [
        'nom',
        'description',
        'type_client',
        'valeur',
        'type',
        'priorité',
        'active',
        'conditions_supplementaires',
    ];

    protected $casts = [
        'conditions_supplementaires' => 'array',
        'active' => 'boolean',
    ];

    public function calculerRemise($montant)
    {
        if (!$this->active) {
            return 0;
        }

        switch ($this->type) {
            case 'pourcentage':
                return $montant * ($this->valeur / 100);
            case 'montant_fixe':
                return min($montant, $this->valeur);
            default:
                return 0;
        }
    }
}
