<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use App\Models\Produit;
use App\Models\ProduitVariante;

class Panier extends Model
{
    protected $table = 'paniers';

    protected $fillable = ['client_id', 'guest_id'];

    /**
     * Relation avec le client (utilisateur)
     */
    public function client()
    {
        return $this->belongsTo(User::class, 'client_id');
    }

    /**
     * Relation avec les items du panier
     */
    public function items()
    {
        return $this->hasMany(PanierItem::class);
    }

    /**
     * Calcule le total du panier
     */
    public function getTotal()
    {
        return $this->items->sum(function ($item) {
            return $item->prix_unitaire * $item->quantite;
        });
    }

    /**
     * Ajoute un produit au panier
     */
    public function addItem($produitId, $quantite = 1, $varianteId = null)
    {
        // Récupérer le produit
        $produit = Produit::findOrFail($produitId);

        // Déterminer le prix
        $prix = $produit->prix_produit;

        // Si une variante est spécifiée, ajouter le supplément de prix
        if ($varianteId) {
            $variante = ProduitVariante::findOrFail($varianteId);
            $prix += $variante->prix_supplement;

            // Vérifier le stock de la variante
            if ($variante->stock < $quantite) {
                throw new \Exception("Stock insuffisant pour cette variante");
            }
        } else {
            // Vérifier le stock du produit
            if ($produit->quantite_produit < $quantite) {
                throw new \Exception("Stock insuffisant pour ce produit");
            }
        }

        // Vérifier si l'item existe déjà dans le panier
        $item = $this->items()
            ->where('produit_id', $produitId)
            ->where('variante_id', $varianteId)
            ->first();

        if ($item) {
            // Mettre à jour la quantité
            $item->quantite += $quantite;
            $item->save();
        } else {
            // Créer un nouvel item
            $this->items()->create([
                'produit_id' => $produitId,
                'variante_id' => $varianteId,
                'quantite' => $quantite,
                'prix_unitaire' => $prix
            ]);
        }

        return $this;
    }

    /**
     * Met à jour la quantité d'un item
     */
    public function updateItem($itemId, $quantite)
    {
        try {
            // Vérifier si l'item appartient à ce panier
            $item = $this->items()->where('id', $itemId)->first();

            if (!$item) {
                throw new \Exception("L'item #$itemId n'appartient pas à ce panier (panier #{$this->id})");
            }

            // Vérifier le stock
            if ($item->variante_id) {
                $variante = $item->variante;
                if (!$variante) {
                    throw new \Exception("La variante associée à cet item n'existe plus");
                }
                if ($variante->stock < $quantite) {
                    throw new \Exception("Stock insuffisant pour cette variante (disponible: {$variante->stock}, demandé: $quantite)");
                }
            } else {
                $produit = $item->produit;
                if (!$produit) {
                    throw new \Exception("Le produit associé à cet item n'existe plus");
                }
                if ($produit->quantite_produit < $quantite) {
                    throw new \Exception("Stock insuffisant pour ce produit (disponible: {$produit->quantite_produit}, demandé: $quantite)");
                }
            }

            if ($quantite <= 0) {
                $item->delete();
            } else {
                $item->quantite = $quantite;
                $item->save();
            }

            return $this;
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            throw new \Exception("L'item #$itemId n'a pas été trouvé");
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * Supprime un item du panier
     */
    public function removeItem($itemId)
    {
        try {
            // Vérifier si l'item appartient à ce panier
            $item = $this->items()->where('id', $itemId)->first();

            if (!$item) {
                throw new \Exception("L'item #$itemId n'appartient pas à ce panier (panier #{$this->id})");
            }

            $item->delete();
            return $this;
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            throw new \Exception("L'item #$itemId n'a pas été trouvé");
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * Vide le panier
     */
    public function clear()
    {
        $this->items()->delete();
        return $this;
    }

    /**
     * Fusionne un panier anonyme avec le panier d'un client
     */
    public static function mergeCarts($guestId, $clientId)
    {
        $guestCart = self::where('guest_id', $guestId)->first();
        $clientCart = self::where('client_id', $clientId)->first();

        if (!$guestCart) {
            return $clientCart ?? self::create(['client_id' => $clientId]);
        }

        if (!$clientCart) {
            $guestCart->update(['client_id' => $clientId, 'guest_id' => null]);
            return $guestCart;
        }

        // Fusionner les items
        foreach ($guestCart->items as $item) {
            try {
                $clientCart->addItem($item->produit_id, $item->quantite, $item->variante_id);
            } catch (\Exception $e) {
                // Ignorer les erreurs de stock
                continue;
            }
        }

        // Supprimer l'ancien panier
        $guestCart->delete();

        return $clientCart;
    }

    /**
     * Génère un nouvel identifiant unique pour un invité
     */
    public static function generateGuestId()
    {
        return (string) \Illuminate\Support\Str::uuid();
    }
}
