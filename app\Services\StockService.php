<?php

namespace App\Services;

use App\Models\Produit;
use App\Models\StockHistorique;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class StockService
{
    /**
     * Ajouter du stock à un produit
     *
     * @param int $produitId ID du produit
     * @param int $quantite Quantité à ajouter (doit être positive)
     * @param string|null $reference Référence externe du mouvement
     * @param string|null $commentaire Commentaire sur le mouvement
     * @return StockHistorique
     */
    public function ajouterStock(int $produitId, int $quantite, ?string $reference = null, ?string $commentaire = null): StockHistorique
    {
        if ($quantite <= 0) {
            throw new \InvalidArgumentException("La quantité à ajouter doit être positive");
        }

        return $this->modifierStock($produitId, $quantite, StockHistorique::TYPE_ENTREE, $reference, $commentaire);
    }

    /**
     * Retirer du stock d'un produit
     *
     * @param int $produitId ID du produit
     * @param int $quantite Quantité à retirer (doit être positive)
     * @param string|null $reference Référence externe du mouvement
     * @param string|null $commentaire Commentaire sur le mouvement
     * @return StockHistorique
     */
    public function retirerStock(int $produitId, int $quantite, ?string $reference = null, ?string $commentaire = null): StockHistorique
    {
        if ($quantite <= 0) {
            throw new \InvalidArgumentException("La quantité à retirer doit être positive");
        }

        return $this->modifierStock($produitId, -$quantite, StockHistorique::TYPE_SORTIE, $reference, $commentaire);
    }

    /**
     * Ajuster le stock d'un produit à une valeur spécifique
     *
     * @param int $produitId ID du produit
     * @param int $nouvelleQuantite Nouvelle quantité totale
     * @param string|null $commentaire Commentaire sur l'ajustement
     * @return StockHistorique
     */
    public function ajusterStock(int $produitId, int $nouvelleQuantite, ?string $commentaire = null): StockHistorique
    {
        if ($nouvelleQuantite < 0) {
            throw new \InvalidArgumentException("La nouvelle quantité ne peut pas être négative");
        }

        $produit = Produit::findOrFail($produitId);
        $quantiteModifiee = $nouvelleQuantite - $produit->quantite_produit;

        return $this->modifierStock(
            $produitId,
            $quantiteModifiee,
            StockHistorique::TYPE_AJUSTEMENT,
            null,
            $commentaire ?: "Ajustement manuel du stock à {$nouvelleQuantite}"
        );
    }

    /**
     * Enregistrer un mouvement de stock lié à une commande
     *
     * @param int $produitId ID du produit
     * @param int $quantite Quantité à retirer (doit être positive)
     * @param string $referenceCommande Référence de la commande
     * @return StockHistorique
     */
    public function retirerStockCommande(int $produitId, int $quantite, string $referenceCommande): StockHistorique
    {
        if ($quantite <= 0) {
            throw new \InvalidArgumentException("La quantité à retirer doit être positive");
        }

        return $this->modifierStock(
            $produitId,
            -$quantite,
            StockHistorique::TYPE_COMMANDE,
            $referenceCommande,
            "Commande #{$referenceCommande}"
        );
    }

    /**
     * Enregistrer un mouvement de stock lié à un retour
     *
     * @param int $produitId ID du produit
     * @param int $quantite Quantité à ajouter (doit être positive)
     * @param string $referenceRetour Référence du retour
     * @param string|null $commentaire Commentaire sur le retour
     * @return StockHistorique
     */
    public function ajouterStockRetour(int $produitId, int $quantite, string $referenceRetour, ?string $commentaire = null): StockHistorique
    {
        if ($quantite <= 0) {
            throw new \InvalidArgumentException("La quantité à ajouter doit être positive");
        }

        return $this->modifierStock(
            $produitId,
            $quantite,
            StockHistorique::TYPE_RETOUR,
            $referenceRetour,
            $commentaire ?: "Retour #{$referenceRetour}"
        );
    }

    /**
     * Vérifier si un produit a suffisamment de stock
     *
     * @param int $produitId ID du produit
     * @param int $quantite Quantité requise
     * @return bool
     */
    public function verifierStockDisponible(int $produitId, int $quantite): bool
    {
        $produit = Produit::findOrFail($produitId);
        return $produit->quantite_produit >= $quantite;
    }

    /**
     * Obtenir les produits en rupture de stock
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getProduitsEnRupture()
    {
        return Produit::where('quantite_produit', '<=', 0)->get();
    }

    /**
     * Obtenir les produits en stock limité
     *
     * @param int $seuilLimite Seuil en dessous duquel le stock est considéré comme limité
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getProduitsStockLimite(int $seuilLimite = 5)
    {
        return Produit::where('quantite_produit', '>', 0)
            ->where('quantite_produit', '<=', $seuilLimite)
            ->get();
    }

    /**
     * Méthode interne pour modifier le stock d'un produit
     *
     * @param int $produitId ID du produit
     * @param int $quantiteModifiee Quantité à ajouter (positive) ou retirer (négative)
     * @param string $typeMouvement Type de mouvement
     * @param string|null $reference Référence externe du mouvement
     * @param string|null $commentaire Commentaire sur le mouvement
     * @return StockHistorique
     */
    protected function modifierStock(
        int $produitId,
        int $quantiteModifiee,
        string $typeMouvement,
        ?string $reference = null,
        ?string $commentaire = null
    ): StockHistorique {
        DB::beginTransaction();

        try {
            $produit = Produit::lockForUpdate()->findOrFail($produitId);
            $quantiteAvant = $produit->quantite_produit;
            $quantiteApres = $quantiteAvant + $quantiteModifiee;

            // Vérifier que le stock ne devient pas négatif (sauf pour les ajustements)
            if ($typeMouvement !== StockHistorique::TYPE_AJUSTEMENT && $quantiteApres < 0) {
                throw new \InvalidArgumentException("Stock insuffisant. Stock actuel: {$quantiteAvant}, Quantité demandée: " . abs($quantiteModifiee));
            }

            // Mettre à jour le stock du produit
            $produit->quantite_produit = $quantiteApres;
            $produit->save();

            // Créer l'historique de mouvement
            $historique = StockHistorique::create([
                'produit_id' => $produitId,
                'quantite_avant' => $quantiteAvant,
                'quantite_apres' => $quantiteApres,
                'quantite_modifiee' => $quantiteModifiee,
                'type_mouvement' => $typeMouvement,
                'reference_mouvement' => $reference,
                'user_id' => Auth::id(),
                'commentaire' => $commentaire
            ]);

            DB::commit();
            return $historique;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
