<?php

namespace App\Http\Controllers;

use App\Models\Attribut;
use Illuminate\Http\Request;
use App\Models\Produit;
use App\Models\Promotion;
use App\Models\ProduitValeur;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class ProduitController extends Controller
{
    /**
     * Afficher la liste des produits avec filtres et pagination
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        // Code existant...
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            // 🛠️ Validation des données
            $validatedData = $request->validate([
                "nom_produit" => "required|string|max:255",
                "description_produit" => "nullable|string",
                "prix_produit" => "required|numeric|min:0",
                "image_produit" => "nullable|url",
                "quantite_produit" => "required|integer|min:1",
                "marque_id" => "required|exists:marques,id",
                "sous_sous_categorie_id" => "required|exists:sous_sous_categories,id"
            ]);
            
            // Tronquer la description si elle dépasse 255 caractères
            if (isset($validatedData['description_produit']) && strlen($validatedData['description_produit']) > 255) {
                $validatedData['description_produit'] = substr($validatedData['description_produit'], 0, 252) . '...';
            }

            // 🚀 Création et sauvegarde du produit
            $produit = Produit::create($validatedData);

            // ✅ Retourner le produit inséré avec un statut 201 (Created)
            return response()->json($produit, 201);

        } catch (\Exception $e) {
            // ❌ Capture de l'erreur et retour d'un message clair
            return response()->json(["error" => "Insertion impossible", "message" => $e->getMessage()], 500);
        }
    }

    // Autres méthodes...
}
