<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('paiements', function (Blueprint $table) {
            // Add new columns first
            if (!Schema::hasColumn('paiements', 'montant')) {
                $table->decimal('montant', 10, 2)->default(0)->after('methode_paiement');
            }
            if (!Schema::hasColumn('paiements', 'status')) {
                $table->string('status')->after('montant');
            }
            if (!Schema::hasColumn('paiements', 'processed_at')) {
                $table->timestamp('processed_at')->nullable()->after('status');
            }
            if (!Schema::hasColumn('paiements', 'transaction_id')) {
                $table->string('transaction_id')->nullable()->after('processed_at');
            }
            if (!Schema::hasColumn('paiements', 'gateway_response')) {
                $table->json('gateway_response')->nullable()->after('transaction_id');
            }
        });

        // Copy data from old columns to new columns
        DB::statement("
            UPDATE paiements
            SET
                montant = CASE
                    WHEN montant_paiement ~ '^[0-9]+\.?[0-9]*$' THEN montant_paiement::decimal(10,2)
                    ELSE 0
                END,
                status = statut_paiement,
                processed_at = CASE
                    WHEN date_paiement ~ '^[0-9]{4}-[0-9]{2}-[0-9]{2}' THEN date_paiement::timestamp
                    ELSE NOW()
                END
        ");

        // Drop old columns
        Schema::table('paiements', function (Blueprint $table) {
            $table->dropColumn(['montant_paiement', 'statut_paiement', 'date_paiement']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('paiements', function (Blueprint $table) {
            // Add back old columns
            $table->string('montant_paiement')->after('methode_paiement');
            $table->string('statut_paiement')->after('montant_paiement');
            $table->string('date_paiement')->after('statut_paiement');
        });

        // Copy data back
        DB::statement("
            UPDATE paiements
            SET
                montant_paiement = montant::text,
                statut_paiement = status,
                date_paiement = processed_at::text
        ");

        // Drop new columns
        Schema::table('paiements', function (Blueprint $table) {
            $table->dropColumn(['montant', 'status', 'processed_at', 'transaction_id', 'gateway_response']);
        });
    }
};
