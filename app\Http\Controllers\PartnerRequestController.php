<?php

namespace App\Http\Controllers;

use App\Models\PartnerRequest;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Http\Requests\StorePartnerRequestRequest;

class PartnerRequestController extends Controller
{
    /**
     * Display partner requests
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        if ($user) {
            // For authenticated users, get their requests
            $requests = $user->partnerRequests()->latest()->get();

            return response()->json([
                'status' => 'success',
                'data' => $requests
            ]);
        } else {
            // For unauthenticated users
            if ($request->has('email')) {
                // If email is provided, filter by email
                $validator = Validator::make($request->all(), [
                    'email' => 'required|email'
                ]);

                if ($validator->fails()) {
                    return response()->json([
                        'status' => 'success',
                        'data' => [] // Return empty array for invalid email
                    ]);
                }

                $requests = PartnerRequest::where('email', $request->email)
                    ->latest()
                    ->get();
            } else {
                // If no email is provided, return all requests
                $requests = PartnerRequest::latest()->get();
            }

            return response()->json([
                'status' => 'success',
                'data' => $requests
            ]);
        }
    }

    /**
     * Submit a new partner request
     */
    public function store(StorePartnerRequestRequest $request)
    {
        $user = Auth::user();

        // Check if user already has a pending request or is already a partner
        if ($user) {
            $pendingRequest = $user->partnerRequests()->pending()->first();
            if ($pendingRequest) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Vous avez déjà une demande de partenariat en attente'
                ], 400);
            }
            if ($user->hasRole('partenaire')) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Vous êtes déjà un partenaire'
                ], 400);
            }
        }

        $data = $request->validated();
        $partnerRequest = new PartnerRequest($data);

        // Set user_id if authenticated
        if ($user) {
            $partnerRequest->user_id = $user->id;
        } else {
            $partnerRequest->user_id = null;
            $partnerRequest->email = $data['email'];
            $partnerRequest->name = $data['name'];
        }

        $partnerRequest->status = 'pending';
        $partnerRequest->save();

        return response()->json([
            'status' => 'success',
            'message' => 'Demande de partenariat soumise avec succès',
            'data' => $partnerRequest
        ], 201);
    }

    /**
     * Display the specified partner request
     */
    public function show(Request $request, string $id)
    {
        $partnerRequest = PartnerRequest::find($id);

        if (!$partnerRequest) {
            return response()->json([
                'status' => 'error',
                'message' => 'Demande de partenariat non trouvée'
            ], 404);
        }

        $user = Auth::user();

        // If user is authenticated, check if the request belongs to them
        if ($user && $partnerRequest->user_id) {
            if ($partnerRequest->user_id !== $user->id) {
                // For authenticated users, only allow them to see their own requests
                return response()->json([
                    'status' => 'error',
                    'message' => 'Vous n\'êtes pas autorisé à voir cette demande'
                ], 403);
            }
        } elseif (!$user && $partnerRequest->email) {
            // For unauthenticated users, check if they provided the correct email
            $validator = Validator::make($request->all(), [
                'email' => 'required|email'
            ]);

            if ($validator->fails() || $request->email !== $partnerRequest->email) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Vous n\'êtes pas autorisé à voir cette demande'
                ], 403);
            }
        }

        return response()->json([
            'status' => 'success',
            'data' => $partnerRequest
        ]);
    }

    /**
     * Get the status of the latest partner request
     */
    public function status(Request $request)
    {
        $user = Auth::user();

        if ($user) {
            // For authenticated users, get their latest request
            $latestRequest = $user->partnerRequests()->latest()->first();

            if (!$latestRequest) {
                return response()->json([
                    'status' => 'success',
                    'data' => [
                        'has_request' => false,
                        'is_partner' => $user->hasRole('partenaire')
                    ]
                ]);
            }

            return response()->json([
                'status' => 'success',
                'data' => [
                    'has_request' => true,
                    'is_partner' => $user->hasRole('partenaire'),
                    'request' => [
                        'id' => $latestRequest->id,
                        'status' => $latestRequest->status,
                        'created_at' => $latestRequest->created_at,
                        'processed_at' => $latestRequest->processed_at
                    ]
                ]
            ]);
        } else {
            // For unauthenticated users, check by email
            $validator = Validator::make($request->all(), [
                'email' => 'required|email'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Email is required to check status',
                    'errors' => $validator->errors()
                ], 422);
            }

            $latestRequest = PartnerRequest::where('email', $request->email)
                ->latest()
                ->first();

            if (!$latestRequest) {
                return response()->json([
                    'status' => 'success',
                    'data' => [
                        'has_request' => false,
                        'is_partner' => false
                    ]
                ]);
            }

            return response()->json([
                'status' => 'success',
                'data' => [
                    'has_request' => true,
                    'is_partner' => false,
                    'request' => [
                        'id' => $latestRequest->id,
                        'status' => $latestRequest->status,
                        'created_at' => $latestRequest->created_at,
                        'processed_at' => $latestRequest->processed_at
                    ]
                ]
            ]);
        }
    }
}
