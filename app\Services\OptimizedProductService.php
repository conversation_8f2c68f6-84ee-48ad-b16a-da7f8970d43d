<?php

namespace App\Services;

use App\Models\Produit;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class OptimizedProductService
{
    /**
     * Get products with optimized queries and caching
     */
    public function getProductsOptimized($page = 1, $perPage = 20)
    {
        $cacheKey = "products_optimized_page_{$page}_per_{$perPage}";

        return Cache::remember($cacheKey, 3600, function () use ($page, $perPage) {
            // Use selective field loading and optimized eager loading
            return Produit::select([
                'id',
                'nom_produit',
                'description_produit',
                'prix_produit',
                'quantite_produit',
                'marque_id',
                'image_produit'
            ])
            ->with([
                'marque:id,nom_marque',
                'images' => function($query) {
                    $query->select('id', 'produit_id', 'url', 'type')
                          ->where('type', 'principale')
                          ->limit(1);
                }
            ])
            ->paginate($perPage, ['*'], 'page', $page);
        });
    }

    /**
     * Get single product with optimized loading
     */
    public function getProductOptimized($id)
    {
        $cacheKey = "product_optimized_{$id}";

        return Cache::remember($cacheKey, 3600, function () use ($id) {
            return Produit::select([
                'id',
                'nom_produit',
                'description_produit',
                'prix_produit',
                'quantite_produit',
                'marque_id',
                'image_produit',
                'reference'
            ])
            ->with([
                'marque:id,nom_marque',
                'images:id,produit_id,url,type',
                'promotions' => function($query) {
                    $query->select(['promotions.id', 'nom', 'type', 'valeur', 'date_debut', 'date_fin'])
                          ->where('date_debut', '<=', now())
                          ->where('date_fin', '>=', now());
                }
            ])
            ->findOrFail($id);
        });
    }

    /**
     * Search products with optimized queries
     */
    public function searchProductsOptimized($searchTerm, $page = 1, $perPage = 20)
    {
        $cacheKey = "products_search_" . md5($searchTerm) . "_page_{$page}_per_{$perPage}";

        return Cache::remember($cacheKey, 1800, function () use ($searchTerm, $page, $perPage) {
            return Produit::select([
                'id',
                'nom_produit',
                'description_produit',
                'prix_produit',
                'quantite_produit',
                'marque_id'
            ])
            ->where('nom_produit', 'ILIKE', "%{$searchTerm}%")
            ->orWhere('description_produit', 'ILIKE', "%{$searchTerm}%")
            ->with(['marque:id,nom_marque'])
            ->paginate($perPage, ['*'], 'page', $page);
        });
    }

    /**
     * Get products by category with optimized loading
     */
    public function getProductsByCategoryOptimized($categoryId, $page = 1, $perPage = 20)
    {
        $cacheKey = "products_category_{$categoryId}_page_{$page}_per_{$perPage}";

        return Cache::remember($cacheKey, 3600, function () use ($categoryId, $page, $perPage) {
            return Produit::select([
                'id',
                'nom_produit',
                'prix_produit',
                'quantite_produit',
                'marque_id',
                'image_produit'
            ])
            ->where('sous_sous_categorie_id', $categoryId)
            ->where('quantite_produit', '>', 0)
            ->with(['marque:id,nom_marque'])
            ->orderBy('nom_produit')
            ->paginate($perPage, ['*'], 'page', $page);
        });
    }

    /**
     * Get featured products with caching
     */
    public function getFeaturedProductsOptimized($limit = 10)
    {
        $cacheKey = "featured_products_optimized_{$limit}";

        return Cache::remember($cacheKey, 7200, function () use ($limit) {
            return Produit::select([
                'id',
                'nom_produit',
                'prix_produit',
                'image_produit',
                'marque_id'
            ])
            ->where('quantite_produit', '>', 0)
            ->with(['marque:id,nom_marque'])
            ->inRandomOrder()
            ->limit($limit)
            ->get();
        });
    }

    /**
     * Clear product caches
     */
    public function clearProductCaches($productId = null)
    {
        if ($productId) {
            Cache::forget("product_optimized_{$productId}");
        }

        // Clear paginated caches (this is a simplified approach)
        for ($page = 1; $page <= 10; $page++) {
            Cache::forget("products_optimized_page_{$page}_per_20");
            Cache::forget("featured_products_optimized_10");
        }
    }

    /**
     * Warm up critical caches
     */
    public function warmUpCaches()
    {
        // Warm up first page of products
        $this->getProductsOptimized(1, 20);

        // Warm up featured products
        $this->getFeaturedProductsOptimized(10);

        return "Caches warmed up successfully";
    }

    /**
     * Get performance statistics
     */
    public function getPerformanceStats()
    {
        $start = microtime(true);

        // Test optimized query
        $products = $this->getProductsOptimized(1, 10);

        $optimizedTime = (microtime(true) - $start) * 1000;

        $start = microtime(true);

        // Test unoptimized query
        $unoptimizedProducts = Produit::with(['marque', 'images', 'promotions'])->take(10)->get();

        $unoptimizedTime = (microtime(true) - $start) * 1000;

        return [
            'optimized_time_ms' => round($optimizedTime, 2),
            'unoptimized_time_ms' => round($unoptimizedTime, 2),
            'improvement_factor' => round($unoptimizedTime / $optimizedTime, 2),
            'cache_status' => Cache::getStore()->getPrefix() ? 'Active' : 'Inactive'
        ];
    }
}
