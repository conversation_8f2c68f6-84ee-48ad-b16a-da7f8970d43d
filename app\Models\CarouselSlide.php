<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class CarouselSlide extends Model
{
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'carousel_id',
        'titre',
        'description',
        'bouton_texte',
        'bouton_lien',
        'ordre',
        'actif'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'actif' => 'boolean',
        'ordre' => 'integer',
    ];

    /**
     * Get the carousel that owns the slide.
     */
    public function carousel(): BelongsTo
    {
        return $this->belongsTo(Carousel::class);
    }

    /**
     * Get all images for this slide
     */
    public function images(): MorphMany
    {
        return $this->morphMany(Image::class, 'imageable')->orderBy('order');
    }

    /**
     * Get the primary image for this slide
     */
    public function getPrimaryImageAttribute()
    {
        return $this->images()->where('is_primary', true)->first() ?? $this->images()->first();
    }

    /**
     * Get the primary image URL for this slide
     */
    public function getPrimaryImageUrlAttribute()
    {
        if ($this->primaryImage) {
            return $this->primaryImage->url;
        }

        return null;
    }

    /**
     * Scope a query to only include active slides.
     */
    public function scopeActif($query)
    {
        return $query->where('actif', true);
    }
}
