<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add groupe_client_id field
        Schema::table('users', function (Blueprint $table) {
            if (!Schema::hasColumn('users', 'groupe_client_id')) {
                $table->foreignId('groupe_client_id')->nullable()->after('point_de_vente_id')
                    ->constrained('groupes_clients')->nullOnDelete();
            }
        });

        // Handle database-specific constraint modifications
        $driver = DB::getDriverName();

        if ($driver === 'pgsql') {
            // For PostgreSQL, we need to use raw SQL to modify the enum type
            // First, drop the constraint
            DB::statement("ALTER TABLE users DROP CONSTRAINT IF EXISTS users_type_client_check");

            // Then update the check constraint with the new values
            DB::statement("ALTER TABLE users ADD CONSTRAINT users_type_client_check CHECK (type_client IN ('normal', 'partenaire', 'point_de_vente', 'groupe'))");
        }
        // For SQLite and other databases, Laravel's schema builder handles enum-like constraints automatically
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the foreign key constraint and column
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['groupe_client_id']);
            $table->dropColumn('groupe_client_id');
        });

        // Handle database-specific constraint modifications
        $driver = DB::getDriverName();

        if ($driver === 'pgsql') {
            // For PostgreSQL, revert the enum type
            DB::statement("ALTER TABLE users DROP CONSTRAINT IF EXISTS users_type_client_check");
            DB::statement("ALTER TABLE users ADD CONSTRAINT users_type_client_check CHECK (type_client IN ('normal', 'partenaire', 'point_de_vente'))");
        }
        // For SQLite and other databases, Laravel's schema builder handles enum-like constraints automatically
    }
};
