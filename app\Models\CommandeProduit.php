<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Relations\Pivot;

class CommandeProduit extends Pivot
{
    // Nom de la table si différent de la convention Laravel
    protected $table = 'commande_produit';

    // Attributs remplissables
    protected $fillable = [
        'commande_id',
        'produit_id',
        'quantite',
        'prix_unitaire'
    ];

    // Relations si nécessaire
    public function commande()
    {
        return $this->belongsTo(Commande::class);
    }

    public function produit()
    {
        return $this->belongsTo(Produit::class);
    }

}
