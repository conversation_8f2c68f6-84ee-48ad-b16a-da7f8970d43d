<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class ContactControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /**
     * Test the info endpoint.
     *
     * @return void
     */
    public function test_info_endpoint_returns_correct_data()
    {
        $response = $this->getJson('/api/contact/info');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'fields' => [
                        'name' => ['required', 'min_length', 'max_length', 'type'],
                        'email' => ['required', 'max_length', 'type'],
                        'message' => ['required', 'min_length', 'max_length', 'type'],
                    ],
                    'contact_email',
                ]
            ])
            ->assertJson([
                'data' => [
                    'contact_email' => '<EMAIL>'
                ]
            ]);
    }

    /**
     * Test valid submission to the submit endpoint.
     *
     * @return void
     */
    public function test_submit_endpoint_with_valid_data()
    {
        Mail::fake();

        $data = [
            'name' => $this->faker->name,
            'email' => $this->faker->safeEmail,
            'message' => $this->faker->paragraph,
        ];

        $response = $this->postJson('/api/contact/submit', $data);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'message',
                    'submitted_at'
                ]
            ])
            ->assertJson([
                'data' => [
                    'message' => 'Votre message a été envoyé avec succès. Nous vous répondrons dans les plus brefs délais.'
                ]
            ]);

        Mail::assertSent(function (\App\Mail\ContactFormMail $mail) use ($data) {
            return $mail->hasTo('<EMAIL>') &&
                $mail->contactData['name'] === $data['name'] &&
                $mail->contactData['email'] === $data['email'];
        });
    }

    /**
     * Test submission with missing name.
     *
     * @return void
     */
    public function test_submit_endpoint_missing_name()
    {
        $data = [
            'email' => $this->faker->safeEmail,
            'message' => $this->faker->paragraph,
        ];

        $response = $this->postJson('/api/contact/submit', $data);

        $response->assertStatus(422)
            ->assertJsonValidationErrors('name');
    }

    /**
     * Test submission with invalid email.
     *
     * @return void
     */
    public function test_submit_endpoint_invalid_email()
    {
        $data = [
            'name' => $this->faker->name,
            'email' => 'not-an-email',
            'message' => $this->faker->paragraph,
        ];

        $response = $this->postJson('/api/contact/submit', $data);

        $response->assertStatus(422)
            ->assertJsonValidationErrors('email');
    }

    /**
     * Test submission with short message.
     *
     * @return void
     */
    public function test_submit_endpoint_short_message()
    {
        $data = [
            'name' => $this->faker->name,
            'email' => $this->faker->safeEmail,
            'message' => 'short',
        ];

        $response = $this->postJson('/api/contact/submit', $data);

        $response->assertStatus(422)
            ->assertJsonValidationErrors('message');
    }
}
