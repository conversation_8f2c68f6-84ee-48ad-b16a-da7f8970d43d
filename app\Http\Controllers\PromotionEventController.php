<?php

namespace App\Http\Controllers;

use App\Models\PromotionEvent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Http\Requests\StorePromotionEventRequest;
use App\Http\Requests\UpdatePromotionEventRequest;

class PromotionEventController extends Controller
{
    /**
     * Afficher la liste des événements promotionnels
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = PromotionEvent::query();

        // Filtrage par statut actif
        if ($request->has('actif') && $request->boolean('actif') !== null) {
            $query->where('actif', $request->boolean('actif'));
        }

        // Filtrage par dates
        if ($request->has('actifs_seulement') && $request->boolean('actifs_seulement')) {
            $now = now()->startOfDay();
            $query->where('actif', true)
                ->where(function ($q) use ($now) {
                    $q->whereNull('date_debut')
                        ->orWhere('date_debut', '<=', $now);
                })
                ->where(function ($q) use ($now) {
                    $q->whereNull('date_fin')
                        ->orWhere('date_fin', '>=', $now);
                });
        }

        // Recherche par nom ou code
        if ($request->has('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('nom', 'like', "%{$search}%")
                    ->orWhere('code', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Tri
        $sortField = $request->input('sort', 'nom');
        $sortDirection = $request->input('direction', 'asc');

        $allowedSortFields = ['nom', 'code', 'date_debut', 'date_fin', 'created_at'];

        if (in_array($sortField, $allowedSortFields)) {
            $query->orderBy($sortField, $sortDirection === 'desc' ? 'desc' : 'asc');
        } else {
            $query->orderBy('nom', 'asc');
        }

        // Pagination
        $perPage = (int) $request->input('per_page', 15);
        $perPage = min(max(5, $perPage), 100); // Limiter entre 5 et 100

        $events = $query->paginate($perPage);

        return response()->json([
            'status' => 'success',
            'data' => $events
        ]);
    }

    /**
     * Créer un nouvel événement promotionnel
     *
     * @param StorePromotionEventRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(StorePromotionEventRequest $request)
    {
        $event = PromotionEvent::create($request->validated());

        return response()->json([
            'status' => 'success',
            'message' => 'Événement promotionnel créé avec succès',
            'data' => $event
        ], 201);
    }

    /**
     * Afficher un événement promotionnel spécifique
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $event = PromotionEvent::findOrFail($id);

        return response()->json([
            'status' => 'success',
            'data' => $event
        ]);
    }

    /**
     * Mettre à jour un événement promotionnel
     *
     * @param UpdatePromotionEventRequest $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(UpdatePromotionEventRequest $request, $id)
    {
        $event = PromotionEvent::findOrFail($id);
        $event->update($request->validated());

        return response()->json([
            'status' => 'success',
            'message' => 'Événement promotionnel mis à jour avec succès',
            'data' => $event
        ]);
    }

    /**
     * Supprimer un événement promotionnel
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $event = PromotionEvent::findOrFail($id);

        // Vérifier si l'événement a des promotions associées
        $promotionsCount = $event->promotions()->count();
        if ($promotionsCount > 0) {
            return response()->json([
                'status' => 'error',
                'message' => 'Impossible de supprimer cet événement car il est associé à ' . $promotionsCount . ' promotion(s)'
            ], 409);
        }

        $event->delete();

        return response()->json([
            'status' => 'success',
            'message' => 'Événement promotionnel supprimé avec succès'
        ]);
    }

    /**
     * Récupérer les promotions associées à un événement
     *
     * @param int $id
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPromotions($id, Request $request)
    {
        $event = PromotionEvent::findOrFail($id);

        $query = $event->promotions();

        // Filtrage par statut
        if ($request->has('statut')) {
            $query->where('statut', $request->input('statut'));
        }

        // Filtrage par dates
        if ($request->has('actives_seulement') && $request->boolean('actives_seulement')) {
            $now = now();
            $query->where('statut', 'active')
                ->where(function ($q) use ($now) {
                    $q->whereNull('date_debut')
                        ->orWhere('date_debut', '<=', $now);
                })
                ->where(function ($q) use ($now) {
                    $q->whereNull('date_fin')
                        ->orWhere('date_fin', '>=', $now);
                });
        }

        // Pagination
        $perPage = (int) $request->input('per_page', 15);
        $perPage = min(max(5, $perPage), 100); // Limiter entre 5 et 100

        $promotions = $query->paginate($perPage);

        return response()->json([
            'status' => 'success',
            'data' => [
                'event' => [
                    'id' => $event->id,
                    'nom' => $event->nom,
                    'code' => $event->code,
                    'actif' => $event->actif,
                    'date_debut' => $event->date_debut,
                    'date_fin' => $event->date_fin
                ],
                'promotions' => $promotions
            ]
        ]);
    }
}
