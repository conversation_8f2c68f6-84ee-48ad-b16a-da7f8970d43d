<?php

namespace App\Services;

use Illuminate\Http\JsonResponse;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class ApiResponseService
{
    /**
     * Return a successful response with data
     */
    public function success($data = null, string $message = 'Success', int $code = 200): JsonResponse
    {
        $response = [
            'status' => 'success',
            'message' => $message,
        ];

        if ($data !== null) {
            $response['data'] = $data;
        }

        return response()->json($response, $code);
    }

    /**
     * Return a paginated response
     */
    public function paginatedResponse(LengthAwarePaginator $data, string $message = 'Success'): JsonResponse
    {
        return response()->json([
            'status' => 'success',
            'message' => $message,
            'data' => $data->items(),
            'pagination' => [
                'current_page' => $data->currentPage(),
                'last_page' => $data->lastPage(),
                'per_page' => $data->perPage(),
                'total' => $data->total(),
                'from' => $data->firstItem(),
                'to' => $data->lastItem(),
                'has_more' => $data->hasMorePages(),
                'path' => $data->path(),
            ],
            'links' => [
                'first' => $data->url(1),
                'last' => $data->url($data->lastPage()),
                'prev' => $data->previousPageUrl(),
                'next' => $data->nextPageUrl(),
            ]
        ]);
    }

    /**
     * Return an error response
     */
    public function error(string $message, int $code = 400, array $details = [], array $errors = []): JsonResponse
    {
        $response = [
            'status' => 'error',
            'message' => $message,
        ];

        if (!empty($details)) {
            $response['details'] = $details;
        }

        if (!empty($errors)) {
            $response['errors'] = $errors;
        }

        return response()->json($response, $code);
    }

    /**
     * Return a validation error response
     */
    public function validationError(array $errors, string $message = 'Les données fournies ne sont pas valides.'): JsonResponse
    {
        return $this->error($message, 422, [], $errors);
    }

    /**
     * Return a not found error response
     */
    public function notFound(string $message = 'Ressource non trouvée.'): JsonResponse
    {
        return $this->error($message, 404);
    }

    /**
     * Return an unauthorized error response
     */
    public function unauthorized(string $message = 'Non autorisé.'): JsonResponse
    {
        return $this->error($message, 401);
    }

    /**
     * Return a forbidden error response
     */
    public function forbidden(string $message = 'Accès interdit.'): JsonResponse
    {
        return $this->error($message, 403);
    }

    /**
     * Return a server error response
     */
    public function serverError(string $message = 'Erreur interne du serveur.'): JsonResponse
    {
        return $this->error($message, 500);
    }

    /**
     * Return a created response
     */
    public function created($data = null, string $message = 'Créé avec succès.'): JsonResponse
    {
        return $this->success($data, $message, 201);
    }

    /**
     * Return a updated response
     */
    public function updated($data = null, string $message = 'Mis à jour avec succès.'): JsonResponse
    {
        return $this->success($data, $message, 200);
    }

    /**
     * Return a deleted response
     */
    public function deleted(string $message = 'Supprimé avec succès.'): JsonResponse
    {
        return $this->success(null, $message, 200);
    }

    /**
     * Return a response with custom data structure
     */
    public function customResponse(array $data, int $code = 200): JsonResponse
    {
        return response()->json($data, $code);
    }

    /**
     * Return a collection response with metadata
     */
    public function collection(Collection $data, string $message = 'Success', array $meta = []): JsonResponse
    {
        $response = [
            'status' => 'success',
            'message' => $message,
            'data' => $data->values(),
            'meta' => array_merge([
                'count' => $data->count(),
            ], $meta)
        ];

        return response()->json($response);
    }

    /**
     * Return a response indicating that an operation is being processed
     */
    public function processing(string $message = 'Traitement en cours...', array $details = []): JsonResponse
    {
        return response()->json([
            'status' => 'processing',
            'message' => $message,
            'details' => $details
        ], 202);
    }

    /**
     * Return a response for rate limiting
     */
    public function rateLimited(string $message = 'Trop de requêtes. Veuillez réessayer plus tard.'): JsonResponse
    {
        return $this->error($message, 429);
    }
}
