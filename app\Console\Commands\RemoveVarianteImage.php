<?php

namespace App\Console\Commands;

use App\Models\Image;
use App\Models\ProduitVariante;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class RemoveVarianteImage extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'images:remove-from-variante
                            {image_id : ID de l\'image à supprimer}
                            {--delete-file : Supprimer également le fichier du stockage}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Supprime l\'association d\'une image à une variante';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $imageId = $this->argument('image_id');
        $deleteFile = $this->option('delete-file');

        // Vérifier si l'image existe
        $image = Image::find($imageId);
        if (!$image) {
            $this->error("L'image avec l'ID {$imageId} n'existe pas.");
            return 1;
        }

        // Vérifier si l'image est associée à une variante
        if ($image->imageable_type !== ProduitVariante::class) {
            $this->error("Cette image n'est pas associée à une variante de produit.");
            return 1;
        }

        $variante = ProduitVariante::find($image->imageable_id);
        if (!$variante) {
            $this->error("La variante associée à cette image n'existe plus.");
            
            // Supprimer l'image quand même
            if ($this->confirm("Voulez-vous supprimer cette image orpheline?", true)) {
                $path = $image->path;
                $image->delete();
                $this->info("Image orpheline supprimée de la base de données.");
                
                if ($deleteFile && Storage::disk('s3')->exists($path)) {
                    Storage::disk('s3')->delete($path);
                    $this->info("Fichier supprimé du stockage: {$path}");
                }
                
                return 0;
            }
            
            return 1;
        }

        $this->info("Image trouvée: ID {$image->id}, {$image->path}");
        $this->info("Associée à la variante: {$variante->sku} (ID: {$variante->id})");

        // Demander confirmation
        if (!$this->confirm("Êtes-vous sûr de vouloir supprimer cette association?", false)) {
            $this->info("Opération annulée.");
            return 0;
        }

        // Vérifier si c'est l'image principale
        $isPrimary = $image->is_primary;
        $path = $image->path;

        // Supprimer l'image
        $image->delete();
        $this->info("Association d'image supprimée avec succès.");

        // Si c'était l'image principale, définir une autre image comme principale
        if ($isPrimary && $variante->images()->exists()) {
            $newPrimary = $variante->images()->first();
            $newPrimary->update(['is_primary' => true]);
            $this->info("Une nouvelle image principale a été définie: ID {$newPrimary->id}");
        }

        // Supprimer le fichier si demandé
        if ($deleteFile && Storage::disk('s3')->exists($path)) {
            Storage::disk('s3')->delete($path);
            $this->info("Fichier supprimé du stockage: {$path}");
        } elseif ($deleteFile) {
            $this->warn("Le fichier n'existe pas dans le stockage: {$path}");
        }

        return 0;
    }
}
