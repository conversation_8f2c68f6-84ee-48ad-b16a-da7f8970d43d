<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StorePromotionRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'nom' => 'required|string|max:255',
            'code' => 'nullable|string|max:50|unique:promotions',
            'description' => 'nullable|string',
            'type' => 'required|in:pourcentage,montant_fixe,gratuit',
            'valeur' => 'required|numeric|min:0',
            'statut' => 'required|in:active,inactive,programmée',
            'date_debut' => 'nullable|date',
            'date_fin' => 'nullable|date|after_or_equal:date_debut',
            'priorité' => 'nullable|integer',
            'cumulable' => 'nullable|boolean',
            'conditions' => 'nullable|array',
        ];
    }

    public function messages()
    {
        return [
            'nom.required' => 'Le nom de la promotion est obligatoire.',
            'code.unique' => 'Le code de promotion doit être unique.',
            'type.required' => 'Le type de promotion est obligatoire.',
            'type.in' => 'Le type de promotion est invalide.',
            'valeur.required' => 'La valeur de la promotion est obligatoire.',
            'valeur.numeric' => 'La valeur doit être un nombre.',
            'valeur.min' => 'La valeur doit être supérieure ou égale à 0.',
            'statut.required' => 'Le statut est obligatoire.',
            'statut.in' => 'Le statut est invalide.',
            'date_debut.date' => 'La date de début doit être une date valide.',
            'date_fin.date' => 'La date de fin doit être une date valide.',
            'date_fin.after_or_equal' => 'La date de fin doit être postérieure ou égale à la date de début.',
        ];
    }
} 