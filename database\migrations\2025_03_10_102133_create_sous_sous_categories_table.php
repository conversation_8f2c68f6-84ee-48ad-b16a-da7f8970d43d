<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sous_sous_categories', function (Blueprint $table) {
            $table->id();
            $table->string('nom_sous_sous_categorie');
            $table->string('description_sous_sous_categorie');
            $table->foreignId('sous_categorie_id')->constrained('sous_categories');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sous_sous_categories');
    }
};
