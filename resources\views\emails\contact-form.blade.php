<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nouveau message de contact - Lack parisien</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600&family=Inter:wght@300;400;500&display=swap');

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            line-height: 1.7;
            color: #5a4a3a;
            max-width: 600px;
            margin: 0 auto;
            padding: 0;
            background: linear-gradient(135deg, #f8f6f0 0%, #f1ede4 100%);
            font-weight: 300;
        }

        .container {
            background-color: #ffffff;
            margin: 20px;
            border-radius: 0;
            box-shadow: 0 8px 32px rgba(139, 117, 95, 0.12);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #d4c4a8 0%, #c8b896 100%);
            color: #5a4a3a;
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="0.5" fill="%23ffffff" opacity="0.1"/><circle cx="75" cy="75" r="0.3" fill="%23ffffff" opacity="0.08"/><circle cx="50" cy="10" r="0.4" fill="%23ffffff" opacity="0.06"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
            opacity: 0.3;
        }

        .header h1 {
            margin: 0;
            font-family: 'Playfair Display', serif;
            font-size: 28px;
            font-weight: 600;
            letter-spacing: -0.5px;
            position: relative;
            z-index: 1;
        }

        .header p {
            margin: 8px 0 0 0;
            font-size: 14px;
            opacity: 0.8;
            font-weight: 400;
            position: relative;
            z-index: 1;
        }

        .content {
            padding: 40px 30px;
        }

        .field {
            margin-bottom: 25px;
            padding: 20px;
            background: linear-gradient(135deg, #faf9f6 0%, #f5f3ee 100%);
            border-left: 3px solid #d4c4a8;
            border-radius: 2px;
        }

        .field-label {
            font-weight: 500;
            color: #8b7560;
            margin-bottom: 10px;
            text-transform: uppercase;
            font-size: 11px;
            letter-spacing: 1.2px;
        }

        .field-value {
            font-size: 16px;
            color: #5a4a3a;
            font-weight: 400;
        }

        .message-content {
            background-color: #ffffff;
            padding: 20px;
            border: 1px solid #f0ede6;
            border-radius: 2px;
            white-space: pre-wrap;
            word-wrap: break-word;
            color: #5a4a3a;
            line-height: 1.7;
            margin-top: 10px;
        }

        .footer {
            margin-top: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #faf9f6 0%, #f5f3ee 100%);
            text-align: center;
            color: #8b7560;
            font-size: 13px;
            line-height: 1.6;
            border-top: 1px solid #f0ede6;
        }

        .footer strong {
            color: #5a4a3a;
            font-weight: 500;
        }

        .timestamp {
            background: linear-gradient(135deg, #f0f8f0 0%, #e8f5e8 100%);
            padding: 15px 20px;
            border-radius: 2px;
            text-align: center;
            margin-bottom: 30px;
            color: #8b7560;
            font-size: 14px;
            border-left: 3px solid #d4c4a8;
            font-weight: 400;
        }

        .reply-info {
            background: linear-gradient(135deg, #fdf8e8 0%, #faf5e0 100%);
            padding: 25px;
            border-radius: 2px;
            margin-top: 30px;
            border-left: 3px solid #d4c4a8;
        }

        .reply-info strong {
            color: #8b7560;
            font-weight: 500;
        }

        .reply-info {
            color: #6b5b4b;
            line-height: 1.6;
        }

        /* Mobile responsiveness */
        @media only screen and (max-width: 600px) {
            .container {
                margin: 10px;
            }

            .header,
            .content {
                padding: 30px 20px;
            }

            .field,
            .reply-info {
                padding: 20px;
            }

            .header h1 {
                font-size: 24px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>Nouveau Message de Contact</h1>
            <p>Lack parisien - Message depuis le site web</p>
        </div>

        <div class="content">
            <div class="timestamp">
                Reçu le {{ $submittedAt }}
            </div>

            <div class="field">
                <div class="field-label">Nom du contact</div>
                <div class="field-value">{{ $name }}</div>
            </div>

            <div class="field">
                <div class="field-label">Adresse email</div>
                <div class="field-value">{{ $email }}</div>
            </div>

            <div class="field">
                <div class="field-label">Message</div>
                <div class="message-content">{{ $messageContent }}</div>
            </div>

            <div class="reply-info">
                <strong>Pour répondre :</strong> Vous pouvez répondre directement à cet email.
                L'adresse de réponse est automatiquement configurée sur {{ $email }}.
            </div>
        </div>

        <div class="footer">
            <p><strong>Lack parisien</strong> - L'art de vivre à la française</p>
            <p>Cet email a été généré automatiquement depuis le formulaire de contact du site.</p>
        </div>
    </div>
</body>

</html>