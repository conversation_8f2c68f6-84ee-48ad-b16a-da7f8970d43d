<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, add a guest_id column to identify guest carts
        Schema::table('paniers', function (Blueprint $table) {
            $table->uuid('guest_id')->nullable()->index()->after('client_id');
        });

        // Migrate existing session-based carts to use guest_id
        // We need to use a loop to handle the type conversion correctly
        $sessionCarts = DB::table('paniers')
            ->whereNotNull('session_id')
            ->whereNull('client_id')
            ->get();

        foreach ($sessionCarts as $cart) {
            // Generate a new UUID for each cart using PHP's built-in function
            $uuid = (string) \Illuminate\Support\Str::uuid();

            DB::table('paniers')
                ->where('id', $cart->id)
                ->update(['guest_id' => $uuid]);
        }

        // Make sure all carts have either client_id or guest_id
        $nullCarts = DB::table('paniers')
            ->whereNull('guest_id')
            ->whereNull('client_id')
            ->get();

        foreach ($nullCarts as $cart) {
            // Generate a new UUID for each cart using PHP's built-in function
            $uuid = (string) \Illuminate\Support\Str::uuid();

            DB::table('paniers')
                ->where('id', $cart->id)
                ->update(['guest_id' => $uuid]);
        }

        // Add a unique constraint to ensure each guest has only one cart
        Schema::table('paniers', function (Blueprint $table) {
            $table->unique('guest_id');
        });

        // Finally, drop the session_id column
        Schema::table('paniers', function (Blueprint $table) {
            $table->dropColumn('session_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Add back the session_id column
        Schema::table('paniers', function (Blueprint $table) {
            $table->string('session_id')->nullable()->index();
        });

        // Migrate guest_id values back to session_id
        // We need to use a loop to handle the type conversion correctly
        $guestCarts = DB::table('paniers')
            ->whereNotNull('guest_id')
            ->whereNull('client_id')
            ->get();

        foreach ($guestCarts as $cart) {
            // Convert UUID to string
            $sessionId = (string) $cart->guest_id;

            DB::table('paniers')
                ->where('id', $cart->id)
                ->update(['session_id' => $sessionId]);
        }

        // Drop the unique constraint and the guest_id column
        Schema::table('paniers', function (Blueprint $table) {
            $table->dropUnique(['guest_id']);
            $table->dropColumn('guest_id');
        });
    }
};
