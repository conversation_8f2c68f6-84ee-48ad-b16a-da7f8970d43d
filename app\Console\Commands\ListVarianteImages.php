<?php

namespace App\Console\Commands;

use App\Models\ProduitVariante;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class ListVarianteImages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'images:list-variantes
                            {--variante_id= : ID de la variante (optionnel)}
                            {--produit_id= : ID du produit parent (optionnel)}
                            {--sku= : SKU de la variante (optionnel)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Liste les images associées aux variantes de produits';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $varianteId = $this->option('variante_id');
        $produitId = $this->option('produit_id');
        $sku = $this->option('sku');

        // Construire la requête
        $query = ProduitVariante::query();

        if ($varianteId) {
            $query->where('id', $varianteId);
        }

        if ($produitId) {
            $query->where('produit_parent_id', $produitId);
        }

        if ($sku) {
            $query->where('sku', 'like', "%{$sku}%");
        }

        // Récupérer les variantes avec leurs images
        $variantes = $query->with('images')->get();

        if ($variantes->isEmpty()) {
            $this->error("Aucune variante trouvée avec les critères spécifiés.");
            return 1;
        }

        $this->info("Nombre de variantes trouvées: " . $variantes->count());

        // Afficher les informations pour chaque variante
        foreach ($variantes as $variante) {
            $this->newLine();
            $this->info("Variante: {$variante->sku} (ID: {$variante->id})");
            $this->info("Produit parent ID: {$variante->produit_parent_id}");
            
            if ($variante->images->isEmpty()) {
                $this->warn("  Aucune image associée à cette variante.");
                continue;
            }

            $this->info("  Images associées: " . $variante->images->count());
            
            // Afficher les détails de chaque image
            $headers = ['ID', 'Chemin', 'Principale', 'Ordre', 'URL'];
            $rows = [];
            
            foreach ($variante->images as $image) {
                $rows[] = [
                    $image->id,
                    $image->path,
                    $image->is_primary ? 'Oui' : 'Non',
                    $image->order,
                    Storage::disk($image->disk)->url($image->path)
                ];
            }
            
            $this->table($headers, $rows);
        }

        return 0;
    }
}
