<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('liste_souhait_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('liste_souhait_id')->constrained('liste_souhaits')->onDelete('cascade');
            $table->foreignId('produit_id')->constrained('produits')->onDelete('cascade');
            $table->foreignId('variante_id')->nullable()->constrained('produit_variantes')->nullOnDelete();
            $table->text('note')->nullable();
            $table->decimal('prix_reference', 10, 2)->nullable()->comment('Prix au moment de l\'ajout');
            $table->timestamps();

            // Indexes pour accélérer les recherches
            $table->index('liste_souhait_id');
            $table->index('produit_id');
            $table->index('variante_id');

            // Contrainte d'unicité pour éviter les doublons
            $table->unique(['liste_souhait_id', 'produit_id', 'variante_id'], 'unique_wishlist_item');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('liste_souhait_items');
    }
};
