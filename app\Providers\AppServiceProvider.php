<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\Keycloak\KeycloakTokenService;
use App\Services\Keycloak\KeycloakUserService;
use App\Services\Keycloak\KeycloakAdminService;
use App\Services\Keycloak\KeycloakRoleService;
use App\Services\KeycloakService;
use App\Services\ApiResponseService;
use App\Services\ProductCacheService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AppServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        // Register Keycloak services
        $this->app->singleton(KeycloakAdminService::class);
        $this->app->singleton(KeycloakTokenService::class);
        $this->app->singleton(KeycloakUserService::class);
        $this->app->singleton(KeycloakRoleService::class);

        $this->app->singleton(KeycloakService::class, function ($app) {
            return new KeycloakService(
                $app->make(KeycloakTokenService::class),
                $app->make(KeycloakUserService::class),
                $app->make(KeycloakAdminService::class),
                $app->make(KeycloakRoleService::class)
            );
        });

        // Register enhanced services
        $this->app->singleton(ApiResponseService::class);
        $this->app->singleton(ProductCacheService::class);
        $this->app->singleton(\App\Services\MonitoringService::class);
        $this->app->singleton(\App\Services\ErrorHandlingService::class);

        // Register new performance and security services
        $this->app->singleton(\App\Services\SimpleCacheService::class);
        $this->app->singleton(\App\Services\ValidationService::class);
        $this->app->singleton(\App\Services\EnhancedJwtSecurityService::class);
        $this->app->singleton(\App\Services\ProductService::class, function ($app) {
            return new \App\Services\ProductService(
                $app->make(\App\Services\ValidationService::class),
                $app->make(\App\Services\SimpleCacheService::class)
            );
        });

        // Set S3 configuration directly for Cloudflare R2
        config([
            'filesystems.default' => 's3',
            'filesystems.disks.s3.driver' => 's3',
            'filesystems.disks.s3.key' => '52c952a39c27df95506687a23c8e726f',
            'filesystems.disks.s3.secret' => 'cce2c2db833a07cd5429d88bb37a019a302b18cdf88578e20a14af2bd8c519fe',
            'filesystems.disks.s3.region' => 'auto',  // 'auto' est recommandé pour R2
            'filesystems.disks.s3.bucket' => 'jihenline-j1jom',
            'filesystems.disks.s3.url' => 'https://f6d1d15e6f0b37b4b8fcad3c41a7922d.r2.cloudflarestorage.com',
            'filesystems.disks.s3.endpoint' => 'https://f6d1d15e6f0b37b4b8fcad3c41a7922d.r2.cloudflarestorage.com',
            'filesystems.disks.s3.use_path_style_endpoint' => true,  // Important pour R2
            'filesystems.disks.s3.throw' => true,  // Pour déboguer les erreurs
            'filesystems.disks.s3.visibility' => 'public',  // Pour que les fichiers soient accessibles publiquement

        ]);
    }

    public function boot(): void
    {
        // Register model observers for cache invalidation
        \App\Models\Produit::observe(\App\Observers\ProduitObserver::class);
        \App\Models\Promotion::observe(\App\Observers\PromotionObserver::class);
        \App\Models\Categorie::observe(\App\Observers\CategorieObserver::class);

        // Database query monitoring in development and staging
        if (app()->environment(['local', 'staging'])) {
            DB::listen(function ($query) {
                // Log slow queries (>1 second)
                if ($query->time > 1000) {
                    Log::warning('Slow database query detected', [
                        'sql' => $query->sql,
                        'bindings' => $query->bindings,
                        'time_ms' => $query->time,
                    ]);
                }
            });

            // Enable query logging for debugging
            DB::enableQueryLog();
        }

        // Production monitoring for very slow queries only
        if (app()->environment('production')) {
            DB::listen(function ($query) {
                // Log extremely slow queries (>5 seconds) in production
                if ($query->time > 5000) {
                    Log::warning('Extremely slow database query detected', [
                        'sql' => $query->sql,
                        'bindings' => $query->bindings,
                        'time_ms' => $query->time,
                    ]);
                }
            });
        }

        // Additional boot logic can be added here
    }
}
