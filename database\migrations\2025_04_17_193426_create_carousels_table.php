<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('carousels', function (Blueprint $table) {
            $table->id();
            $table->string('nom')->comment('Nom du carousel');
            $table->text('description')->nullable()->comment('Description du carousel');
            $table->boolean('actif')->default(true)->comment('Statut actif du carousel');
            $table->integer('ordre')->default(0)->comment('Ordre d\'affichage');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('carousels');
    }
};
