<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Exception;

class EnhancedJwtSecurityService
{
    private const TOKEN_BLACKLIST_PREFIX = 'blacklisted_token:';
    private const FAILED_ATTEMPTS_PREFIX = 'jwt_failed_attempts:';
    private const MAX_FAILED_ATTEMPTS = 5;
    private const LOCKOUT_DURATION = 900; // 15 minutes

    /**
     * Check if a token is blacklisted
     */
    public function isTokenBlacklisted(string $token): bool
    {
        $tokenHash = hash('sha256', $token);
        return Cache::has(self::TOKEN_BLACKLIST_PREFIX . $tokenHash);
    }

    /**
     * Blacklist a token
     */
    public function blacklistToken(string $token, int $ttl = 3600): void
    {
        $tokenHash = hash('sha256', $token);
        Cache::put(self::TOKEN_BLACKLIST_PREFIX . $tokenHash, true, $ttl);

        Log::info('Token blacklisted', [
            'token_hash' => $tokenHash,
            'ttl' => $ttl,
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);
    }

    /**
     * Record a failed JWT validation attempt
     */
    public function recordFailedAttempt(string $identifier): void
    {
        $key = self::FAILED_ATTEMPTS_PREFIX . $identifier;
        $attempts = Cache::get($key, 0) + 1;

        Cache::put($key, $attempts, self::LOCKOUT_DURATION);

        Log::warning('JWT validation failed', [
            'identifier' => $identifier,
            'attempts' => $attempts,
            'max_attempts' => self::MAX_FAILED_ATTEMPTS,
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);

        if ($attempts >= self::MAX_FAILED_ATTEMPTS) {
            $this->lockoutIdentifier($identifier);
        }
    }

    /**
     * Check if an identifier is locked out
     */
    public function isLockedOut(string $identifier): bool
    {
        $attempts = Cache::get(self::FAILED_ATTEMPTS_PREFIX . $identifier, 0);
        return $attempts >= self::MAX_FAILED_ATTEMPTS;
    }

    /**
     * Lock out an identifier
     */
    private function lockoutIdentifier(string $identifier): void
    {
        Cache::put('lockout:' . $identifier, true, self::LOCKOUT_DURATION);

        Log::alert('Identifier locked out due to repeated JWT failures', [
            'identifier' => $identifier,
            'lockout_duration' => self::LOCKOUT_DURATION,
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);
    }

    /**
     * Clear failed attempts for an identifier
     */
    public function clearFailedAttempts(string $identifier): void
    {
        Cache::forget(self::FAILED_ATTEMPTS_PREFIX . $identifier);
        Cache::forget('lockout:' . $identifier);
    }

    /**
     * Validate token claims with enhanced security checks
     */
    public function validateTokenClaims(object $decoded): void
    {
        // Check required claims
        $requiredClaims = ['sub', 'iss', 'exp', 'iat'];
        foreach ($requiredClaims as $claim) {
            if (!isset($decoded->$claim)) {
                throw new Exception("Missing required claim: {$claim}");
            }
        }

        // Validate issued at time (iat) - token shouldn't be from future
        if (isset($decoded->iat) && $decoded->iat > time() + 60) { // Allow 60 seconds clock skew
            throw new Exception('Token issued in the future');
        }

        // Validate not before (nbf) if present
        if (isset($decoded->nbf) && $decoded->nbf > time() + 60) {
            throw new Exception('Token not yet valid');
        }

        // Additional audience validation
        /*
        if (isset($decoded->aud)) {
            $expectedAudience = config('services.keycloak.client_id');
            $audiences = is_array($decoded->aud) ? $decoded->aud : [$decoded->aud];

            if (!in_array($expectedAudience, $audiences)) {
                throw new Exception('Invalid token audience');
            }
        }
        */
    }

    /**
     * Validate token expiry with buffer
     */
    public function validateTokenExpiry(object $decoded): void
    {
        if (!isset($decoded->exp)) {
            throw new Exception('Token missing expiration claim');
        }

        $buffer = 30; // 30 seconds buffer
        if (time() >= ($decoded->exp - $buffer)) {
            throw new Exception('Token expired or expiring soon');
        }
    }

    /**
     * Enhanced token validation with security monitoring
     */
    public function enhancedValidateToken(string $token, callable $baseValidator): object
    {
        $identifier = $this->getValidationIdentifier();

        // Check if identifier is locked out
        if ($this->isLockedOut($identifier)) {
            throw new Exception('Too many failed attempts. Please try again later.');
        }

        // Check if token is blacklisted
        if ($this->isTokenBlacklisted($token)) {
            $this->recordFailedAttempt($identifier);
            throw new Exception('Token has been revoked');
        }

        try {
            // Call the base token validator
            $decoded = $baseValidator($token);

            // Perform additional claim validation
            $this->validateTokenClaims($decoded);
            $this->validateTokenExpiry($decoded);

            // Clear failed attempts on successful validation
            $this->clearFailedAttempts($identifier);

            // Log successful validation
            Log::info('Token validated successfully', [
                'user_id' => $decoded->sub,
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'token_hash' => substr(hash('sha256', $token), 0, 16) // Partial hash for logging
            ]);

            return $decoded;

        } catch (Exception $e) {
            $this->recordFailedAttempt($identifier);

            Log::warning('Enhanced token validation failed', [
                'error' => $e->getMessage(),
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'token_hash' => substr(hash('sha256', $token), 0, 16)
            ]);

            throw $e;
        }
    }

    /**
     * Get validation identifier for failed attempt tracking
     */
    private function getValidationIdentifier(): string
    {
        $ip = request()->ip();
        $userAgent = request()->userAgent();

        return md5($ip . '|' . $userAgent);
    }

    /**
     * Batch blacklist multiple tokens (useful for logout all devices)
     */
    public function blacklistTokens(array $tokens, int $ttl = 3600): void
    {
        foreach ($tokens as $token) {
            $this->blacklistToken($token, $ttl);
        }
    }

    /**
     * Get failed attempt statistics for monitoring
     */
    public function getFailedAttemptStats(): array
    {
        $identifier = $this->getValidationIdentifier();
        $attempts = Cache::get(self::FAILED_ATTEMPTS_PREFIX . $identifier, 0);
        $isLockedOut = $this->isLockedOut($identifier);

        return [
            'identifier' => $identifier,
            'failed_attempts' => $attempts,
            'max_attempts' => self::MAX_FAILED_ATTEMPTS,
            'is_locked_out' => $isLockedOut,
            'lockout_duration' => self::LOCKOUT_DURATION
        ];
    }
}
