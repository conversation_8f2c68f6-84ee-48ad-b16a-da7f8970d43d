<?php

namespace App\Services\Keycloak;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;

class KeycloakAdminService
{
    private const ADMIN_TOKEN_CACHE_KEY = 'keycloak_admin_token';
    private const ADMIN_TOKEN_CACHE_TTL = 300; // 5 minutes

    public function getAdminToken(): string
    {
        return Cache::remember(self::ADMIN_TOKEN_CACHE_KEY, self::ADMIN_TOKEN_CACHE_TTL, function () {
            $response = Http::asForm()->post(
                $this->getAdminTokenUrl(),
                [
                    'grant_type' => 'client_credentials',
                    'client_id' => config('services.keycloak.admin_client_id'),
                    'client_secret' => config('services.keycloak.admin_client_secret'),
                ]
            );

            if (!$response->successful()) {
                throw new Exception('Failed to get admin token from Keycloak: ' . $response->body());
            }

            return $response->json()['access_token'];
        });
    }

    private function getAdminTokenUrl(): string
    {
        return sprintf(
            '%s/realms/master/protocol/openid-connect/token',
            config('services.keycloak.base_url')
        );
    }

    // ... other admin-related methods
}
